@keyframes shine-node {
  0% {
    fill: #006ef9; /* 蓝色 */
  }
  
  16.67% {
    fill: #FF7200; /* 黄色 */
  }
  
  33.33% {
    fill: #006ef9; /* 蓝色 */
  }
  
  50% {
    fill: #FF7200; /* 黄色 */
  }
  
  66.67% {
    fill: #006ef9; /* 蓝色 */
  }
  
  83.33% {
    fill: #FF7200; /* 黄色 */
  }
  
  100% {
    fill: #FF7200; /* 最终稳定为黄色 */
  }
}

.shine-node {
  .base-stroke {
    animation: shine-node 3s ease-in-out 1 forwards;
  }
  .base-fill {
    animation: shine-node 3s ease-in-out 1 forwards;
  }
}

.color-node {
  .base-stroke {
    fill: #FF7200;
  }
  .base-fill {
    fill: #FF7200;
  }
}

.chart-tooltip {
  overflow: auto;
  max-height: 250px;
}