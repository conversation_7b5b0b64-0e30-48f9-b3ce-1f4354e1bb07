/* eslint-disable no-nested-ternary */
import React from 'react';
import {
  setAppId,
  setArchInfo,
  setAppIdInfo,
  setUnSupportedProducts,
  updateNodeIcon,
  setProductDict,
  setSupportedProducts,
} from '@src/utils/caching';
import {
  GetAccountInfoByFields,
  DescribeProductConfigList,
} from '@src/service/api/baseInfo';
import {
  setCurrentNodeAction,
  setGraphDataAction,
  setDrawerVisibleMapAction,
} from '@src/origin-store/guardAction';
import { EENDTYPE } from '@src/constants';
import { getSearchParam } from '@src/utils/common';
import { hasResourcesBind } from '@src/utils/guard-node';
import GuardRoot from '@src/pages/guard-root';
import store from './origin-store/store';
import { nanoid } from 'nanoid';

const Init = (api: AppPluginAPI.PluginAPI) => {
  const type = {
    CONSOLE: EENDTYPE.CUSTOMER,
    ISA: EENDTYPE.OPERATOR,
  }[api.env];

  // 生成当前窗口的sessionId
  window.sessionStorage.setItem('SessionId', nanoid());
  // 缓存架构图信息
  setArchInfo(api);
  store.dispatch(setGraphDataAction(api));
  // 缓存架构图节点图标
  updateNodeIcon(api?.shapeCategoryList || []);
  // 从url中获取appId
  const AppId = getSearchParam('appid', location);
  // 缓存appId
  setAppId(AppId);

  // 缓存appId信息
  GetAccountInfoByFields()
    .then((res: any) => {
      setAppIdInfo(res);
    })
    .catch((err) => {
      console.log(err);
    });

  // 缓存兜底产品列表
  DescribeProductConfigList({ Env: 'all', TaskType: 'guardTaskType' })
    .then((res: any) => {
      setProductDict(res.ProductDict ? JSON.parse(res.ProductDict) : []);
      setSupportedProducts(res.Products || []);
      setUnSupportedProducts(res.UnSupportedProducts || []);
    })
    .catch((err) => {
      console.log(err);
    });

  api.setSlotComponent(<GuardRoot />);
  api.initOver();

  return {
    onShapeClick: ({ node }) => {
      // 关闭所有工具栏抽屉，打开对应节点抽屉
      api.closeDrawer();
      const isCustomer = type === EENDTYPE.CUSTOMER;
      const isOperator = type === EENDTYPE.OPERATOR;
      const hasBind = hasResourcesBind(node);
      // 运营端抽屉
      if (isOperator) {
        if (hasBind) {
          store.dispatch(setCurrentNodeAction(node));
          store.dispatch(setDrawerVisibleMapAction({ nodeDrawerVisible: true }));
        } else {
          store.dispatch(setDrawerVisibleMapAction({ nodeDrawerVisible: false }));
        }
      }
      // 租户端抽屉
      if (isCustomer) {
        if (hasBind) {
          store.dispatch(setCurrentNodeAction(node));
          store.dispatch(setDrawerVisibleMapAction({ nodeCustomerDrawerVisible: true }));
        } else {
          store.dispatch(setDrawerVisibleMapAction({ nodeCustomerDrawerVisible: false }));
        }
      }
    },
    onDocClick: () => {
      // 点击画布，关闭全部抽屉
      api.closeDrawer();
      store.dispatch(setDrawerVisibleMapAction({}));
    },
  };
};

export default Init;
