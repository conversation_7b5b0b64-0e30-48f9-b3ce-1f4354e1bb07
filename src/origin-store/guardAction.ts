import {
  SET_CURRENT_NODE,
  SET_DRAWER_VISIBLE_MAP,
  SET_NODE_CUSTOMER_DRAWER_RANGE_TIME,
  SET_GRAPH_DATA,
  RESET_STORE,
  SET_NODE_CUSTOMER_DRAWER_METRICS,
  SET_NODE_CUSTOMER_DRAWER_CURRENT_METRICS,
  SET_HISTORY_GUARD,
  SET_GUARD_UPDATE_FLAG,
  SET_NODE_UPDATE_FLAG,
  SET_BROADCAST_LINK_ID,
  SET_AGENT_DRAWER_VISIBLE,
  SET_DASHBOARD_DRAWER_VISIBLE,
  SET_DASHBOARD_ID,
  SET_SCROLL_GRAHP_ID,
} from './constants';

export const resetStoreAction = data => ({
  type: RESET_STORE,
  payload: data,
});

export const setCurrentNodeAction = data => ({
  type: SET_CURRENT_NODE,
  payload: data,
});

export const setDrawerVisibleMapAction = data => ({
  type: SET_DRAWER_VISIBLE_MAP,
  payload: data,
});

export const setNodeCustomerDrawerRangeTimeAction = data => ({
  type: SET_NODE_CUSTOMER_DRAWER_RANGE_TIME,
  payload: data,
});

export const setGraphDataAction = data => ({
  type: SET_GRAPH_DATA,
  payload: data,
});

export const setDrawerMetrics = data => ({
  type: SET_NODE_CUSTOMER_DRAWER_METRICS,
  payload: data,
});

export const setCurDrawerMetrics = data => ({
  type: SET_NODE_CUSTOMER_DRAWER_CURRENT_METRICS,
  payload: data,
});

export const setHistoryGuardAction = data => ({
  type: SET_HISTORY_GUARD,
  payload: data,
});

export const setGuardUpdateFlagAction = data => ({
  type: SET_GUARD_UPDATE_FLAG,
  payload: data,
});

export const setNodeUpdateFlagAction = data => ({
  type: SET_NODE_UPDATE_FLAG,
  payload: data,
});

export const setBroadcastLinkIdAction = data => ({
  type: SET_BROADCAST_LINK_ID,
  payload: data,
});

export const setAgentDrawerVisible = data => ({
  type: SET_AGENT_DRAWER_VISIBLE,
  payload: data,
});

export const setDashboardDrawerVisible = data => ({
  type: SET_DASHBOARD_DRAWER_VISIBLE,
  payload: data,
});

export const setDashboardId = data => ({
  type: SET_DASHBOARD_ID,
  payload: data,
});

export const setScrollGraphId = data => ({
  type: SET_SCROLL_GRAHP_ID,
  payload: data,
});


