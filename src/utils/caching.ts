import { DescribeOtherPlatformGuardSheet, ReportGuardEditCost } from '@src/service/api/baseInfo';
import { getStatu } from '@src/utils/common';

// 架构信息
export let archInfo: any = {};
export const setArchInfo = (info) => {
  archInfo = info;
};

// 用户appId
export let appId = '';
export const setAppId = (info) => {
  appId = info;
};

// 用户appId授权、转售信息
export let appIdInfo: any = {};
export const setAppIdInfo = (info) => {
  appIdInfo = info;
};

// policy
export let policy: any = {};
export const setPolicy = (info) => {
  policy = info;
};

// 架构图全量节点
export let allNodeList: any = {};
export const setAllNodeList = (info) => {
  allNodeList = info;
};

// 架构图资源
export let nodeList: any = [];
export const setNodeList = (info) => {
  nodeList = info;
};

// 产品中文名映射
export let productDict = [];
export const setProductDict = (info) => {
  productDict = info;
};

// 接入云护航的产品
export let supportedProducts = [];
export const setSupportedProducts = (info) => {
  supportedProducts = info;
};

// 未接入云护航的产品
export let unSupportedProducts = [];
export const setUnSupportedProducts = (info) => {
  unSupportedProducts = info;
};

// 未接入云护航的产品选项
export let otherProductNode = [];
export const setOtherProductNode = (info) => {
  otherProductNode = info;
};

// 架构图节点图标
export let nodeIcon: any = {};
// 更新护航单信息
export function updateNodeIcon(shapeCategoryList) {
  const list = shapeCategoryList.reduce((result, item) => {
    item.shapes.forEach((shape) => {
      result[shape.name] = shape.d3Icon.replace(/width="\d+"/, 'width="64"').replace(/height="\d+"/, 'height="64"');
    });
    return result;
  }, {});
  nodeIcon = list;
}

// 护航单信息
export let guardInfo: any = {};
export const setGuardInfo = (info) => {
  guardInfo = info;
};

// 更新护航单信息
export function updateGuardInfo(archId = archInfo?.archInfo?.archId) {
  DescribeOtherPlatformGuardSheet({ Filters: [{ Name: 'platform_unique_id', Values: [archId] }] })
    .then((res: any) => {
      // 最后一条护航单
      const guard = res.Guard?.length ? res.Guard[0] : {};
      // 如果最后一条已经护航完成，则不缓存
      setGuardInfo(getStatu(guard) === 'finish' ? {} : guard);
    })
    .catch((err) => {
      console.log(err);
    });
}

/**
 * @description 事件上报
 * @param 发起护航：0，保存护航单（上一步、下一步、保存）：1，提交护航单：2
 */
export function reportGuard(Event = 0) {
  // 公共参数
  const commonParams = {
    ArchId: archInfo.archInfo?.archId,
    GuardId: guardInfo.GuardId,
    UserName: archInfo.userName,
    Event,
    SessionId: window.sessionStorage.getItem('SessionId') || '',
  };
  // 运营端参数
  const IsaParams = {
    AppId: parseInt(appId, 10),
    Uin: archInfo.uin,
  };
  // 区分租户端和运营端传参
  const params = archInfo.env === 'ISA' ? { ...commonParams, ...IsaParams } : commonParams;
  ReportGuardEditCost(params)
    .then(() => {})
    .catch((err) => {
      console.log(err);
    });
}
