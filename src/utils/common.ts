import { map, reduce, toPairs } from 'lodash';
import { lng, t } from '@tea/app/i18n';
import { message } from '@tencent/tea-component';
import moment from 'moment';
import { DescribeOtherPlatformGuardSheet } from '@src/service/api/baseInfo';
// 每一次保存/查询实例的限制
export const Limit = 1000;

// 根据护航单id获取最新的护航单信息
export async function getGuardById(guardId = 0) {
  try {
    const res: any = await DescribeOtherPlatformGuardSheet({ Filters: [{ Name: 'guard_id', Values: [`${guardId}`] }] });
    if (res?.Error) {
      return null;
    }
    return res?.Guard?.[0] || null;
  } catch (err) {
    const msg = err.msg || err.toString() || t('未知错误');
    console.log({ content: msg });
  }
};

// 把建议中的markdown形式的超链接转换为html里的target为_blank的a标签
export function simpleMarkdownToHTML(input = ''): string {
  const reg = new RegExp(
    // eslint-disable-next-line no-useless-escape
    t('(\[[一-龥_a-zA-Z0-9,、/-\s]+\])(\((\s?https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]\))'),
    'g'
  );
  let result = input.replace(reg, (match, text, link) => `<a href="${link.slice(1, -1)}" target="_blank">${text.slice(1, -1)}</a>`);

  // 无序列表markdown语法转换
  if (result.includes('- ')) {
    result = result
      .split('- ')
      .filter(r => r.length)
      .map((r, index) => (!index ? `<div>• ${r}</div>` : `<div style="margin-top: 8px;">• ${r}</div>`))
      .join('');
  }
  return result;
};

// 构造树形结构
export function buildNestedArray(arr, idProp, parentIdProp) {
  const nestedArray = [];
  const map = arr.reduce((acc, item) => {
    acc[item[idProp]] = { ...item };
    return acc;
  }, {});
  for (const item of arr) {
    if (item[parentIdProp] in map) {
      const parent = map[item[parentIdProp]];
      parent.children = parent.children || [];
      parent.children.push(map[item[idProp]]);
    } else {
      nestedArray.push(map[item[idProp]]);
    }
  }
  return nestedArray;
}

export function getLanguageParam() {
  return lng === 'zh' ? 'zh-CN' : 'en-US';
}

export function getSearchParam(name, location) {
  const { search } = location;
  const nameArr = search.split(`${name}=`);
  if (nameArr.length === 2) {
    const searchParamArr = nameArr[1].split('&');
    return searchParamArr[0];
  }
  return null;
}

export function getUrlParamFromLocation(nameList, location) {
  const retObj = {};
  map(nameList, (item) => {
    retObj[item] = getSearchParam(item, location);
  });
  return retObj;
}

export function getUrlParamsStr(paramObj) {
  return reduce(toPairs(paramObj), (ret, [key, value]) => {
    ret.push(`${key}=${value}`);
    return ret;
  }, []).join('&');
}

export const queryStringObj = (str) => {
  if (!str) return {};
  const arrUrl = str?.split('?')[1].split('&');
  const objUrl = {};
  for (let m = 0; m < arrUrl.length; m++) {
    objUrl[arrUrl[m].split('=')[0]] = arrUrl[m].split('=')[1];
  }
  return objUrl;
};

// 护航单状态映射
export const StatusDict = new Map([
  [1, t('草稿')],
  [2, t('订单已提交')],
  [31, t('售后审批（TAM）')],
  [32, t('正在巡检中')],
  [33, t('专项人员分配（专项接口人）')],
  [34, t('巡检结果审批（专项Owner）')],
  [36, t('巡检报告生成中')],
  [37, t('巡检报告已完成')],
  [40, t('实例变更中')],
  [41, t('实例变更巡检中')],
  [48, t('巡检风险审批（护航负责人）')],
  [50, t('护航巡检已完成')],
  [-1, t('流程中止')],
  [-2, t('巡检任务异常')],
  [-3, t('审核任务异常')],
]);

// 流程状态ID
export const processState = {
  onNoteId: 1,              // 草稿状态ID
  submitId: 2,              // 订单已提交状态ID
  onSaleApprovalId: 31,     // 售后审批状态ID
  onRunningId: 32,          // 正在巡检中状态ID
  onExpertApprovalId: 33,   // 专项分配人员状态ID
  onResultApprovalId: 34,   // 巡检结果审核状态ID
  onReportGenerating: 36,   // 巡检报告生成中
  onReportGenerated: 37,    // 巡检报告已完成
  instanceAltering: 40,     // 实例修改中ID
  instanceAlteredRun: 41,   // 实例修改后运行中ID
  tamCheckResultId: 48,     // 巡检风险审批（TAM）
  onFinishId: 50,           // 护航巡检完成状态ID
  processStopId: -1,        // 流程中止状态ID
  scanFailedId: -2,         // 巡检异常状态ID
  approvalFailedId: -3,     // 审核异常状态ID
  deletedId: -50,           // 已删除
};

// 判断当前护航状态: 护航前准备、护航中、护航结束
export function getStatu(guard) {
  if (guard.GuardId) {
    // 护航开始、结束时间
    const { StartTime, EndTime } = guard;
    const today = moment();
    const start = moment(StartTime, 'YYYY-MM-DD HH:mm:ss');
    const end = moment(EndTime, 'YYYY-MM-DD HH:mm:ss');
    if (today.isBefore(start)) {
      return 'prepare';
    } if (today.isBetween(start, end)) {
      return 'running';
    }
    return 'finish';
  }
  return 'prepare';
}

// 根据路径去找值
export function findValueByPath(obj, path) {
  try {
    const value = path.split('.').reduce((acc, key) => acc?.[key], obj);
    // 如果值是数组，返回逗号拼接的字符串.如果值是字符串，返回本身
    return Array.isArray(value) ? value.join(',') : value;
  } catch (error) {
    return '--'; // 如果发生错误，返回--
  }
}
// 根据路径去修改值
export function updateNestedField(obj, fieldPath, newValue) {
  const parts = fieldPath.split('.');
  let currentObj = obj;

  for (let i = 0; i < parts.length - 1; i++) {
    if (!currentObj[parts[i]]) {
      currentObj[parts[i]] = {};
    }
    currentObj = currentObj[parts[i]];
  }
  currentObj[parts[parts.length - 1]] = newValue;
}

const ENV_INFO = {
  'isa.woa.com': { name: t('正式环境'), value: 'production' },
  'isa-pre.woa.com': { name: t('预发布环境'), value: 'pre' },
  'isa-test.woa.com': { name: t('测试环境'), value: 'test' },
  'isa-intl.woa.com': { name: t('海外正式环境'), value: 'production-abroad' },
};

export const getProcessEnv = () => {
  const { hostname } = location;
  return ENV_INFO[hostname]?.value || 'others';
};

// 如果url有fromBusiness，就将数据上报到野鹤平台
export function dataReport(api?: any, guardId = '') {
  const fromBusiness = getSearchParam('fromBusiness', location);
  if (fromBusiness && api?.pluginReportV2) {
    api.pluginReportV2({
      event: 'CloudEscortPluginLinkClick',
      stringFields: {
        url: location.href,
        guardId,
        clickTime: moment().format('YYYY-MM-DD HH:mm:ss'),
      },
    });
  }
}

// eslint-disable-next-line import/prefer-default-export, no-promise-executor-return
export const sleep = (delay: number) => new Promise(resolve => setTimeout(resolve, delay));

// 文字复制到剪切板
export const copyTextToClipboard = (text: string) => {
  // 使用 Clipboard API
  navigator.clipboard
    .writeText(text)
    .then(() => {
      message.success({ content: t('复制成功') });
    })
    .catch(() => {
      message.error({ content: t('复制失败') });
    });
};

export const getPopupContainer = (): HTMLElement => document.querySelector('#micro-frontend-root div[data-qiankun="isa-cloud-arch"]',) || document.body;

export const portalContainer: any = document.querySelector('#micro-frontend-root div[data-qiankun="isa-cloud-arch"]') || document.querySelector('#advisor-index main') || document.body;

// 计算字符串相似度（使用编辑距离算法）
export const calculateSimilarity = (str1: string, str2: string): number => {
  if (!str1 || !str2) return 0;

  const len1 = str1.length;
  const len2 = str2.length;

  if (len1 === 0) return len2 === 0 ? 100 : 0;
  if (len2 === 0) return 0;

  const matrix = Array(len1 + 1).fill(null)
    .map(() => Array(len2 + 1).fill(null));

  for (let i = 0; i <= len1; i++) {
    matrix[i][0] = i;
  }

  for (let j = 0; j <= len2; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      );
    }
  }

  const maxLen = Math.max(len1, len2);
  const similarity = ((maxLen - matrix[len1][len2]) / maxLen) * 100;
  return similarity;
};
