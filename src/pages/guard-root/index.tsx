import React, { useEffect, useRef } from 'react';
import { Provider } from 'react-redux';
import GuardNodeDrawer from '@src/components/guard-node-drawer';
import GuardNodeCustomerDrawer from '@src/components/guard-node-customer-drawer';
import originStore from '@src/origin-store/store';
import { store } from '@src/store';
import HistoryDrawer from '@src/pages/history-drawer';
import InfoChangeDrawer from '@src/pages/info-change-drawer';
import BroadcastDrawer from '@src/components/broadcast-drawer';
import CloudResourceDrawer from '@src/components/cloud-resource-drawer';
import OperatorDuckMenu from '../operator-duck-menu';
import CustomerDuckMenu from '../customer-duck-menu';
import GuardAgentChat from '../guard-agent-chat';
import MainContent from '../main-content';
import { SDKLoader } from '@tencent/tea-app';
import { setDashboardDrawerVisible, setScrollGraphId } from '@src/origin-store/guardAction';

// 根节点
const GuardRoot = () => {
  const [, setState] = React.useState({});
  const {
    drawerVisibleMap,
    currNode,
    graphApi,
    agentDrawerVisible,
    dashboardDrawerVisible,
    dashboardId,
    scrollGraghId,
  }  = originStore.getState().guard;
  // 抽屉ref
  const dashboardRef = useRef(null);

  const isISA = graphApi.env === 'ISA';

  useEffect(() => {
    originStore.subscribe(() => {
      setState({});
    });
  }, []);

  useEffect(() => {
    if (dashboardDrawerVisible) {
      setTimeout(() => {
        dashboardRef?.current?.scrollTo(scrollGraghId);;
      }, 1500);
    }
  }, [dashboardDrawerVisible, scrollGraghId]);

  return <Provider store={store}>
    <MainContent />
    {
      isISA
        ? <OperatorDuckMenu />
        : <CustomerDuckMenu/>
    }
    {
      drawerVisibleMap.nodeDrawerVisible
      && <GuardNodeDrawer />
    }
    {
      drawerVisibleMap.nodeCustomerDrawerVisible
      && <GuardNodeCustomerDrawer key={currNode.key} />
    }
    {/* 历史项目抽屉 */}
    {drawerVisibleMap.historyDrawerVisible && <HistoryDrawer />}

      {/* 基本信息变更抽屉 */}
    {drawerVisibleMap.infoChangeDrawerVisible && <InfoChangeDrawer />}

    {/* 播报策略抽屉 */}
    {drawerVisibleMap.broadcastVisible && <BroadcastDrawer />}

    {/* 售后确认架构图抽屉 */}
    {drawerVisibleMap.cloudResourceVisible && <CloudResourceDrawer />}

    <GuardAgentChat visible={agentDrawerVisible} />

    <SDKLoader sdkNames={['ai-bi-dashboard-sdk']}>
      {([sdk]) => (
        <sdk.reactNode.DashboardDrawer
          ref={dashboardRef}
          dashboardId={ dashboardId }
          apis={graphApi}
          onClose={() => {
            originStore.dispatch(setDashboardDrawerVisible(false));
            originStore.dispatch(setScrollGraphId(''));
          }}
          open={dashboardDrawerVisible}
          width={420}
        />
      )}
    </SDKLoader>
  </Provider>;
};
export default GuardRoot;
