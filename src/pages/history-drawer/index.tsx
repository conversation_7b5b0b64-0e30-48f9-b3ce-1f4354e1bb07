/* eslint-disable no-nested-ternary */
import React from 'react';
import { t } from '@tea/app/i18n';
import {
  Drawer,
} from '@tencent/tea-component';
import store from '@src/origin-store/store';
import { setDrawerVisibleMapAction, setHistoryGuardAction } from '@src/origin-store/guardAction';
import './index.less';
import Confirm from '@src/pages/escortDrawer/components/confirm';

/** Root
 * @param props
 * @returns React.ReactElement
 */
export default function HistoryDrawer(): React.ReactElement {
  return (
    <>
      <Drawer
        style={{ width: 700 }}
        visible
        title={ t('历史护航') }
        destroyOnClose
        className='escortDrawerWrap'
        outerClickClosable={false}
        onClose={() => {
          // 修改弹框标志
          store.dispatch(setDrawerVisibleMapAction({ historyDrawerVisible: false }));
          // 护航历史项目置空
          store.dispatch(setHistoryGuardAction({}));
        }}
      >
       <Confirm />
      </Drawer>

    </>
  );
}
