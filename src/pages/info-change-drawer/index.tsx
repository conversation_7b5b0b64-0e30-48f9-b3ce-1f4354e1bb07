/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useRef } from 'react';
import { t } from '@tea/app/i18n';
import {
  Drawer,
  Button,
} from '@tencent/tea-component';
import store from '@src/origin-store/store';
import { setDrawerVisibleMapAction } from '@src/origin-store/guardAction';
import './index.less';
import BaseInfo from '@src/pages/escortDrawer/components/baseInfo';
import { DescribeGuardBaseConfig } from '@src/service/api/baseInfo';
import { guardInfo } from '@src/utils/caching';


/** Root
 * @param props
 * @returns React.ReactElement
 */
export default function InfoChangeDrawer(): React.ReactElement {
  const baseinfoRef = useRef(null);
  // 当前护航单可修改字段列表
  const [editList, setEditList] = useState([]);

  useEffect(() => {
    getDescribeGuardBaseConfig();
  }, []);

  // 查询护航可编辑的信息
  function getDescribeGuardBaseConfig() {
    DescribeGuardBaseConfig({
      AppId: 1253985742, // 接口必须传appid
      GuardId: Number(guardInfo.GuardId),
    }).then((res: any) => {
      setEditList((res.GuardBaseConfig || []).filter(i => i.IsSupportModify).map(i => i.Field));
    })
      .catch((err) => {
        console.log(err);
      });
  };
  return (
    <>
      <Drawer
        style={{ width: 700 }}
        visible
        title={ t('护航基本信息变更') }
        destroyOnClose
        className='escortDrawerWrap'
        outerClickClosable={false}
        footer={
          <div className='infoChangeFooter'>
            <Button type="weak" onClick={() => {
              store.dispatch(setDrawerVisibleMapAction({ infoChangeDrawerVisible: false }));
            }}>{t('取消')}
            </Button>
            <Button type="primary" onClick={() => {
              baseinfoRef.current.save();
            }}>{t('提交')}</Button>
          </div>
        }
        onClose={() => {
          // 修改弹框标志
          store.dispatch(setDrawerVisibleMapAction({ infoChangeDrawerVisible: false }));
        }}
      >
        <BaseInfo ref={baseinfoRef} isEdit={true} editList={editList}/>
      </Drawer>

    </>
  );
}
