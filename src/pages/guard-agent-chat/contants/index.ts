
import { t } from '@tea/app/i18n';
export const TEMPLATE_MOCK = 'TemplateRenderNode';

export const TEMPLATE_QUERY_MOCK = 'TemplateQueryNode';

export const SCOPE_QUERY_NODE = 'ScopeQueryNode';

export const SCOPE_SUBSCRIBE_NODE = 'ScopeSubscribeNode';

export const SCOPE_DISABLE_NODE = 'ScopeDisableNode';

export const MONITOR_CONFIG = 'MonitorDashboardConfigNode';

export enum TitleTabsEnum {
  template = 'template',
  mySubscribe = 'mySubscribe',
  nodeMonitoring ='nodeMonitoring'
}


export enum StatusEunm {
  online = 1,
  offline = 0,
}

// 汇总粒度
export const AggregationGranularityOptions = [
  {
    value: 'instance',
    text: t('按实例'),
  }, {
    value: 'node',
    text: t('按节点'),
  },
];

// 汇总函数
export const AggregationFunctionOptions = [
  {
    value: 'avg',
    text: t('均值avg( )'),
  }, {
    value: 'max',
    text: t('最大值max( )'),
  },
  {
    value: 'min',
    text: t('最小值min( )'),
  },
  {
    value: 'sum',
    text: t('累计sum( )'),
  },
];

// 指标周期
export const IndicatorCycleOptions = [
  {
    value: 'minute',
    text: t('按分钟'),
  }, {
    value: 'hour',
    text: t('按小时'),
  },
  {
    value: 'day',
    text: t('按天'),
  },
];

// 时间周期
export const TimePeriodOptions = [
  {
    value: t('时间范围是过去30分钟'),
    text: t('过去30分钟'),
  },
  {
    value: t('时间范围是过去1小时'),
    text: t('过去1小时'),
  },
  {
    value: t('时间范围是过去3小时'),
    text: t('过去3小时'),
  },
  {
    value: t('时间范围是过去6小时'),
    text: t('过去6小时'),
  },
  {
    value: t('时间范围是过去12小时'),
    text: t('过去12小时'),
  },
  {
    value: t('时间范围是过去24小时'),
    text: t('过去24小时'),
  },
  {
    value: t('时间范围是过去3天'),
    text: t('过去3天'),
  },
  {
    value: t('时间范围是过去7天'),
    text: t('过去7天'),
  },
  {
    value: t('时间范围是过去30天'),
    text: t('过去30天'),
  },
];
