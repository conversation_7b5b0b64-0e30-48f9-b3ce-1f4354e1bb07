/* eslint-disable */
import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { ChatTabs, ChatInstructionList } from '@tencent/cloud-chat-ui';
import { Form, Input, InputAdornment, Select, Button, message } from '@tencent/tea-component';
import { updateUserAISubscribeCustomMetric } from '@src/service/api/broadcast-agent-chat';
import { useMetricSelector, changeMetricData } from '@src/store/metric-data';
import { useCommonSelector, changeCommonData } from '@src/store/app-common';
import { useForm, Controller } from 'react-hook-form';
import { app } from '@tea/app';
import { t } from '@tea/app/i18n';
import { EENDTYPE } from '@src/constants';
import { portalContainer } from '@src/utils/common';
import originStore from '@src/origin-store/store';
import { omit, find } from 'lodash';
import './index.less';

interface BroadcastListProps {
  metric: any
}

const compareSelectOption = ['>', '=', '<'];

const MetricThresholdSetting: React.FC<BroadcastListProps> = ({
 metric
}) => {
  const {
		control,
		watch,
		setValue,
		handleSubmit,
		formState: { errors },
	} = useForm({ mode: 'all' });
	const [loading, setLoading] = useState(false);
	const { graphApi: apis }  = originStore.getState().guard;
  const dispatch = useDispatch();
  const [thresholdFactor, setThresholdFactor] = useState('=');
	const [thresholdUnit, setThresholdUnit] = useState('');
	const type = {
		CONSOLE: EENDTYPE.CUSTOMER,
		ISA: EENDTYPE.OPERATOR,
	}[apis.env];

	const compareSelect = (
		<Select
			matchButtonWidth
      size="xs"
			popupContainer={portalContainer}
			options={compareSelectOption.map(value => ({ value }))}
			value={thresholdFactor}
			onChange={value => setThresholdFactor(value)}
		/>
	);

	const unitInput = (
		<Input
			size="xs"
			placeholder="单位"
			disabled
			value={thresholdUnit}
			onChange={value => setThresholdUnit(value)}
		/>
	);

  const getStatus = (fieldState) => {
		if (fieldState?.error?.message) {
			return 'error';
		}
		if (!fieldState.isDirty) {
			return undefined;
		}
		return fieldState.invalid ? 'error' : 'success';
	};

	const onSubmitHandle = async (values) => {
		setLoading(true);
		let operator = '';
		if (type === EENDTYPE.CUSTOMER) {
			const rs = await app.user.current();
			operator = rs.displayName ?? ''
		} else {
			operator = localStorage.getItem('engName');
		}
		await app.user.current()
		const params = {
			ArchId: apis?.archInfo?.archId,
			MetricId: +metric?.id,
			MetricFactor: metric?.thresholdFactor,
			MetricValue: +values?.ThresholdValue,
			Updater: operator
		}
		try {
			const res: any = await updateUserAISubscribeCustomMetric(params);
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			message.success({ content: '保存成功' });
			// history.push('/advisor/config-management?tab=2');
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (metric) {
			setThresholdFactor(metric?.MetricFactor);
			setThresholdUnit(metric?.MetricUnit);
			setValue('ThresholdValue', metric?.MetricValue);
		}
	}, [metric])

  return (
    <div className='metric-threshold-box'>
      <Form layout="default">
				<Controller
					name="ThresholdValue"
					control={control}
					rules={{
						validate: async (value) => {
							if (!value) {
								return '请输入阈值';
							}
							if (value < 0) {
								return '请输入大于等于零的阈值';
							}
							return undefined;
						},
					}}
					render={({ field, fieldState }) => (
						<Form.Item
							required
							status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
							message={errors.ThresholdValue?.message}
						>
							<InputAdornment before={compareSelect} after={unitInput}>
								<Input
									{...field}
									size='s'
									type="number"
									autoComplete="off"
								/>
							</InputAdornment>
						</Form.Item>
					)}
				/>
			</Form>
			<div className='metric-threshold-btns'>
				<Button
					type="primary"
					loading={loading}
					onClick={handleSubmit(onSubmitHandle) as any}
				>
					保存
				</Button>
				<Button
					type="weak"
					onClick={() => {
					}}
				>
					取消
				</Button>
			</div>
    </div>
  );
};

export default MetricThresholdSetting;
