.monitor-config-wrap {
  min-width: 350px;
  box-sizing: border-box;

  .monitor-config-content {
    .form-wrap {
      width: 100%;
      .tip {
        border-bottom: 1px dashed #E6E9EF;
        padding-bottom: 6px;
        margin-bottom: 4px;
        margin-right: 24px;
      }
      .list-wrap{
        padding-right: 24px;
        .metric-name{
          width: 100%;
        }
          .t-list-item{
            font-size: 12px;
            border: 1px solid #e2e6ec;
            padding: 0 12px;
            margin-bottom: 6px;
            height: 40px;
            .t-list-item-main{
              height: 100%;
              .item-label{
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                cursor: move;
              }
            }
          }
      }

      .dimension-select-wrap{
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin: 16px 0 8px 0;
        & > div{
          width: calc((100% - 32px) / 2);
        }
        .tea-form__controls{
          padding-right: 0;
        }
      }
    }

    .charts-card-action-btn {
      display: flex;
      padding-top: 6px;

      .charts-card-action-btn-item {
        width: 45%;
        height: 36px;
        font-size: 12px;
        margin-top: 16px;
        margin-right: 12px
      }

      .charts-card-action-btn-fix-item {
        width: 45%;
        height: 36px;
        font-size: 12px;
        margin-top: 16px;
      }
    }
  }

  .monitor-config-footer{
      padding-right: 24px;
      margin-top: 16px;
      display: flex;
      gap: 12px ;
      .add-btn{
        width: 100%;
      }
  }
}

.node-max10-select-wrap{
  .t-checkbox__former{
    display: none;
  }
  .t-checkbox__input{
    position: relative;
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    border: 1px solid #ddd;
    border-radius: 3px;
    background-color: #fff;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -ms-flex-negative: 0;
    flex-shrink: 0;
  }
  .t-checkbox.t-is-checked .t-checkbox__input{
    border-color: #0052d9;
    background-color: #0052d9;
    -webkit-transition: background-color .2s cubic-bezier(.82,0,1,.9);
    transition: background-color .2s cubic-bezier(.82,0,1,.9);
  }

  .t-checkbox__input:after{
    content: "";
    position: absolute;
    opacity: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
  
  .t-checkbox.t-is-checked .t-checkbox__input:after{
    content: "";
    opacity: 1;
    top: 6px;
    left: 3px;
    width: 5px;
    height: 9px;
    border: 2px solid var(--td-text-color-anti);
    border-radius: 0 0 1px;
    border-top: 0;
    border-left: 0;
    -webkit-transform: rotate(45deg) scale(1) translate(-50%, -50%);
    transform: rotate(45deg) scale(1) translate(-50%, -50%);
    background: transparent;
    position: absolute;
  }

  .t-checkbox__label{
    vertical-align: middle;
    margin-left: 8px;
  }
}
