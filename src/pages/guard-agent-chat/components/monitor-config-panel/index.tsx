import { Checkbox, Form, Input, Select } from '@tencent/tea-component';
import React, { useEffect, useState } from 'react';
import { Button, List, message, Select as TdesignSelect } from 'tdesign-react';
import { useForm, Controller } from 'react-hook-form';
import { AggregationGranularityOptions, AggregationFunctionOptions, IndicatorCycleOptions, TimePeriodOptions } from '@src/pages/guard-agent-chat/contants';
import './index.less';
import { t } from '@tea/app/i18n';
import { DndContext } from '@dnd-kit/core';
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { restrictToParentElement } from '@dnd-kit/modifiers';
import { useCommonSelector, changeCommonData } from '@src/store/app-common';
import { useDispatch } from 'react-redux';
import { use } from '@tencent/tea-sdk-runner';
import originStore from '@src/origin-store/store';
import { getPopupContainer } from '@src/utils/common';

const { ListItem } = List;

interface IProps {
  index?: number
  stepContent?: any;
  id: string;
  stepKey: string;
}

// 可选指标列表最大数量
const maxNumber = 10;

// index获取算法，事件的回调包含active和over对象，无法直接用indexOf获取在原数组中的index
// 用这个方法遍历获取在原数组对应的位置index，供arrayMove方法使用
const getMoveIndex = (array, dragItem) => {
  const { active, over } = dragItem;
  let activeIndex = 0;
  let overIndex = 0;
  try {
    // 遍历数组，查找出active和over的index
    array.forEach((item, index) => {
      if (active.id === item.key) {
        activeIndex = index;
      }
      if (over.id === item.key) {
        overIndex = index;
      }
    });
  } catch (error) {
    overIndex = activeIndex; // 如果有问题，则复位
  }
  return { activeIndex, overIndex };
};

const MonitorConfigPanel = ({ index = 0, id, stepContent = null }: IProps) => {
  const dispatch = useDispatch();
  const { dashboardConfigMap } = useCommonSelector();
  const { graphApi, dashboardId }  = originStore.getState().guard;
  const currentConfig = dashboardConfigMap?.[id]?.[index] || {};
  const isConsole = graphApi.env === 'CONSOLE';
  const queryParams = new URLSearchParams(location.search);
  const appId = +queryParams.get('appid');

  // 提交按钮loading
  const [loading, setLoading] = useState(false);
  // 指标列表
  const [dataList, setDataList] = useState([]);
  // 节点选项
  const [nodeOptions, setNodeOptions] = useState([]);

  const {
    setValue,
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({ mode: 'all' });

  const getStatus = (fieldState) => {
    if (fieldState?.error?.message) {
      return 'error';
    }
    if (!fieldState.isDirty) {
      return undefined;
    }
    return fieldState.invalid ? 'error' : 'success';
  };

  // 拖拽结束后的操作
  const dragEndEvent = (dragItem) => {
    setDataList((prevDataList) => {
      const moveDataList = [...prevDataList];
      const { activeIndex, overIndex } = getMoveIndex(moveDataList, dragItem);
      const newDataList = arrayMove(moveDataList, activeIndex, overIndex);
      return newDataList;
    });
  };

  // 点击checkbox的操作，修改指定ID的对象的isChecked属性取反即可
  const handleCheckedChange = (chosedItemKey, isChecked) => {
    let updatedDataList = [];
    setDataList((prevDataList) => {
      const newDataList = [...prevDataList];
      const isOverMax = newDataList.filter(item => item.isChecked)?.length + (isChecked ? 1 : 0) >  maxNumber;
      updatedDataList = newDataList.map((item) => {
        if (item.key === chosedItemKey && !isOverMax) {
          return { ...item, isChecked: !item.isChecked };
        }
        return item;
      });
      return updatedDataList;
    });
  };

  // 拖拽项组件
  const SortableItem = (itemProps) => {
    // 父传子，从props里拿，建议使用其他名字（如itemProps）代替props，以免和父组件的props混淆
    const { checkboxItem } = itemProps;
    const { setNodeRef, attributes, listeners, transform, transition } = useSortable({
      id: checkboxItem.key, // 这里传入的id属性必须和SortableContext的items数组一一对应
      transition: {
        duration: 500,
        easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
      },
    });
    const styles = {
      transform: CSS.Transform.toString(transform),
      transition,
    };
    return (
            <List ref={setNodeRef} {...attributes} style={styles} className='list-wrap'>
                <ListItem
                  key={checkboxItem.key}
                  action={
                    <Checkbox
                      value={checkboxItem.isChecked}
                      onChange={() => handleCheckedChange(checkboxItem.key, !checkboxItem.isChecked)} />
                  }
                >
                  <div {...listeners} className='item-label'>{checkboxItem.label}</div>
                </ListItem>
            </List>
    );
  };

  const onSubmitHandle = async (values) => {
    try {
      let sdk = null;
      // 兼容插件使用方式
      if (isConsole) {
        sdk = await use('ai-bi-dashboard-sdk');
      } else {
        sdk = await graphApi.use('ai-bi-dashboard-sdk');
      }
      // 提供了查询、添加、删除等方法
      const dashboardClient = new sdk.client.DashboardClient({
        archId: graphApi?.archInfo?.archId,
        isConsole,
        ...(!isConsole && { AppId: appId, Uin: graphApi?.uin }),
      });
      setLoading(true);
      // 如果是编辑，先删除原来的图表
      if (currentConfig?.configId) {
        await dashboardClient?.removeDashboardGraph(dashboardId, currentConfig?.graphIds || []);
        // console.log(t('原图表删除成功'), dashboardId, currentConfig?.graphIds);
      }

      dispatch(changeCommonData({
        dashboardConfigMap: {
          ...dashboardConfigMap,
          [id]: currentConfig.configId
            ? dashboardConfigMap?.[id]?.map((i) => {
              if (i.configId === currentConfig?.configId) {
                return { ...i, ...values, MetricList: dataList, isFinish: true, graphIds: [], graphItems: [] };
              }
              return i;
            })
            : [{
              ...values,
              MetricList: dataList,
              NodeSigmaId: stepContent?.ProductEnName,
              ProductName: stepContent?.ProductChName,
              nodeOptions,
              isFinish: true,
              configId: index + 1,
              graphIds: [],
              graphItems: [],
            }],
        },
      }));
      setLoading(false);
    } catch (e) {
      console.log(e);
      message.error(t('生成失败，请重试'));
    }
  };

  // 取消
  function deleteConfig() {
    dispatch(changeCommonData({
      dashboardConfigMap: {
        ...dashboardConfigMap,
        [id]: dashboardConfigMap?.[id]?.filter(i => i.configId !== currentConfig?.configId),
      },
    }));
  }

  useEffect(() => {
    // 默认汇总函数为avg
    setValue('Func', AggregationFunctionOptions?.[0]?.value);
    const currentConfig = dashboardConfigMap?.[id]?.[index] || {};
    if (currentConfig?.configId) {
      setValue('Title', currentConfig?.Title);
      setValue('Dimension', currentConfig?.Dimension);
      setValue('Func', currentConfig?.Func);
      setValue('MetricPeriod', currentConfig?.MetricPeriod);
      setValue('TimePeriod', currentConfig?.TimePeriod);
      setValue('NodeUUIDs', currentConfig?.NodeUUIDs);
      setDataList(currentConfig?.MetricList || []);
      setNodeOptions(currentConfig?.nodeOptions || []);
    } else if (stepContent) {
      // 第一次对话，初始化指标列表和节点列表
      const list = stepContent?.MetricDetail?.map((i, index) => ({
        label: i.MetricChName,
        value: i.MetricEnName,
        key: i.MetricEnName,
        isChecked: !(index >= maxNumber),
      }));
      // 流式接口初始化节点列表
      const options = stepContent?.NodeList?.map(i => ({
        label: i.NodeName,
        value: i.NodeUuid,
      }));
      setDataList(list || []);
      setNodeOptions(options || []);
    }
  }, [dashboardConfigMap]);

  return <div className='monitor-config-wrap' style={{ pointerEvents: currentConfig?.isFinish ? 'none' : 'auto' }}>
    <div className='title'>{t('Dashboard 配置')}</div>
    <div className='monitor-config-content'>
      <Form layout="vertical" className='form-wrap'>
         <Controller
          name="Title"
          control={control}
          render={({ field }) => (
            <Form.Item>
              <Input
                style={{ width: '100%' }}
                placeholder={t('请输入文字说明')}
                {...field}
              />
            </Form.Item>
          )}
        />
        <div className='tip'>{t('*请按需选择指标（最多10个），可拖动改变顺序')}</div>
         <DndContext onDragEnd={dragEndEvent} modifiers={[restrictToParentElement]}>
            <SortableContext items={dataList.map(c => c.key)} strategy={verticalListSortingStrategy}>
                {/* 这里的items接收一个数组，这个数组的值要和useSortable传入的id属性一一对应 */}
                <div className="attrs">
                    <ul className="attrs-list">
                        {dataList.map((checkboxItem, index) => (
                            <SortableItem checkboxItem={checkboxItem} key={checkboxItem.key} index={index} />
                        ))}
                    </ul>
                </div>
            </SortableContext>
        </DndContext>
        <div className='dimension-select-wrap'>
          <Controller
            name="Dimension"
            control={control}
            rules={{
              validate: value => (!value ? t('选择汇总粒度') : undefined),
            }}
            render={({ field, fieldState }) => (
              <Form.Item
                label={t('汇总粒度')}
                status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
                message={errors.Dimension?.message}
                showStatusIcon={false}
              >
                <Select
                  {...field}
                  popupContainer={getPopupContainer()}
                  size='full'
                  matchButtonWidth
                  appearance="button"
                  placeholder={t('请选择汇总粒度')}
                  options={AggregationGranularityOptions}
                />
              </Form.Item>
            )}
          />
           <Controller
            name="Func"
            control={control}
            rules={{
              validate: value => (!value ? t('选择汇总函数') : undefined),
            }}
            render={({ field, fieldState }) => (
              <Form.Item
                label={t('汇总函数')}
                status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
                message={errors.Func?.message}
                showStatusIcon={false}
              >
                <Select
                  {...field}
                  popupContainer={getPopupContainer()}
                  size='full'
                  matchButtonWidth
                  appearance="button"
                  placeholder={t('请选择汇总函数')}
                  options={AggregationFunctionOptions}
                />
              </Form.Item>
            )}
          />
           <Controller
            name="MetricPeriod"
            control={control}
            rules={{
              validate: value => (!value ? t('选择指标周期') : undefined),
            }}
            render={({ field, fieldState }) => (
              <Form.Item
                label={t('指标周期')}
                status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
                message={errors.MetricPeriod?.message}
                showStatusIcon={false}
              >
                <Select
                  {...field}
                  popupContainer={getPopupContainer()}
                  size='full'
                  matchButtonWidth
                  appearance="button"
                  placeholder={t('请选择指标周期')}
                  options={IndicatorCycleOptions}
                />
              </Form.Item>
            )}
          />
           <Controller
            name="TimePeriod"
            control={control}
            rules={{
              validate: value => (!value ? t('选择时间周期') : undefined),
            }}
            render={({ field, fieldState }) => (
              <Form.Item
                label={t('时间周期')}
                status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
                message={errors.TimePeriod?.message}
                showStatusIcon={false}
              >
                <Select
                  {...field}
                  popupContainer={getPopupContainer()}
                  size='full'
                  matchButtonWidth
                  appearance="button"
                  placeholder={t('请选择时间周期')}
                  options={TimePeriodOptions}
                />
              </Form.Item>
            )}
          />
        </div>
        <div>{t('覆盖节点（最多10个）')}</div>
         <Controller
          name="NodeUUIDs"
          control={control}
          rules={{
            validate: (value) => {
              if (!value?.length) {
                return t('请选择节点');
              }
              return undefined;
            },
          }}
          render={({ field, fieldState }) => (
            <Form.Item
              status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
              message={errors.NodeUUIDs?.message}
              showStatusIcon={false}
            >
              <TdesignSelect
                {...field}
                clearable
                multiple
                placeholder={t('请选择节点')}
                max={maxNumber}
                minCollapsedNum={3}
                options={nodeOptions}
                popupProps={{
                  overlayClassName: 'node-max10-select-wrap',
                }}
              >
              </TdesignSelect>
            </Form.Item>
          )}
        />
      </Form>
    </div>
     <div className='monitor-config-footer'>
        <Button
          shape="rectangle"
          size="medium"
          type="button"
          variant="base"
          className='add-btn'
          loading={loading}
          disabled={currentConfig?.isFinish || !dataList.length || dataList?.filter(i => i?.isChecked)?.length === 0}
          onClick={handleSubmit(onSubmitHandle) as any}
        >
          { currentConfig?.isFinish ? t('已添加') :  t('添加到Dashboard')}
        </Button>
        {currentConfig?.configId && !currentConfig?.isFinish && <Button
          shape="rectangle"
          size="medium"
          type="button"
          variant="outline"
          className='add-btn'
          loading={loading}
          onClick={() => {
            deleteConfig();
          }}
        >
          {t('取消')}
        </Button>}
      </div>
  </div>;
};

export default MonitorConfigPanel;
