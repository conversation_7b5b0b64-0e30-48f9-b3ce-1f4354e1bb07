import React from 'react';
import { useDispatch } from 'react-redux';
import { ChatInstructionList } from '@tencent/cloud-chat-ui';
import { Bubble } from '@tencent/tea-component';
import { useMetricSelector } from '@src/store/metric-data';
import { t } from '@tea/app/i18n';
import { useCommonSelector, changeCommonData } from '@src/store/app-common';
import { StatusEunm } from '@src/pages/guard-agent-chat/contants';
import { TitleTabsEnum } from '../../contants';
import { getPopupContainer } from '@src/utils/common';
import './index.less';

interface MetricSearchListProps {
  searchValue: string;
  onClose?: () => void;
  getKeyword: (val: string, searchVal: string) => string;
  currentTab: TitleTabsEnum;
  textareaRef: any;
  onSendMessage?: (message: string, intention?: string, intentionParam?: string) => void;
}

const MetricSearchList: React.FC<MetricSearchListProps> = ({
  searchValue,
  onClose,
  getKeyword,
  currentTab,
  textareaRef,
  onSendMessage,
}) => {
  const dispatch = useDispatch();
  const { list, mySubscribeList, monitorList } = useMetricSelector();
  const {
    isTemplateEdit,
    agentInputValue,
    editMessageId,
    editTemplateContent,
  } = useCommonSelector();

  // 根据当前tab获取对应的数据和过滤逻辑
  const getFilteredData = () => {
    if (currentTab === TitleTabsEnum.template) {
      // 搜索播报指标数据
      return list?.filter(item => (item?.metricInfo?.MetricName ?? '')?.toLowerCase()?.includes(searchValue?.toLowerCase())) || [];
    }
    if (currentTab === TitleTabsEnum.nodeMonitoring) {
      // 搜索节点监控数据
      return monitorList?.filter(item => (item?.metricInfo?.MetricName ?? '')?.toLowerCase()?.includes(searchValue?.toLowerCase())) || [];
    }
    if (currentTab === TitleTabsEnum.mySubscribe) {
      // 搜索我的订阅数据
      return mySubscribeList?.filter(item => (item?.TemplateName ?? '')?.toLowerCase()?.includes(searchValue?.toLowerCase())) || [];
    }
    return [];
  };

  const filteredData = getFilteredData();

  // 统一的点击处理逻辑
  const handleItemClick = (item: any) => {
    if (currentTab === TitleTabsEnum.template) {
      const messageVal = `${item.id} ${item?.metricInfo.MetricName}`;
      if (isTemplateEdit && editMessageId) {
        const newContent = `${editTemplateContent || ''}${editTemplateContent ? '\n' : ''}${messageVal}`;
        dispatch(changeCommonData({ editTemplateContent: newContent }));
      } else {
        const inputMessageVal = `${agentInputValue}${agentInputValue && '\n'}${messageVal}`;
        // 正常状态：设置到ChatSender
        dispatch(changeCommonData({ agentInputValue: inputMessageVal }));
        setTimeout(() => {
          textareaRef.current?.focus();
          const length = textareaRef.current?.value.length; // 获取文本长度
          textareaRef.current?.setSelectionRange(length, length);
          textareaRef.current.scrollTop = textareaRef.current?.scrollHeight || 0;
        }, 0);
      }
    } else if (currentTab === TitleTabsEnum.mySubscribe) {
      if (item.Status === StatusEunm.offline || isTemplateEdit) return;
      onSendMessage(t('当前播报'), 'TemplateQueryNode');
    } else if (currentTab === TitleTabsEnum.nodeMonitoring) {
      const messageVal = t('生成 {{attr0}} {{attr1}} Dashboard', { attr0: item.id, attr1: item?.metricInfo.MetricName });
      // 意图参数
      const IntentionParam = {
        ProductCode: item.id,
        MetricName: item?.metricInfo.Metric,
      };
      onSendMessage?.(messageVal, 'MonitorDashboardConfigNode', JSON.stringify(IntentionParam));
    }
    onClose?.();
  };

  // 统一的渲染逻辑
  const renderItem = (item: any, index: number) => {
    if (currentTab === TitleTabsEnum.template) {
      // 渲染播报指标项
      return (
        <Bubble
          content={item?.metricInfo.MetricName ?? ''}
          key={index}
          openDelay={700}
          popupContainer={getPopupContainer()}
        >
          <ChatInstructionList.Item
            keyword={getKeyword(item?.metricInfo?.MetricName ?? '', searchValue)}
            label={item?.metricInfo.MetricName ?? ''}
            category={item.productName ?? ''}
            showCollect={false}
            onClick={() => handleItemClick(item)}
          />
        </Bubble>
      );
    }
    if (currentTab === TitleTabsEnum.mySubscribe) {
      // 渲染我的订阅项
      const isOffline = item.Status === StatusEunm.offline;
      return (
        <ChatInstructionList.Item
          keyword={getKeyword(item?.TemplateName ?? '', searchValue)}
          label={item?.TemplateName ?? ''}
          category={item.Status === StatusEunm.online ? t('已启用') : t('已停用')}
          showCollect={false}
          onClick={() => handleItemClick(item)}
          className={(isOffline || isTemplateEdit) ? 'status-offline' : 'status-online'}
          style={(isOffline || isTemplateEdit) ? { cursor: 'not-allowed', opacity: 0.6 } : {}}
        />
      );
    }
    if (currentTab === TitleTabsEnum.nodeMonitoring) {
      // 渲染节点监控
      return (
        <Bubble
          content={item?.metricInfo.MetricName ?? ''}
          key={index}
          openDelay={700}
          popupContainer={getPopupContainer()}
        >
          <ChatInstructionList.Item
            keyword={getKeyword(item?.metricInfo?.MetricName ?? '', searchValue)}
            label={item?.metricInfo.MetricName ?? ''}
            category={item.productName ?? ''}
            showCollect={false}
            onClick={() => handleItemClick(item)}
          />
        </Bubble>
      );
    }
    return null;
  };

  return (
    <ChatInstructionList>
      {filteredData.map((item: any, index: number) => renderItem(item, index))}
    </ChatInstructionList>
  );
};

export default MetricSearchList;
