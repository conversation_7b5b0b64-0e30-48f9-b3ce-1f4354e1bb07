import React, { useState, useEffect } from 'react';
import { ChatTabs, ChatInstructionList } from '@tencent/cloud-chat-ui';
import { Bubble, StatusTip } from '@tencent/tea-component';
import { useMetricSelector } from '@src/store/metric-data';
import { getPopupContainer } from '@src/utils/common';
import { t } from '@tea/app/i18n';

interface BroadcastListProps {
  onClose?: () => void;
  visible?: boolean;
  onSendMessage: (message: string, intention?: string, intentionParam?: string) => void;
}

// 节点监控
const MonitorList: React.FC<BroadcastListProps> = ({
  onClose,
  visible,
  onSendMessage,
}) => {
  const { monitorTabs, monitorTabsDataMap  } = useMetricSelector();
  const [activeId, setActiveId] = useState('');

  useEffect(() => {
    if (visible && monitorTabs.length > 0 && !activeId) {
      setActiveId(monitorTabs[0].id);
    }
  }, [monitorTabs, visible]);

  return (
    <>
      {
        monitorTabs?.length > 0 ? (
          <ChatTabs
            placement='left'
            tabs={monitorTabs}
            activeId={activeId}
            tabBarRender={
              (children, tab) => (
                  <Bubble
                    className={'sub-config-tab-l-bubble'}
                    content={tab.label ?? ''}
                    openDelay={700}
                    popupContainer={getPopupContainer()}
                  >
                    <a
                      className={`tea-tabs__tab ${tab.id === activeId ? 'is-active' : ''}`}
                      onClick={
                        () => {
                          setActiveId(tab.id);
                        }
                      }
                    >
                      {children}
                    </a>
                  </Bubble>
              )
            }
          >
            {
              monitorTabs?.map((item, i) => (
                <ChatTabs.TabPanel id={item.id} key={i}>
                  <ChatInstructionList>
                    <Bubble
                      content={t('生成{{attr0}} Dashboard', { attr0: item.label }) ?? ''}
                      key='totalDashboard'
                      openDelay={700}
                      popupContainer={getPopupContainer()}
                    >
                      <ChatInstructionList.Item
                          label={t('生成{{attr0}} Dashboard', { attr0: item.label })}
                          key='totalDashboard'
                          showCollect={false}
                          onClick={() => {
                            const messageVal = t('生成 {{attr0}} Dashboard', { attr0: item.label });
                            // 意图参数
                            const IntentionParam = {
                              ProductCode: item.id,
                              // MetricId: null,
                            };
                            onSendMessage?.(messageVal, 'MonitorDashboardConfigNode', JSON.stringify(IntentionParam));
                            onClose?.();
                          }}
                        />
                    </Bubble>
                    {monitorTabsDataMap?.[item.id]?.map((el: any, index: number) => (
                      <Bubble
                        content={el.MetricName ?? ''}
                        key={index}
                        openDelay={700}
                        popupContainer={getPopupContainer()}
                      >
                        <ChatInstructionList.Item
                          label={el.MetricName}
                          key={index}
                          showCollect={false}
                          onClick={() => {
                            const messageVal = t('生成 {{attr0}} {{attr1}} Dashboard', { attr0: item.id, attr1: el.MetricName });
                            // 意图参数
                            const IntentionParam = {
                              ProductCode: item.id,
                              MetricName: el.Metric,
                            };
                            onSendMessage?.(messageVal, 'MonitorDashboardConfigNode', JSON.stringify(IntentionParam));
                            onClose?.();
                          }}
                        />
                      </Bubble>
                    ))}
                  </ChatInstructionList>
                </ChatTabs.TabPanel>
              ))
            }
          </ChatTabs>
        ) : (
          <div className="status-wrap">
            <StatusTip status={'empty'} emptyText={t('架构图未绑定已支持产品的资源')} />
          </div>
        )
      }
    </>
  );
};

export default MonitorList;
