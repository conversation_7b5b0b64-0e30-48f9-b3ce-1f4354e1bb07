.broadcast-record-drawer-wrap {
  .broadcast-record-search-box {
    margin-bottom: 20px;
  }
  .tea-table .tea-table__header thead tr {
    background-color: #F2F4F8;
  }
  .tea-table__header .tea-table__box th > div {
    color: rgba(0, 0, 0, 0.90);
  }
  .message-con-wrap {
    white-space: pre-wrap;
    max-height: 110px;
    overflow: auto;
    user-select: text;
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    &::-webkit-scrollbar-thumb {
      background: #888; /* 滑块颜色 */
      border-radius: 6px; /* 滑块圆角 */
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #555; /* 悬停时的滑块颜色 */
    }
  }
  .tea-table__sortbtn:hover .tea-icon-sort {
    background-size: auto;
  }
}