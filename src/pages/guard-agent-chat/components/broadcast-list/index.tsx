/* eslint-disable */
import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { ChatTabs, ChatInstructionList } from '@tencent/cloud-chat-ui';
import { Popup } from 'tdesign-react';
import { Bubble, StatusTip, message, Button } from '@tencent/tea-component';
import { describeGuardAISubscribeMetricInfo } from '@src/service/api/broadcast-agent-chat';
import MetricThresholdSetting from '@src/pages/guard-agent-chat/components/mtric-threshold-setting';
import { useMetricSelector, changeMetricData } from '@src/store/metric-data';
import { useCommonSelector, changeCommonData } from '@src/store/app-common';
import { getPopupContainer, portalContainer } from '@src/utils/common';
import originStore from '@src/origin-store/store';
import { t } from '@tea/app/i18n';
import './index.less';

interface BroadcastListProps {
  onClose?: () => void;
  textareaRef: any;
  visible?: boolean;
}

interface CustomItemProps {
  label: string;
  metric: any;
  onClick: () => void;
}

const CustomInstructionItem: React.FC<CustomItemProps> = ({
  label,
  metric,
  onClick,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  return (
    <div
      className="custom-instruction-item"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <ChatInstructionList.Item
        label={label}
        showCollect={false}
        onClick={onClick}
      />
      {isHovered && (
        <Popup
          content={
            <MetricThresholdSetting
              metric={metric}
            />
          }
          placement="left"
          trigger="click"
          showArrow
          attach={portalContainer}
        >
          <Button
            type="weak"
            className="hover-button"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            {t('阈值')}
          </Button>
        </Popup>
      )}
    </div>
  );
};

const BroadcastList: React.FC<BroadcastListProps> = ({
  onClose,
  textareaRef,
  visible,
}) => {
  const dispatch = useDispatch();
  const { graphApi: apis }  = originStore.getState().guard;
  const { contentTabs, tabsDataMap,  } = useMetricSelector();
  const {
    agentInputValue,
    isTemplateEdit,
    editMessageId,
    editTemplateContent,
  } = useCommonSelector();
  const [activeId, setActiveId] = useState('');
  const [loading, setLoading] = useState(false);

  const fetchMetricData = async () => {
    setLoading(true);
    try {
      const res: any = await describeGuardAISubscribeMetricInfo({
        ArchId: apis?.archInfo?.archId,
      });
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        setLoading(false);
        return;
      }

      const temTabs: any[] = [];
      const temMap: any = {};
      const temList: any[] = [];

      res?.ProductMetricInfoList?.forEach((item: any) => {
        temTabs.push({
          id: item.Product,
          label: item.ProductName,
        });
        temMap[item.Product] = item.MetricInfoList;
        item.MetricInfoList?.forEach((el: any) => {
          temList.push({
            id: item.Product,
            productName: item.ProductName,
            metricInfo: el,
          });
        });
      });

      dispatch(changeMetricData({
        contentTabs: temTabs,
        tabsDataMap: temMap,
        list: temList,
      }));

      setLoading(false);

      if (temTabs.length > 0) {
        setActiveId(temTabs[0].id);
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      message.error({ content: errorMsg });
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && contentTabs.length === 0) {
      fetchMetricData();
    }
  }, [visible]);

  useEffect(() => {
    if (contentTabs.length > 0 && !activeId) {
      setActiveId(contentTabs[0].id);
    }
  }, [contentTabs]);

  if (loading) {
    return (
      <div className="status-wrap">
        <StatusTip status="loading" />
      </div>
    );
  }

  const handleItemClick = (item: any, el: any) => {
    const messageVal = `${item.id} ${el.MetricName}`;
    if (isTemplateEdit && editMessageId) {
      const newContent = `${editTemplateContent || ''}${editTemplateContent ? '\n' : ''}${messageVal}`;
      dispatch(changeCommonData({ editTemplateContent: newContent }));
    } else {
      const inputMessageVal = `${agentInputValue}${agentInputValue && '\n'}${messageVal}`;
      // 正常状态：设置到ChatSender
      dispatch(changeCommonData({ agentInputValue: inputMessageVal }));
      setTimeout(() => {
        textareaRef?.current?.focus();
        const length = textareaRef?.current?.value.length; // 获取文本长度
        textareaRef?.current?.setSelectionRange(length, length);
        if (textareaRef.current) {
          textareaRef.current.scrollTop = textareaRef.current?.scrollHeight || 0;
        }
      }, 0);
    }
    onClose?.();
  };
  return (
    <>
      {
        contentTabs?.length > 0 ? (
          <ChatTabs
            placement='left'
            tabs={contentTabs}
            activeId={activeId}
            tabBarRender={
              (children, tab) => (
                  <Bubble
                    className={'sub-config-tab-l-bubble'}
                    content={tab.label ?? ''}
                    openDelay={700}
                    popupContainer={getPopupContainer()}
                  >
                    <a
                      className={`tea-tabs__tab ${tab.id === activeId ? 'is-active' : ''}`}
                      onClick={
                        () => {
                          setActiveId(tab.id);
                        }
                      }
                    >
                      {children}
                    </a>
                  </Bubble>
              )
            }
          >
            {
              contentTabs?.map((item, i) => (
                <ChatTabs.TabPanel id={item.id} key={i}>
                  <ChatInstructionList>
                    {tabsDataMap?.[item.id]?.map((el: any, index: number) => (
                      <Bubble
                        content={el.MetricName ?? ''}
                        key={index}
                        openDelay={700}
                        popupContainer={getPopupContainer()}
                      >
                        <CustomInstructionItem
                          label={el.MetricName}
                          onClick={() => {
                            handleItemClick(item, el);
                          }}
                          metric={el}
                        />
                      </Bubble>
                    ))}
                  </ChatInstructionList>
                </ChatTabs.TabPanel>
              ))
            }
          </ChatTabs>
        ) : (
          <div className="status-wrap">
            <StatusTip status={'empty'} />
          </div>
        )
      }
    </>
  );
};

export default BroadcastList;
