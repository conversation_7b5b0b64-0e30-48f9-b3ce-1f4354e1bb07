.sub-config-drawer-wrap {
  .title-tabs-wrap {
    .tea-tabs__tabbar {
      margin: 0!important;
      .tea-tabs__tabitem {
        flex: none!important;
      }
    }
    .tea-tabs--vertical > .tea-tabs__tabbar .tea-tabs__tab {
      padding: 4px 28px 4px 12px!important;
      text-align: left;
    }
    .tea-tabs--vertical>.tea-tabs__tabbar .tea-tabs__tabitem {
      min-width: 120px;
      max-width: 200px;
    }
  }
  .search-wrap {
    display: flex;
    align-items: center;
    position: relative;
    .close-icon-wrap {
      position: absolute;
      right: 3px;
      top: 2px;
      padding: 5px;
      cursor: pointer;
      &:hover {
        background-color: #f7f8fb;
      }
      .tea-icon-close {
        opacity: 0.7;
      }
    }
  }
  .status-wrap {
    margin-top: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.sub-config-tab-l-bubble.tea-tabs__tab {
  font-weight: 400;
  &::after {
    display: none;
  }
}

.sub-drawer-box {
  position: relative;

  .chat-drawer-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1016;
  }
}