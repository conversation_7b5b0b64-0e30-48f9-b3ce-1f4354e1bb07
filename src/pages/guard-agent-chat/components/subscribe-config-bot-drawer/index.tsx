import React, { useState, useEffect } from 'react';
import { Chat<PERSON><PERSON><PERSON>, ChatTabs } from '@tencent/cloud-chat-ui';
import { Input, Icon } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
import { TitleTabsEnum } from '../../contants';
import BroadcastList from '../broadcast-list';
import MetricSearchList from '../metric-search-list';
import MySubscribeList from '../my-subscribe-list';
import MonitorList from '../monitor-list';
import './index.less';

interface Props {
  visible: boolean,
  onClose: () => void,
  textareaRef: any,
  onSendMessage?: (message: string, intention?: string, intentionParam?: string) => void,
}

const tabs = [
  { id: TitleTabsEnum.template, label: t('播报指标') },
  { id: TitleTabsEnum.mySubscribe, label: t('我的订阅') },
  { id: TitleTabsEnum.nodeMonitoring, label: t('节点监控') },
];

const SubscribeConfigBotDrawer = ({
  visible,
  onClose,
  textareaRef,
  onSendMessage,
}: Props) => {
  const [searchValue, setSearchValue] = useState('');
  const [mainActiveTab, setMainActiveTab] = useState(TitleTabsEnum.template);

  const getKeyword = (val: string, searchVal: string) => {
    const valLoawerCase = val?.toLowerCase();
    const searchValLoawerCase = searchVal?.toLowerCase();
    const satrtIndex = valLoawerCase.indexOf(searchValLoawerCase);
    return val.slice(satrtIndex, satrtIndex + (searchVal?.length ?? 0));
  };

  // 每次打开抽屉时重置到第一个tab
  useEffect(() => {
    if (visible) {
      setMainActiveTab(TitleTabsEnum.template);
    }
  }, [visible]);

  return <ChatDrawer
      className='sub-config-drawer-wrap'
      visible={visible}
      outerClickClosable
      placement='bottom'
      onClose={() => onClose?.()}
      title={
        <div className='search-wrap'>
          <Icon type="search" />
          <Input
            placeholder={t('请填写指标进行搜索')}
            value={searchValue}
            onChange={
              (val) => {
                setSearchValue(val);
              }
            }
            size='full'
          />
          {
            searchValue && <div
              className='close-icon-wrap'
              onClick={
                () => {
                  setSearchValue('');
                }
              }
            >
              <Icon type="close" />
            </div>
          }
        </div>
      }
    >
      {searchValue ? (
        <MetricSearchList
          searchValue={searchValue}
          onClose={onClose}
          getKeyword={getKeyword}
          currentTab={mainActiveTab}
          onSendMessage={onSendMessage}
          textareaRef={textareaRef}
        />
      ) : (
        <ChatTabs
          tabs={tabs}
          className='title-tabs-wrap'
          activeId={mainActiveTab}
          onActive={val => setMainActiveTab(val.id as TitleTabsEnum)}
        >
          <ChatTabs.TabPanel id={TitleTabsEnum.template}>
            <BroadcastList
              onClose={onClose}
              textareaRef={textareaRef}
              visible={visible && mainActiveTab === TitleTabsEnum.template}
            />
          </ChatTabs.TabPanel>
          <ChatTabs.TabPanel id={TitleTabsEnum.mySubscribe}>
            <MySubscribeList
              onClose={onClose}
              visible={visible && mainActiveTab === TitleTabsEnum.mySubscribe}
              onSendMessage={onSendMessage}
            />
          </ChatTabs.TabPanel>
          <ChatTabs.TabPanel id={TitleTabsEnum.nodeMonitoring}>
            <MonitorList
              onClose={onClose}
              visible={visible && mainActiveTab === TitleTabsEnum.nodeMonitoring}
              onSendMessage={onSendMessage}
            />
          </ChatTabs.TabPanel>
        </ChatTabs>
      )}
    </ChatDrawer>;
};

export default SubscribeConfigBotDrawer;
