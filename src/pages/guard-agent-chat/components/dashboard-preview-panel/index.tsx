import React, { useEffect, useState } from 'react';
import { Text } from '@tencent/tea-component';
import { Button, Skeleton } from 'tdesign-react';
import type { SkeletonProps } from 'tdesign-react';
import './index.less';
import { t, Trans, Slot } from '@tea/app/i18n';
import { useCommonSelector, changeCommonData } from '@src/store/app-common';
import { useDispatch } from 'react-redux';
import _ from 'lodash';
import { SDKLoader } from '@tencent/tea-app';
import originStore from '@src/origin-store/store';
import { setDashboardDrawerVisible, setScrollGraphId } from '@src/origin-store/guardAction';
import { GraphLoadingType } from '@src/types/ai-chat';
import { use } from '@tencent/tea-sdk-runner';
interface IProps {
  id: string;
  productName?: string;
}
let sdk = null;
let dashboardClient = null;

const DashboardPreviewPanel = ({ id, productName }: IProps) => {
  // 轮询生成进度的taskId
  let taskId = null;
  const dispatch = useDispatch();
  const { dashboardConfigMap } = useCommonSelector();
  const { dashboardId, graphApi }  = originStore.getState().guard;
  // 复制最后一条数据
  const currentConfig = _.cloneDeep(dashboardConfigMap?.[id]?.[dashboardConfigMap?.[id].length - 1] || {});
  const isConsole = graphApi.env === 'CONSOLE';
  const queryParams = new URLSearchParams(location.search);
  const appId = +queryParams.get('appid');
  // 查询进度
  const [processData, setProcessData] = useState<any>(null);
  // 是否创建中
  const creatingStatus = [GraphLoadingType.initTaskId, GraphLoadingType.initBiTask, GraphLoadingType.runningBiTask];
  // 是否创建成功
  const [createSuccess, setCreateSuccess] = useState(!!currentConfig?.graphIds?.length);
  // 是否创建失败
  const createFail = [GraphLoadingType.failBiTask, GraphLoadingType.failTaskId];

  const { NodeSigmaId, MetricList, NodeUUIDs, Dimension, Func, MetricPeriod, TimePeriod } = currentConfig;
  const options = {
    ArchId: graphApi?.archInfo?.archId,
    NodeSigmaId,
    MetricNames: MetricList.filter(c => c.isChecked).map(c => c.value),
    NodeUUIDs,
    Dimension,
    Func,
    MetricPeriod,
    TimePeriod,
  };

  const rowCol: SkeletonProps['rowCol'] = [
    [1, 1].map(() => ({
      type: 'rect',
      content: t('加载中...'),
      width: '100%',
      height: '180px',
    })),
  ];

  async function initTask() {
    // 兼容插件使用方式
    if (isConsole) {
      sdk = await use('ai-bi-dashboard-sdk');
    } else {
      sdk = await graphApi.use('ai-bi-dashboard-sdk');
    }
    // 提供了查询、添加、删除等方法
    dashboardClient = new sdk.client.DashboardClient({
      archId: graphApi?.archInfo?.archId,
      isConsole,
      ...(!isConsole && { AppId: appId, Uin: graphApi?.uin }),
    });
    // console.log(t('生成图卡的参数：'), { dashboardId, title: currentConfig?.Title, options });
    taskId = await dashboardClient?.createTask(
      dashboardId,
      currentConfig?.Title,
      options,
      // 监听器
      (data) => {
        // console.log(t('加载进度'), data);
        setProcessData(data);
      },
    );
  }

  useEffect(() => {
    if (processData?.process === 1
      && processData?.status === GraphLoadingType.successBiTask
      && processData?.graphIds?.length) {
      dispatch(changeCommonData({
        dashboardConfigMap: {
          ...dashboardConfigMap,
          [id]: dashboardConfigMap?.[id]?.map((i) => {
            if (i.configId === currentConfig?.configId) {
              const { graphIds, graphItems } = processData;
              return { ...i, graphIds, graphItems };
            }
            return i;
          }),
        },
      }));
      setCreateSuccess(true);
    }
  }, [JSON.stringify(processData)]);

  useEffect(() => {
    !currentConfig?.graphIds?.length && initTask();
    return () => {
      if (taskId) {
        // console.log(t('图卡组件销毁，销毁的任务id'), taskId);
        dashboardClient?.cancelTask(taskId);
        taskId = '';
      }
    };
  }, []);

  return <div className='dashboard-preview-wrap'>
    <div className='title'><Trans><Slot content={productName} /> Dashboard 预览</Trans></div>
    <div className='chat-wrap'>
     { creatingStatus.includes(processData?.status) && <div className='loading-wrap'>
        <div className='loading-text'><Trans>加载进度：<Slot content={((processData?.process || 0) * 100).toFixed(2)} />%</Trans></div>
        <Skeleton animation='gradient' rowCol={rowCol} className='loading-skeleton'></Skeleton>
      </div>}
      { createSuccess
         && <SDKLoader sdkNames={['ai-bi-dashboard-sdk']}>
          {([sdk]) => (
            <sdk.reactNode.DashboardItem
              dashboardId={ dashboardId }
              graphIds={currentConfig?.graphIds}
              graphItems={currentConfig?.graphItems}
              apis={graphApi}
            />
          )}
        </SDKLoader>
      }
      {
        createFail.includes(processData?.status)
        && <div>
          <Text theme="danger" className='error-tip'>{t('图卡生成失败，请您重试。')}</Text>
        </div>
      }

    </div>
   {createSuccess && <div className='dashboard-preview-footer'>
        <Button
          shape="rectangle"
          size="medium"
          type="button"
          variant="base"
          onClick={() => {
            // 修改最后一条数据的状态
            currentConfig.isEdit = true;
            currentConfig.isFinish = false;
            currentConfig.configId = currentConfig.configId + 1;
            dispatch(changeCommonData({
              dashboardConfigMap: {
                ...dashboardConfigMap,
                [id]: [...dashboardConfigMap?.[id], currentConfig],
              },
            }));
          }}
        >
          {t('修改')}
        </Button>
         <Button
          shape="rectangle"
          size="medium"
          type="button"
          variant="outline"
          onClick={() => {
            originStore.dispatch(setDashboardDrawerVisible(true));
            originStore.dispatch(setScrollGraphId(currentConfig?.graphIds?.[0] || ''));
          }}
        >
          {t('查看')}
        </Button>
    </div>}
  </div>;
};

export default DashboardPreviewPanel;
