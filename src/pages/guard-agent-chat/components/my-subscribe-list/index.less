.subscribe-list {
  padding-right: 22px;
  .subscribe-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 5px;
    cursor: pointer;

    .subscribe-item-name {
      font-size: 12px;
      line-height: 20px;
      display: flex;

      .subscribe-item-name-text {
        display: block;
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .subscribe-item-status-online {
        color: #0ABF5B;
      }

      .subscribe-item-status-offline {
        color: rgba(0, 0, 0, .8);
      }
    }

    .subscribe-item-time {
      color: rgba(0, 0, 0, .3);
    }

    // 启用状态的样式
    &.status-online {
      color: #000;
      cursor: pointer;

      &:hover {
        background-color: #f5f5f5;
        border-radius: 3px;
      }
    }

    // 停用状态的样式
    &.status-offline {
      color: rgba(0, 0, 0, .9);
      cursor: not-allowed;
      opacity: 0.6;

      &:hover {
        cursor: not-allowed;
        background-color: transparent;
      }

      .subscribe-item-name {
        color: rgba(0, 0, 0, .9);
      }

      .subscribe-item-time {
        color: rgba(0, 0, 0, .3);
      }
    }
  }
}