import React, { useEffect, useState } from 'react';
import { SelectMultiple, Collapse, Icon, Row, Col, Text, Input, TextArea, Button, message, Form } from '@tencent/tea-component';
import './index.less';
import { otherProductNode, productDict, archInfo, appId, guardInfo, unSupportedProducts, updateGuardInfo } from '@src/utils/caching';
import { CreateGuardSheet, DescribeArchGuardProductInstances } from '@src/service/api/baseInfo';
import store from '@src/origin-store/store';
import { setGuardUpdateFlagAction } from '@src/origin-store/guardAction';
import _ from 'lodash';
import { t } from '@tea/app/i18n';
// import EscortTool from '@src/pages/escortTool';
// import { nanoid } from 'nanoid';
import Loading from '@src/components/Loading';
// 发起护航抽屉
const OtherProduct = () => {
  const { drawerUpdateFlag } = store.getState().guard;
  // 已经保存的其他产品
  const savedOtherProductDesc = (guardInfo.ProductDesc || []).filter(i => unSupportedProducts.includes(i.Product));
  // 初始化全部的desc
  const initOtherProductDesc =  unSupportedProducts.map((i) => {
    // 保存的产品
    const savedDesc = savedOtherProductDesc.find(m => m.Product === i) || {};
    return {
      AppId: savedDesc.AppId || Number(appId),
      NodeUuid: '',
      Comment: savedDesc.Comment || '',
      Product: savedDesc.Product || i,
      InstanceIds: savedDesc.InstanceIds ||  '',
    };
  });

  // 全部产品
  const [allProductDesc, setAllProductDesc] = useState<any>(initOtherProductDesc);
  // 其他产品选项
  const otherProductOptions = (unSupportedProducts || []).map(i => ({ text: productDict[i], value: i }));
  // 选中的其他产品
  const [productList, setProductList] = useState<any>(savedOtherProductDesc.map(i => i.Product));
  const [loading, setLoading] = useState(false);
  // 更新ProductDesc
  function updateProductDesc(product, name, value) {
    setAllProductDesc((i) => {
      const oldValue = _.cloneDeep(i);
      const desc = oldValue.find(i => i.Product === product);
      if (desc) {
        desc[name] = value;
      }
      return oldValue;
    });
  }

  // 保存其他产品
  function saveProdectDesc() {
    // 新保存的其他产品
    const newProductDesc = allProductDesc.filter(i => productList.includes(i.Product));
    // 每一个兜底产品是否有实例
    const isFinish = newProductDesc.every(i => i.InstanceIds);
    if (!isFinish) {
      return;
    }
    const params = {
      ...guardInfo,
      ProductDesc: [...(guardInfo.ProductDesc || []).filter(i => i.IsProductSupported), ...newProductDesc],
    };
    setLoading(true);
    CreateGuardSheet(params).then(() => {
      // 更新缓存的护航单信息
      updateGuardInfo();
      message.success({ content: t('其他产品保存成功') });
      archInfo.closeDrawer();
      // 更新
      store.dispatch(setGuardUpdateFlagAction(!drawerUpdateFlag));
      // archInfo.setSlotComponent(<EscortTool uuid={nanoid()} />);
    })
      .finally(() => {
        setLoading(true);
      });
  }
  // 获取节点的实例
  function getInstance(NodeUuid) {
    return new Promise((resolve, reject) => {
      const params = {
        MapId: archInfo?.archInfo?.archId || '',
        NodeUuid,
        Offset: 0,
        Limit: 5000,
        Filter: [],
      };
      DescribeArchGuardProductInstances(params)
        .then((res: any) => {
          resolve(res || {});
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  // 其他产品实例
  function getAllInstance() {
    const resultList = [];
    // 查询每一种产品绑定的实例
    otherProductNode.map((i) => {
      resultList.push(getInstance(i.NodeUuid));
    });
    Promise.all(resultList).then((res) => {
      const obj = {};
      if (res?.length) {
        res.map((i) => {
          if (i.Product) {
          // 相同产品的实例合并到一起
            obj[i.Product] = (obj[i.Product] || []).concat(i.Instance);
          }
        });
        const oldProductDesc = _.cloneDeep(allProductDesc);
        oldProductDesc.map((i) => {
          if (obj[i.Product]?.length) {
            // 将查询到的实例和已经保存的实例合并，去重
            const resultInstanceIds =  Array.from(new Set(obj[i.Product].map(j => j.InstanceId).concat(i.InstanceIds ? i.InstanceIds.split('\n') : [])));
            i.InstanceIds = resultInstanceIds.join('\n');
          }
          return i;
        });
        // 全部的其他产品
        setAllProductDesc(_.cloneDeep(oldProductDesc));
      }
    });
  }

  useEffect(() => {
    getAllInstance();
  }, []);

  return <div className='otherProduct'>
    <div className='addProduct'>
      <div className='addLabel'>{t('添加云产品')}</div>
      <SelectMultiple
        className='otherProductSelect'
        listHeight={400}
        boxClassName='otherProductBox'
        appearance="button"
        options={otherProductOptions}
        value={productList}
        onChange={(v) => {
          setProductList(v);
        }}
        searchable
      />
    </div>

    <Collapse
      className='otherProductList'
      icon={active => (active ? <Icon type="arrowdown" /> : <Icon type="arrowright" />)}
      defaultActiveIds={unSupportedProducts}
    >
      {(productList || []).map((i) => {
        // 从全部的Product查找
        const desc = (allProductDesc || []).find(m => m.Product === i);
        // 该节点的全部搜索条件
        return <Collapse.Panel
          id={i}
          key={i}
          title={<div className='otherProductTitle'> {t('{{attr0}} 护航配置', { attr0: productDict[i] })}<span>{t('该产品未接入云护航，仅分配人员')}</span></div>}>
          <Row verticalAlign="top" style={{ marginTop: 10 }}>
            <Col span={6} ><Text theme="label">{t('护航实例')}</Text></Col>
            <Col span={18}>
              <Form.Control
                showStatusIcon={false}
                status={desc?.InstanceIds ? 'success' : 'error'}
                message={desc?.InstanceIds ? '' : t('护航实例不能为空')}
              >
                <TextArea
                  key={i}
                  size="full"
                  value={desc?.InstanceIds || ''}
                  onChange={(v) => {
                    updateProductDesc(i, 'InstanceIds', v);
                  }}
                  placeholder={t('客户未开通云顾问，或产品未接入云顾问。请手动输入护航实例，每行一个实例。如果是特殊场景没有实例列表，请说明具体情况。')}
                />
              </Form.Control>

            </Col>
          </Row>
          <Row verticalAlign="middle" style={{ marginTop: 10 }}>
            <Col span={6} >
              <Text theme="label">{t('备注')}</Text>
            </Col>
            <Col span={18} >
              <Input
                key={i}
                size='full'
                placeholder={t('护航特殊需求备注')}
                value={desc?.Comment || ''}
                onChange={(v) => {
                  updateProductDesc(i, 'Comment', v);
                }}
              />
            </Col>
          </Row>
        </Collapse.Panel>;
      })
      }
    </Collapse>

    <div className='btnWrap'>
      <Button type="primary" onClick={() => {
        saveProdectDesc();
      }}>{t('提交')}</Button>
    </div>
    {/* 全屏遮罩 */}
    <Loading show={loading}></Loading>
  </div>;
};
export default OtherProduct;
