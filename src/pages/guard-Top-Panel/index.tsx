/* eslint-disable no-nested-ternary */
import React, { useEffect, useMemo, useState, useRef, useImperativeHandle } from 'react';
import { t } from '@tea/app/i18n';
import { app } from '@tencent/tea-app';
import {
  setNodeList,
  unSupportedProducts,
  supportedProducts,
  setOtherProductNode,
  reportGuard,
} from '@src/utils/caching';
import { Button, message } from '@tencent/tea-component';
import {
  setDrawerVisibleMapAction,
} from '@src/origin-store/guardAction';
import { DescribeArchNodeGuardInfo, DescribeArchNodeTask, DescribeArchNodeTaskResult } from '@src/service/api/baseInfo';
import { queryStringObj } from '@src/utils/common';
import { useDispatch } from 'react-redux';
import { find, isEmpty, debounce, some } from 'lodash';
import { ENodeTaskStatus, guardStatusEnum } from '@src/constants';
import bandIconSvg from '@src/assets/svg/band.svg';
import guardBeforeIconSvg from '@src/assets/svg/guard-before.svg';
import PendingSvgIcon from '@src/assets/svg/pending.svg';
import resourcePanel from '@src/assets/svg/resource-panel.svg';
import importantPanel from '@src/assets/svg/important-panel.svg';
import store from '@src/origin-store/store';
import { useCommonSelector, changeCommonData } from '@src/store/app-common';
import './index.less';
import EscortDrawer from '@src/pages/escortDrawer';
import { CustomerGuardStatusMap } from '@src/pages/customer-duck-menu';

interface Iprops {
  onRef: any;
}

const GuardTopPanel = ({ onRef }: Iprops) => {
  const { guardStatus, guardInfoDetail, appId, asyncTaskId, taskResult, isUserAuth }  = useCommonSelector();
  const {
    graphApi,
  }  = store.getState().guard;
  const { uin, env, archInfo, userName } = graphApi;
  const enName = localStorage.getItem('enName') ?? '-';
  const isISA = env === 'ISA';
  const dispatch = useDispatch();
  const taskResultRef = useRef(taskResult);
  const { nodeUuid = '' }: any = queryStringObj(location?.search);

  // 点击护航资源，隐藏重点关注
  const [hidEscortImportantResource, setHidEscortImportantResource] = useState(false);
  // 点击重点关注，隐藏护航资源
  const [hidEscortResource, setHidEscortResource] = useState(false);

  // 护航资源数量
  const [totalGuardCount, setTotalGuardCount] = useState(0);
  // 重点关注数量
  const [totalImportantCount, setTotalImportantCount] = useState(0);
  // 角标数据列表
  const [nodeRiskCountList, setNodeRiskCountList] = useState([]);
  useImperativeHandle(onRef, () =>
    // 需要将暴露的接口返回出去
    ({
      startTask: handleArchNodeTaskFetch,
      getNodeInfo: getDescribeArchNodeGuardInfo,
    }));

  const escortResourceStatus = useMemo(() => {
    if (guardStatus === guardStatusEnum.GUARD_BEFORE_PARPARE) {
      return {
        icon: guardBeforeIconSvg,
        desc: isISA ? t('护航前准备') : t('需求处理中'),
        color: '#FF7200',
        alt: isISA ? t('护航前准备') : t('需求处理中'),
      };
    }
    if (guardStatus === guardStatusEnum.GUARDING) {
      return {
        icon: PendingSvgIcon,
        desc: t('护航中'),
        color: '#0abf5b',
        alt: t('护航中'),
      };
    }
    return {
      icon: bandIconSvg,
      desc: t('未在护航'),
      color: '#888888',
      disabled: guardStatus === guardStatusEnum.NO_GUARD_DRAFT,
      alt: t('发起护航'),
    };
  }, [guardStatus]);

  const handleEscortGuardInfo = (res) => {
    // 所有的节点
    const allNode = res.NodeRiskCountList || [];
    // 节点设置的节点
    const nodeSettingNode = allNode.filter(i => i.Count && supportedProducts.includes(i.Product));
    // 其他产品的节点
    const otherProductNode = allNode.filter(i => unSupportedProducts.includes(i.Product));

    dispatch(changeCommonData({
      allNodeList: allNode,
    }));
    // 保存绑定实例的节点
    setNodeList(nodeSettingNode);
    setOtherProductNode(otherProductNode);

    setTotalGuardCount(nodeSettingNode.reduce((sum, item) => sum + item.GuardCount, 0));
    setTotalImportantCount(res.TotalImportantCount || 0);

    handleNodeRiskCountList(nodeSettingNode);
    setNodeRiskCountList(nodeSettingNode);
  };

  // 获取顶栏和角标数据
  const getDescribeArchNodeGuardInfo = async () => {
    if ((isUserAuth && guardInfoDetail?.GuardId) || (!isUserAuth && !guardInfoDetail?.GuardId)) {
      const params = {
        GuardId: guardInfoDetail?.GuardId !== undefined ? guardInfoDetail.GuardId : 0,
        MapId: archInfo?.archId,
      };
      try {
        const res: any = await DescribeArchNodeGuardInfo(params);
        if (res.Error) {
          const message = res.Error.Message || '';
          app.tips.error(message);
          return;
        }
        handleEscortGuardInfo(res);
      } catch (err) {
        const message = err.msg || err.toString() || t('未知错误');
        app.tips.error(t('{{message}}', { message }));
      }
    }
  };

  const fetchArchNodeTaskResult = () => {
    DescribeArchNodeTaskResult({
      TaskId: asyncTaskId,
    } as any).then((res: any) => {
      const { ProgressValue, NodeDetail } = res;
      graphApi.setProgressParams(ProgressValue * 100, t('配置更新中'), {
        hoverText: `${Math.floor(ProgressValue * 100)}%`,
      });
      dispatch(changeCommonData({
        taskResult: res,
      }));
      if (ProgressValue === 1) {
        graphApi.setAsyncTaskStop();
        if (some(NodeDetail, item => item.Status === ENodeTaskStatus.FAILURE)) {
          message.warning({ content: t('部分节点配置信息更新失败，请稍后刷新页面重试') });
        }
        getDescribeArchNodeGuardInfo();
        setTimeout(() => {
          dispatch(changeCommonData({
            asyncTaskId: '',
          }));
        }, 10);
      }
    })
      .catch((e) => {
        console.log(e);
      });
  };

  useEffect(() => {
    let intervalId;
    if (asyncTaskId) {
      intervalId = setInterval(() => {
        fetchArchNodeTaskResult();
      }, 1000);
    }
    if (taskResult?.ProgressValue === 1) {
      clearInterval(intervalId);
    }
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [asyncTaskId, taskResult, guardInfoDetail?.GuardId]);

  const getArchGuardInfoProgress = debounce((nodeUuid) => {
    DescribeArchNodeGuardInfo({
      GuardId: guardInfoDetail?.GuardId !== undefined ? guardInfoDetail.GuardId : 0,
      MapId: archInfo?.archId,
    }).then((rs) => {
      if (rs) {
        const currentTaskResult = taskResultRef.current;
        if (!isEmpty(currentTaskResult)) {
          const nodeStatus = find(currentTaskResult?.NodeDetail, item => item?.NodeUuId === nodeUuid) || {};
          if (currentTaskResult.ProgressValue < 1) {
            if (nodeStatus.Status !== ENodeTaskStatus.SUCCESS) {
              getArchGuardInfoProgress(nodeUuid); // 递归调用依然受防抖控制
            }
            handleEscortGuardInfo(rs);
          }
        } else {
          getArchGuardInfoProgress(nodeUuid); // 递归调用依然受防抖控制
        }
      }
    });
  }, 2000);

  const handleArchNodeTaskFetch = async () => {
    DescribeArchNodeTask({
      MapId: archInfo?.archId ?? '',
      PrioritizeNodeList: nodeUuid ? [nodeUuid] : [],
      UpdatedBy: userName ? userName : enName,
      ...(isISA && { Uin: uin, AppId: appId }),
    }).then((res: any) => {
      dispatch(changeCommonData({
        asyncTaskId: res.TaskId,
      }));
      if (nodeUuid) {
        getArchGuardInfoProgress(nodeUuid);
      }
    })
      .catch((e) => {
        console.log(e);
      });
  };

  // 当 taskResult 更新时，同步到 ref
  useEffect(() => {
    taskResultRef.current = taskResult;
  }, [taskResult]);

  // 动态渲染角标
  const handleNodeRiskCountList = (nodeRiskCountList: any[]) => {
    nodeRiskCountList.forEach((item) => {
      const guardCount = item.GuardCount;
      const importantCount = item.ImportantCount;
      const exceptionCount = item.ExceptionCount || 0;
      graphApi.createBar([item.NodeUuid], {
        children: <div className="escortAvatarBox" style={{ backgroundColor: guardCount && importantCount && !hidEscortResource && !hidEscortImportantResource ? '#F4DFE1' : 'transparent' }}>
          <span className="escortAvatar escortGuardAvatar" style={{ display: guardCount && !hidEscortResource ? 'block' : 'none' }}>{guardCount.toString() > 99 ? '99+' : guardCount.toString()}</span>
          <span className="escortAvatar escortImportantAvatar" style={{ display: importantCount && !hidEscortImportantResource ? 'block' : 'none' }}>{importantCount.toString() > 99 ? '99+' : importantCount.toString()}</span>
          <span className="escortAvatar escortExceptionAvatar" style={{ display: exceptionCount ? 'block' : 'none' }}>{exceptionCount.toString() > 99 ? '99+' : exceptionCount.toString()}</span>
        </div>,
      });
    });
  };

  const handleResourceContent = () => {
    if (hidEscortResource === true) {
      setHidEscortResource(false);
    }
    setHidEscortImportantResource(!hidEscortImportantResource);
  };

  const handleImportantResourceContent = () => {
    if (hidEscortImportantResource === true) {
      setHidEscortImportantResource(false);
    }
    setHidEscortResource(!hidEscortResource);
  };

  useEffect(() => {
    handleNodeRiskCountList(nodeRiskCountList);
  }, [hidEscortResource, hidEscortImportantResource]);
  useEffect(() => {
    graphApi?.setSlotComponentV2(<div className="escortResourceWrap">
      <div className="escortResource">
        <Button
          type="weak"
          className='escortResourceButton'
          style={{ color: '#0ABF5B', backgroundColor: hidEscortImportantResource ? '#F3F4F7' : '' }}
          onClick={handleResourceContent}
        >
          <span>
            <img src={resourcePanel} />
          </span>
          <span>{t('护航资源')}</span>
          <span>{totalGuardCount}</span>
        </Button>
        <div className="segmentationLine"></div>
        <Button
          type="weak"
          className='escortResourceButton'
          style={{ color: '#006EFF', backgroundColor: hidEscortResource ? '#F3F4F7' : '' }}
          onClick={handleImportantResourceContent}
        >
          <span>
            <img src={importantPanel} />
          </span>
          <span>{t('重点关注')}</span>
          <span>{totalImportantCount}</span>
        </Button>
        <div className="segmentationLine"></div>
        <Button
          type="weak"
          style={{ color: escortResourceStatus.color }}
          className='escortResourceButton'
          disabled={!!escortResourceStatus?.disabled}
          onClick={
            () => {
              // 发起护航上报
              if (guardStatus === guardStatusEnum.GUARD_DRAFT) {
                reportGuard(0);
              }
              // 统一打开抽屉逻辑
              store.dispatch(setDrawerVisibleMapAction({}));
              graphApi.setDrawerProps({
                title: CustomerGuardStatusMap[guardStatus].desc,
                children: <EscortDrawer />,
                className: 'escortDrawerWrap',
                extra: { outerClickClosable: false },
              });
              graphApi.openDrawer();
            }
          }
        >
          <span>
            <img src={escortResourceStatus.icon} alt={escortResourceStatus.alt} />
          </span>
          <span>{escortResourceStatus.desc}</span>
        </Button>
      </div>
    </div>);
  }, [
    hidEscortImportantResource,
    totalGuardCount,
    hidEscortResource,
    totalImportantCount,
    escortResourceStatus,
    guardStatus,
    taskResult,
  ]);

  return <></>;
};
export default GuardTopPanel;
