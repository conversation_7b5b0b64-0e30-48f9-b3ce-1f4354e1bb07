.escortResourceWrap {
  .escortResource {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 6px;

    .escortResourceButton {
      border: 0;
      padding: 0;
      display: flex;
      align-items: center;
      font-size: 12px;
      font-weight: 500;
      
      &:nth-child(1),&:nth-child(3) {
        padding: 0 6px;
        margin-left: -6px;
        margin-right: -6px;
        border-radius: 4px;
      }

      &:focus {
        background-color: inherit;
      }

      &:hover {
        background-color: inherit;;
        /* 使用继承的颜色，!important确保覆盖其他样式 */
      }

      span {
        &:nth-child(1) {
          font-size: 0;
          display: flex;
          align-items: center;
          img {
            width: 14px;
          }
        }
        &:nth-child(2) {
          margin: 0 5px;
        }
      }
    }

    .segmentationLine {
      width: 1px;
      height: 16px;
      background: #cfd5de;
      margin: 0px 11px;
    }
  }
}
.escortAvatarBox {
  display: flex;
  padding: 3px;
  align-items: center;
  justify-content: flex-start;
  border-radius: 16px;
  // safari 角标会异常
  // opacity: 0.8;
  background-color: rgba(0, 0, 0, 0);
  box-sizing: border-box;

  .escortAvatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 600;
    font-family: Inter;
    text-align: center;
    line-height: 30px;
  }

  .escortGuardAvatar {
    background-color: #0ABF5B;
  }

  .escortImportantAvatar {
    background-color: #006EFF;
    margin-left: 3px;
  }

  .escortExceptionAvatar {
    background-color: #dd4241;
    margin-left: 3px;
  }
}