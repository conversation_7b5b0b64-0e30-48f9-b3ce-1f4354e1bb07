.escortDrawerWrap {
  width: 725px !important;

  .app-cloud-arch-drawer__body-inner{
    height: 100%;
  }
  .tea-search__tips{
    margin-top: -12.5px !important;
  }

  .verticalSteps {
    .tea-step__item {
      display: flex !important;

      .tea-step__num {
        border: 2px solid #E5F0FF !important;
      }

      .tea-step__num,
      .tea-step__arrow {
        background-color: #E5F0FF !important;
      }

      .tea-step__content {
        flex: 1 1 auto;
        margin-left: 10px;
        overflow: hidden;
        .tea-step__title{
          max-width: none;
        }
      }
    }
  }

  .typeTabWrap {
    font-size: 12px !important;

    .tea-tabs__tabitem {
      font-size: 12px;

      .tabHead {
        display: flex;
        align-items: center;

        .tabName {
          margin-right: 10px;
        }
      }
    }
  }

  .typeTip {
    margin: 20px 0;
  }

  .serviceWrap {
    .tea-accordion {
      display: flex;

      .tea-accordion__header {
        display: flex;
        width: 30%;
        font-weight: 600;
      }

      .tea-accordion__body {
        flex: 1;
        overflow: hidden;
        position: relative;
        top: -4px
      }
    }
  }


  .tea-icon {
    background-size: auto;
  }

  .tea-datetimepicker__input:after {
    background-size: auto
  }

  .tea-datepicker__input:after {
    background-size: auto
  }

  .tea-drawer__body-inner {
    height: 100%;
  }

  .escortDrawer {
    height: 100%;
    display: flex;
    flex-direction: column;

    .escortDrawerContent {
      margin-top: 10px;
      flex: 1;
      overflow: auto;
      overflow-x: hidden;
      padding-right: 15px;

      &::-webkit-scrollbar {
        width: 2px;
        height: 6px;
      }
  
      &::-webkit-scrollbar-thumb {
        border-radius: 20px;
        background: #888;
      }
  
      &::-webkit-scrollbar-track {
        border-radius: 20px;
      }
    }
  }

  .saveAffix {
    border-top: 1px solid #ebebeb;
    padding: 20px;
    background: #fff;
    text-align: right;

    button {
      margin-left: 10px;
    }
  }


  .datePickerWrap {
    width: 100%;

    .tea-datepicker__input,
    .tea-input {
      width: 100% !important;
    }

  }

  .antoolTask{
    margin-bottom: 10px;
    display: inline-block;
    font-weight: 600;
    .link{
      text-decoration: underline;
    }
  }
}