import React, { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import {
  Stepper,
  Row,
  Col,
  Text,
  Tabs,
  Collapse,
  Tree,
  Icon,
  Bubble,
  message,
  SelectMultiple,
  Select,
} from '@tencent/tea-component';
import { archInfo, updateGuardInfo, unSupportedProducts, productDict } from '@src/utils/caching';
import { t } from '@tea/app/i18n';
import { simpleMarkdownToHTML, buildNestedArray, getStatu, getProcessEnv } from '@src/utils/common';
import { DescribeGuardServiceDetails, CreateGuardSheet, DescribeOtherPlatformGuardSheet, CreateAntoolTask } from '@src/service/api/baseInfo';
// import EscortTool from '@src/pages/escortTool';
// import { nanoid } from 'nanoid';
import Loading from '@src/components/Loading';
import ResourceList from '@src/components/resourceList';
import './index.less';
import store from '@src/origin-store/store';
import { setHistoryGuardAction, setGuardUpdateFlagAction } from '@src/origin-store/guardAction';
// 护航未填写字段
const noSet = <Text style={{ color: 'LightGray' }} verticalAlign="middle">{t('未填写')}</Text>;

interface Iprops {
  updateIndex?: (index: string) => void;
}

// 护航第三步：信息确认
const Confirm = ({ updateIndex }: Iprops, ref) => {
  const { drawerVisibleMap, drawerUpdateFlag } = store.getState().guard;
  const isISA = archInfo.env === 'ISA';
  // 护航单信息
  const [guardInfo, setGuardInfo] = useState<any>({});
  // 服务目录
  const [treeData, setTreeData] = useState([]);
  // 默认勾选的服务
  const [initServiceId, setInitServiceId] = useState([]);
  // 必填的服务
  const [requiredServiceId, setRequiredServiceId] = useState([]);
  // 当前勾选的服务
  const [selectIds, setSelectIds] = useState([]);
  // 是否是紧急护航
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [emergency, setEmergency] = useState(false);
  const [loading, setLoading] = useState(false);
  // 护航类型tab
  const tabs = [
    {
      id: '0',
      label: <div className='tabHead'><span className='tabName'>{t('标准护航')}</span></div>,
      disabled: guardInfo.Standard !== '0',
    },
    {
      id: '1',
      label: <div className='tabHead'><span className='tabName'>{t('售中迁云割接护航')}</span></div>,
      disabled: guardInfo.Standard !== '1',
    },
    {
      id: '3',
      label: <div className='tabHead'><span className='tabName'>{t('自助护航')}</span></div>,
      disabled: guardInfo.Standard !== '3',
    },
  ];
    // 其他产品选项
  const otherProductOptions = (unSupportedProducts || []).map(i => ({
    text: productDict[i],
    value: i,
  }));
    // 选中的其他产品
  const [productList, setProductList] = useState<any>([]);
  // 历史护航列表
  const [historyList, setHistoryList] = useState([]);
  // 选择的历史护航项目
  const [historyGuard, setHistoryGuard] = useState(null);

  const steps = [
    {
      id: 'info',
      label: <div className='stepLabelWrap'>
      <div>{t('护航项目信息')}</div>
     {drawerVisibleMap.historyDrawerVisible && <div className='historyList'>
        <div className='selectLabel'>{t('历史护航')}</div>
        <Select
          className='historySelect'
          searchable
          matchButtonWidth
          size="full"
          appearance="button"
          options={historyList}
          value={historyGuard}
          onChange={value => setHistoryGuard(value)}
        />
      </div>}
      </div>,
      detail: (
        <>
          <Row verticalAlign="middle">
            <Col span={3} ><Text theme="label">{t('护航名称')}</Text></Col>
            <Col span={21} ><Text theme="text">{guardInfo.GuardName}</Text></Col>
          </Row>
          <Row verticalAlign="top">
            <Col span={3} ><Text theme="label">{t('护航需求')}</Text></Col>
            <Col span={21}><Text theme="text">{guardInfo.StatementOfNeeds || noSet}</Text></Col>
          </Row>
          <Row verticalAlign="top">
            <Col span={3} ><Text theme="label">{t('护航项目')}</Text></Col>
            <Col span={21}><Text theme="text">{guardInfo.StatementOfNeeds || noSet}</Text></Col>
          </Row>
          <Row verticalAlign="middle">
            <Col span={3} ><Text theme="label">{t('开始时间')}</Text></Col>
            <Col span={9} ><Text theme="text">{guardInfo.StartTime}</Text></Col>
            <Col span={3} ><Text theme="label">{t('结束时间')}</Text></Col>
            <Col span={9} ><Text theme="text">{guardInfo.EndTime}</Text></Col>
          </Row>
          <Row verticalAlign="top">
            <Col span={3} ><Text theme="label">{t('重保日期')}</Text></Col>
            <Col span={21}><Text theme="text">{(guardInfo.ImportantGuardTime || []).join(', ') || noSet}</Text></Col>
          </Row>
          <Row verticalAlign="top">
            <Col span={3} ><Text theme="label">{t('驻场日期')}</Text></Col>
            <Col span={21}><Text theme="text">{(guardInfo.OnsiteTime || []).join(', ') || noSet}</Text></Col>
          </Row>
          <Row verticalAlign="middle">
            <Col span={3} ><Text theme="label">{t('客户接口人')}</Text></Col>
            <Col span={21}><Text theme="text">{guardInfo.CustomerContacts || noSet}</Text></Col>
          </Row>
          <Row verticalAlign="middle">
            <Col span={3} ><Text theme="label">{t('联系方式')}</Text></Col>
            <Col span={21}><Text theme="text">{guardInfo.CustomerPhone || noSet}</Text></Col>
          </Row>
          {isISA && guardInfo.Status > 1 && <Row verticalAlign="middle">
            <Col span={3} ><Text theme="label">{t('Antool任务')}</Text></Col>
            <Col span={21}><Text theme="text">{getAntoolUrl()}</Text></Col>
          </Row>}
        </>
      ),
    },
    {
      id: 'type', label: t('护航类型'), detail: (
        <>
          <Tabs className='typeTabWrap' tabs={tabs} activeId={String(guardInfo.Standard)} ></Tabs>
          <div className='typeTip'>{getTypeTip()}</div>
          <Row verticalAlign={'top'}>
            <Col span={6}>
              <Text theme="label" verticalAlign="middle">
                {t('护航阶段｜护航服务')}
              </Text>
            </Col>
            <Col span={18}>
              <div style={{ width: '100%' }} className='serviceWrap'>
                {
                  treeData?.length > 0
                  && <Collapse defaultActiveIds={treeData.map(i => i.id)} icon={active => <Icon type={active ? 'arrowdown' : 'arrowright'} />}>
                    {treeData.map(i => <>
                        <Collapse.Panel id={i.id} title={i.content}>
                          {i.children?.length > 0 && <Tree
                            selectable
                            style={{ maxWidth: '100%' }}
                            data={i.children}
                            selectedIds={selectIds}
                            defaultExpandAll
                            selectValueMode='onlyLeaf'
                          />}
                        </Collapse.Panel>
                        <div style={{ height: 1, background: '#eee', margin: '10px 0 16px 0' }}></div>
                      </>)}
                  </Collapse>
                }
              </div>
            </Col>
          </Row>
        </>
      ),
    },
    {
      id: 'config',
      label: t('护航配置信息'),
      detail: (
        <>
          <Row verticalAlign="middle">
            <Col span={3} ><Text theme="label">{t('播报时段')}</Text></Col>
            <Col span={10}><Text theme="text">{`${guardInfo.StartClock} - ${guardInfo.EndClock}`}</Text></Col>
            <Col span={4} ><Text theme="label">{t('每日巡检')}</Text></Col>
            <Col span={3} ><Text theme="text">{guardInfo.CronType === 0 ? t('未开启') : t('开启')}</Text></Col>
          </Row>
          <Row verticalAlign="top">
            <Col span={3} ><Text theme="label">{t('封网需求')}</Text></Col>
            <Col span={21}><Text theme="text">{guardInfo.ClosedNetworkDemand || noSet}</Text></Col>
          </Row>
          <Row verticalAlign={'middle'} style={{ margin: '4px 0 4px -10px' }}>
            <Col span={3} ><Text theme="label" verticalAlign="middle">{t('放量预估')}</Text></Col>
            <Col span={18}><Text theme="text">{t('对比近 {{ExpectedEnlargeDays}} 天业务放量 {{ExpectedEnlargeTimes}} 倍', { ExpectedEnlargeDays: guardInfo.ExpectedEnlargeDays, ExpectedEnlargeTimes: guardInfo.ExpectedEnlargeTimes })}</Text></Col>
          </Row>
          <Row verticalAlign="top">
            <Col span={3} ><Text theme="label">{t('限流降级')}</Text></Col>
            <Col span={21}><Text theme="text">{guardInfo.LimitStrategy || noSet}</Text></Col>
          </Row>
          <Row verticalAlign="top">
            <Col span={3} ><Text theme="label">{t('业务压测')}</Text></Col>
            <Col span={21}><Text theme="text">{guardInfo.PressureTestPlan || noSet}</Text></Col>
          </Row>
          <Row verticalAlign="top">
            <Col span={3} ><Text theme="label">{t('应急预案')}</Text></Col>
            <Col span={21}><Text theme="text">{guardInfo.BusinessEmergencyPlan || noSet}</Text></Col>
          </Row>
        </>
      ),
    },
    {
      id: 'list',
      label: <>{t('护航资源列表')}</>,
      detail: <ResourceList></ResourceList>,
    },
    {
      id: 'otherProduct',
      label: <>{t('其他产品')}</>,
      detail: <SelectMultiple
        style={{ width: '100%' }}
        disabled
        appearance="button"
        options={otherProductOptions}
        value={productList}
      />,
    },
    {
      id: 'operation',
      detail: <></>,
    },
  ];

  function createAntoolTask() {
    // 判断是否有antool平台信息
    const antoolPlatformInfo = guardInfo?.CloudGuardBaseInfoOtherPlatform?.find(i => i.Platform === 'Antool');
    // 如果不是运营端，或者有antool信息，就不调接口回写
    if (!isISA || antoolPlatformInfo) {
      return;
    }
    const params = {
      GuardId: parseInt(guardInfo.GuardId, 10),
      GuardName: guardInfo.GuardName,
      StartTime: guardInfo.StartTime,
      EndTime: guardInfo.EndTime,
      Operator: archInfo?.userName || '',
    };
    CreateAntoolTask(params)
      .then(() => {})
      .catch((err) => {
        console.log(err);
      });
  }

  // 获取antool链接
  function getAntoolUrl() {
    // 当前环境
    const env = getProcessEnv();
    const antoolPlatformInfo = guardInfo?.CloudGuardBaseInfoOtherPlatform?.find(i => i.Platform === 'Antool');
    const URL = `https://${env === 'production' ? '' : 'test-'}antool.woa.com/fe-base/antool-page/visit/info?taskId=${antoolPlatformInfo?.SubTaskId}&processInstanceId=${antoolPlatformInfo?.PlatformUniqueId}`;
    if (antoolPlatformInfo?.SubTaskId) {
      return <a href={URL} target="_blank" rel="noreferrer" className='link'>{antoolPlatformInfo?.SubTaskId}</a>;
    }
    return <Text style={{ color: 'LightGray' }} verticalAlign="middle">{t('暂无')}</Text>;
  }

  // 暴露给父组件的数据
  useImperativeHandle(ref, () => ({
    save: (prev) => {
      if (prev) {
        updateIndex?.('prev');
        return;
      }
      const params = { ...guardInfo };
      // 转换为 2  订单已提交
      params.Status = 2;
      setLoading(true);
      CreateGuardSheet(params).then((res: any) => {
        if (res.Id) {
          // 创建antool任务
          createAntoolTask();
          updateGuardInfo();
          message.success({ content: t('护航单提交成功') });
          archInfo.closeDrawer();
          // archInfo.setSlotComponent(<EscortTool uuid={nanoid()} />);
          store.dispatch(setGuardUpdateFlagAction(!drawerUpdateFlag));
        }
      })
        .finally(() => {
          setLoading(false);
        });
    },
  }));


  // 查询当前护航单数据
  function getGuardSheet() {
    DescribeOtherPlatformGuardSheet({ Filters: [{ Name: 'platform_unique_id', Values: [archInfo?.archInfo?.archId] }] })
      .then((res: any) => {
        // 记录历史护航单
        setHistoryList((res.Guard || []).map((i) => {
          i.value = i.GuardId;
          i.text = i.GuardName;
          return i;
        }));
        // 最后一条护航单
        const guard = res.Guard?.length ? res.Guard[0] : {};
        // 如果最后一条已经护航完成，则不缓存
        setGuardInfo(getStatu(guard) === 'finish' ? {} : guard);
        setProductList((guard.ProductDesc || []).map(i => i.Product));
      })
      .catch((err) => {
        console.log(err);
      });
  }

  // 护航类型提示信息
  function getTypeTip() {
    switch (guardInfo.Standard) {
      case '0':
        return <Text theme="warning">{t('注意：护航账号需要转售，否则无法发起，没有 TAM 进行支持，如果售中护航请选择迁云割接护航')}</Text>;
      case '1':
        return <Text theme="warning">{t('注意：迁云割接护航适合售中、迁云场景，没有售后角色。')}</Text>;
      case '3':
        return <Text theme="warning">{t('注意：自助护航会略过售后和专项等人员审批，')}<span style={{ textDecoration: 'underline' }}>{t('快速自动生成巡检报告、监控、播报等服务')}</span>{t('。此时，自助护航')}<span style={{ textDecoration: 'underline' }}>{t('不会支持售后（如未勾选护航服务群）和专家等人员服务')}</span>{t('，您可以自助提工单以获取对应服务。')}</Text>;
      default:
        return '';
    }
  }

  // 查询护航类型对应的服务
  function getEscortTypeService() {
    setTreeData([]);
    DescribeGuardServiceDetails({ Standard: parseInt(guardInfo.Standard) })
      .then((res: any) => {
        const temp = (res.Data || []).map(i => ({
          id: String(i.Id),
          parentId: String(i.ParentId),
          content: <div style={{ display: 'flex' }}>
            <div>{i.ServiceName}</div>
            {i.ServiceDesc && (
              i.ServiceDescType === 'infologo'
                ? <Bubble overlayStyle={{ width: 200 }} arrowPointAtCenter placement="right" content={i.ServiceDesc}>	<Icon type="info" style={{ marginLeft: 4, position: 'relative', top: -1 }} /></Bubble>
                : <div dangerouslySetInnerHTML={{ __html: simpleMarkdownToHTML(i.ServiceDesc) }}></div>
            )
            }
          </div>,
          disableSelect: i.ShowStatus === -1,
          selected: (i.Id === 27 && guardInfo.Standard === '3') ? (!!emergency) : (i.ShowStatus === 1 || i.ShowStatus === -1),
          required: i.ShowStatus === -1,
        }));
        setTreeData(buildNestedArray(temp, 'id', 'parentId'));
        setRequiredServiceId(temp.filter(i => i.required).map(i => i.id));
        setInitServiceId(temp.filter(i => i.selected).map(i => i.id));
      })
      .catch((err) => {
        console.log(err);
      });
  }

  useEffect(() => {
    // 打开的是历史护航，则默认选中第一个
    drawerVisibleMap.historyDrawerVisible && setHistoryGuard(historyList?.[0]?.GuardId);
  }, [historyList]);

  useEffect(() => {
    if (historyGuard) {
      const guardData = historyList.find(i => i.GuardId === historyGuard) || {};
      setProductList((guardData.ProductDesc || []).map(i => i.Product));
      setGuardInfo(guardData);
      // 选择的历史护航记录到store里
      store.dispatch(setHistoryGuardAction(guardData));
    }
  }, [historyGuard]);

  useEffect(() => {
    getGuardSheet();
  }, []);

  useEffect(() => {
    if (guardInfo.GuardId) {
      getEscortTypeService();
    }
  }, [guardInfo]);

  useEffect(() => {
    // 只有客户保存过服务并且当前护航类型和已保存的护航类型一致，才会展示客户保存过的服务
    if (guardInfo.GuardService?.length) {
      // 客户保存的服务和必填服务并集
      const savedServiceId = guardInfo.GuardService.map(i => String(i.ID));
      setSelectIds(Array.from(new Set([...savedServiceId, ...requiredServiceId])));
    } else {
      setSelectIds(initServiceId);
    }
  }, [initServiceId]);

  return <>
    <Stepper
      className='detailInfoWrap verticalSteps'
      type="process-vertical-dot"
      steps={steps}
    />
    <Loading show={loading}/>
  </>;
};
export default forwardRef(Confirm);
