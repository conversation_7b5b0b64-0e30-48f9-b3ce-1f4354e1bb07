import React, { useEffect, useState, useImperativeHandle, forwardRef, useRef } from 'react';
import {
  Stepper,
  Row,
  Col,
  Text,
  Input,
  DatePicker,
  Tag,
  Tabs,
  Collapse,
  Checkbox,
  Tree,
  Icon,
  Bubble,
  message,
  Form,
  Select,
  Button,
  Switch,
} from '@tencent/tea-component';
import './index.less';
import moment from 'moment';
import _ from 'lodash';
import { app } from '@tea/app';
import {
  DescribeGuardServiceDetails,
  CreateGuardSheet,
  UpdateGuardSheet,
  DescribeGuardProjects,
  DescribeArchGuardProductInstances,
  ModifyShareStatus,
  DescribeOtherPlatformGuardSheet,
  CopyOneArchGuard,
  DescribeOnsiteGuardConfig,
  UpdateOnsiteGuardApprovalDirector,
} from '@src/service/api/baseInfo';
import { simpleMarkdownToHTML, buildNestedArray } from '@src/utils/common';
import RTXPicker from '@tencent/qmfe-yoa-react-ui/es/RTXPicker/index';
import { guardInfo, archInfo, updateGuardInfo, appId, appIdInfo, nodeList, otherProductNode, unSupportedProducts } from '@src/utils/caching';
import { t } from '@tea/app/i18n';
// import EscortTool from '@src/pages/escortTool';
import { nanoid } from 'nanoid';
import Loading from '@src/components/Loading';
import ResourceList from '@src/components/resourceList';
import EscortDrawer from '@src/pages/escortDrawer/index';
import store from '@src/origin-store/store';
import { setDrawerVisibleMapAction, setGuardUpdateFlagAction } from '@src/origin-store/guardAction';
const { TextArea } = Input;
const { RangePicker } = DatePicker;

interface IProps {
  updateIndex?: (index: string) => void;
  isEdit?: boolean;
  editList?: Array<any>,
  copyId?: number
}
enum ConfirmAuthorStatus {
  'NONE' = 0,
  'CONFIRM' = 1,
  'NOAUTHOR'= 2,
}

// 护航第一步：基本信息
const BaseInfo = ({ updateIndex, isEdit = false, editList = [], copyId }: IProps, ref) => {
  // 护航启动 ref
  const resourceListRef = useRef(null);
  const { drawerUpdateFlag } = store.getState().guard;
  // 是否是运营端
  const isISA = archInfo.env === 'ISA';
  const isConfirmAuthor = guardInfo?.CustomerAuthStatus === ConfirmAuthorStatus.CONFIRM;
  const [otherProductDesc, setOtherProductDesc] = useState<any>((guardInfo.ProductDesc || [])
    .filter(i => unSupportedProducts.includes(i.Product)));
  // 初始化productDesc,有护航资源才保存
  const nodeProductDesc = nodeList.filter(node => node.GuardCount)
    .map(node => ({
      AppId: Number(appId),
      NodeUuid: node.NodeUuid,
      Product: node.Product,
      Comment: (guardInfo.ProductDesc || []).find(i => i.NodeUuid === node.NodeUuid)?.Comment || '',
    }));
  // 架构图id
  const archId = archInfo?.archInfo?.archId || '';
  // 环境
  const env = archInfo?.env || '';
  // 当前用户
  const rtx = archInfo?.userName || '';
  const [accountName, setAccountName] = useState('');

  // 护航名称
  const [guardName, setGuardName] = useState(guardInfo.GuardName || t('{{CustomerName}}护航需求{{CurrentTime}}', { CustomerName: appIdInfo.CustomerName, CurrentTime: moment().format('YYYYMMDDHH') }));
  // 护航需求
  const [statementOfNeeds, setStatementOfNeeds] = useState(guardInfo.StatementOfNeeds || '');
  // 护航项目
  const [project, setProject] = useState(guardInfo.Project > 0 ? guardInfo.Project.toString() : '');
  // 护航开始时间 如果开始时间小于当前时间，则默认+2h
  const [startTime, setStartTime] = useState(moment(guardInfo.StartTime).isAfter(moment()) ? moment(guardInfo.StartTime) : moment().add(2, 'hours')
    .startOf('hour'));
  // 护航结束时间 如果结束时间小于当前时间，则结束时间默认+1d
  const [endTime, setEndTime] = useState(moment(guardInfo.EndTime).isAfter(moment()) ? moment(guardInfo.EndTime) : moment().add(1, 'days')
    .startOf('hour'));
  // 重保日期
  const [importantGuardTime, setImportantGuardTime] = useState(guardInfo.ImportantGuardTime || []);
  // 驻场时间
  const [onsiteDates, setOnsiteDates] = useState(guardInfo.OnsiteTime || []);
  // 客户接口人
  const [customerContacts, setCustomerContacts] = useState(guardInfo.CustomerContacts || '');
  // 联系电话
  const [customerPhone, setCustomerPhone] = useState(guardInfo.CustomerPhone || '');
  // 护航类型 转售账号默认选择标准护航，未转售默认选择售中迁云割接护航
  const [standard, setStandard] = useState(guardInfo.Standard >= 0 ? guardInfo.Standard.toString() : (appIdInfo.HasAfterSale ? '0' : '1'));
  // 服务目录
  const [treeData, setTreeData] = useState([]);
  // 默认勾选的服务
  const [initServiceId, setInitServiceId] = useState([]);
  // 必填的服务
  const [requiredServiceId, setRequiredServiceId] = useState([]);
  // 当前勾选的服务
  const [selectIds, setSelectIds] = useState([]);
  // 是否是紧急护航
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [emergency, setEmergency] = useState(false);
  // 护航项目选项
  const [projectOptions, setProjectOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  // 资源是否填写完毕
  const [resourceFinish, setResourceFinish] = useState(false);
  // 确认客户授权巡检
  const [confirmAuthor, setConfirmAuthor] = useState<boolean>(isConfirmAuthor);

  const [errorLayerVisible, setErrorLayerVisible] = useState(false);

  // 复制护航单开关
  const [copySwitch, setCopySwitch] = useState(!!copyId);
  // 可复制的护航单列表
  const [copyList, setCopyList] = useState([]);
  // 选择的复制护航单
  const [copyGuard, setCopyGuard] = useState(null);

  // 驻场护航配置
  const [onsiteGuardConfig, setOnsiteGuardConfig] = useState<any>({});
  // 总监
  const [leaderName, setLeaderName] = useState(onsiteGuardConfig?.CreateDirector || '');
  // 当前选择驻场天数 是否大于单次上限
  const [isMoreThanOne, setIsMoreThanOne] = useState(false);
  // 当前选择驻场天数 是否大于单次上限
  const [isMoreThanYear, setIsMoreThanYear] = useState(false);

  useEffect(() => {
    if (onsiteGuardConfig.TamDirector) {
      setIsMoreThanOne(onsiteDates?.length > (onsiteGuardConfig?.OneGuardMaxOnsiteDays || 0));
      setIsMoreThanYear((onsiteDates?.length || 0) + (onsiteGuardConfig?.AppidYearOnsiteDays || 0)
        > (onsiteGuardConfig?.OneYearMaxOnsiteDays || 0) && onsiteDates?.length > 0);
    }
  }, [onsiteDates, onsiteGuardConfig]);

  useEffect(() => {
    app.user.current().then((res) => {
      setAccountName(res?.displayName);
    });
  }, []);

  function getDescribeOnsiteGuardConfig() {
    // 缓存驻场护航配置信息
    DescribeOnsiteGuardConfig({
      AppId: appId,
      Operator: rtx,
      GuardId: guardInfo.GuardId || 0,
    }).then((res: any) => {
      setOnsiteGuardConfig(res.OnsiteGuardConfig || {});
      setLeaderName(res?.OnsiteGuardConfig?.CreateDirector);
    })
      .catch((err) => {
        console.log(err);
      });
  }

  // 获取总监审批提示
  function leaderTip() {
    const reason = t('- {{attr0}} {{attr1}}需发起人总监+售后总监审批', { attr0: isMoreThanOne ? t('驻场护航天数大于{{attr0}}天，', { attr0: onsiteGuardConfig?.OneGuardMaxOnsiteDays }) : '', attr1: isMoreThanYear ? t('驻场护航天数累计超{{attr0}}天，', { attr0: onsiteGuardConfig?.OneYearMaxOnsiteDays }) : '' });
    return <>
    <div>{reason}</div>
    <div>{t('- 审批人：{{attr0}};{{attr1}}', { attr0: onsiteGuardConfig.CreateDirector, attr1: onsiteGuardConfig.TamDirector })}</div>
    <div>{t('（发起人总监若有误可修改）')}</div>
  </>;
  }

  	// 更新总监
	 function updateLeader(id) {
    UpdateOnsiteGuardApprovalDirector({
      AppId: appId,
      GuardId: id,
      CreateDirector: leaderName,
      TamDirector: onsiteGuardConfig?.TamDirector,
    }).then(() => {})
      .catch((err) => {
        console.log(err);
      });
  }

  useEffect(() => {
    copyList?.length && setCopyGuard(copyId || null);
  }, [copyList]);

  useEffect(() => {
    if (isISA) {
      let showData: any = {};
      if (copyGuard) {
        // 如果选择了复制护航单，就显示复制护航单的数据，不然后就展示缓存中最新的护航单
        const guardData = copyList.find(i => i.GuardId === copyGuard) || {};
        showData = _.cloneDeep(guardData);
        // 重新渲染护航服务
      } else {
        showData = guardInfo;
      }
      getEscortTypeService(showData.Standard);
      setGuardName(showData.GuardName);
      setStatementOfNeeds(showData.StatementOfNeeds);
      setProject(showData.Project > 0 ? showData.Project.toString() : '');
      if (copyGuard) {
        setStartTime(moment(showData.StartTime));
        setEndTime(moment(showData.EndTime));
      } else {
        // 不是复制护航单，就展示最新时间或护航单的时间
        setStartTime(moment(guardInfo.StartTime).isAfter(moment()) ? moment(guardInfo.StartTime) : moment().add(2, 'hours')
          .startOf('hour'));
        setEndTime(moment(guardInfo.EndTime).isAfter(moment()) ? moment(guardInfo.EndTime) : moment().add(1, 'days')
          .startOf('hour'));
      }
      setImportantGuardTime(showData.ImportantGuardTime || []);
      setOnsiteDates(showData.OnsiteTime);
      setCustomerContacts(showData.CustomerContacts);
      setCustomerPhone(showData.CustomerPhone);
      // eslint-disable-next-line no-nested-ternary
      setStandard(showData.Standard >= 0 ? showData.Standard.toString() : (appIdInfo.HasAfterSale ? '0' : '1'));
      setSelectIds(showData.GuardService || []);
    }
  }, [copyGuard]);

  // 护航类型tab
  const tabs = [
    {
      id: '0', label: <div className='tabHead'>
        <span className='tabName'>{t('标准护航')}</span>
        <Checkbox name="0" value={standard === '0'} onClick={() => {
          setStandard('0');
        }} disabled={!appIdInfo.HasAfterSale || noEdit('standard')}></Checkbox>
      </div>,
      disabled: !appIdInfo.HasAfterSale || noEdit('standard'),
    },
    {
      id: '1', label: <div className='tabHead'>
        <span className='tabName'>{t('售中迁云割接护航')}</span>
        <Checkbox name="0" value={standard === '1'} onClick={() => {
          setStandard('1');
        }} disabled={noEdit('standard')}></Checkbox>
      </div>,
      disabled: noEdit('standard'),
    },
    {
      id: '3', label: <div className='tabHead'>
        <span className='tabName'>{t('自助护航')}</span>
        <Checkbox name="0" value={standard === '3'} onClick={() => {
          setStandard('3');
        }} disabled={noEdit('standard')}></Checkbox>
      </div>,
      disabled: noEdit('standard'),
    },
  ];

  // 不能修改的字段判断
  function noEdit(name) {
    const editMap = {
      importantGuardTime: 'ImportantGuardTime',
      guardName: 'GuardName',
      StatementOfNeeds: 'StatementOfNeeds',
      project: 'Project',
      startTime: 'StartTime',
      endTime: 'EndTime',
      onsiteDates: 'OnsiteTime',
      customerContacts: 'CustomerContacts',
      customerPhone: 'CustomerPhone',
      standard: 'Standard',
      selectIds: 'GuardServiceIds',
    };

    if (isEdit && editList?.length) {
      return !editList.includes(editMap[name]);
    }
    return false;
  }

  // 查询护航项目清单
  function getProjects() {
    DescribeGuardProjects().then((res: any) => {
      const tmp = [];
      res.Projects.map((i) => {
        tmp.push({ value: i.Id.toString(), text: i.Name });
      });
      setProjectOptions(tmp);
    });
  }

  // 护航类型提示信息
  function getTypeTip() {
    switch (standard) {
      case '0':
        return <Text theme="warning">{t('注意：护航账号需要转售，否则无法发起，没有 TAM 进行支持，如果售中护航请选择迁云割接护航')}</Text>;
      case '1':
        return <Text theme="warning">{t('注意：迁云割接护航适合售中、迁云场景，没有售后角色。')}</Text>;
      case '3':
        return <Text theme="warning">{t('注意：自助护航会略过售后和专项等人员审批，')}<span style={{ textDecoration: 'underline' }}>{t('快速自动生成巡检报告、监控、播报等服务')}</span>{t('。此时，自助护航')}<span style={{ textDecoration: 'underline' }}>{t('不会支持售后（如未勾选护航服务群）和专家等人员服务')}</span>{t('，您可以自助提工单以获取对应服务。')}</Text>;
      default:
        return '';
    }
  }

  // 查询护航类型对应的服务
  function getEscortTypeService(oldStandard = null) {
    setTreeData([]);
    DescribeGuardServiceDetails({ Standard: parseInt(oldStandard || standard, 10) })
      .then((res: any) => {
        const temp = (res.Data || []).map(i => ({
          id: String(i.Id),
          parentId: String(i.ParentId),
          content: <div style={{ display: 'flex' }}>
            <div>{i.ServiceName}</div>
            {i.ServiceDesc && (
              i.ServiceDescType === 'infologo'
                ? <Bubble overlayStyle={{ width: 200 }} arrowPointAtCenter placement="right" content={i.ServiceDesc}>	<Icon type="info" style={{ marginLeft: 4, position: 'relative', top: -1 }} /></Bubble>
                :                <div dangerouslySetInnerHTML={{ __html: simpleMarkdownToHTML(i.ServiceDesc) }}></div>
            )
            }
          </div>,
          disableSelect: (i.Id === 27 && standard === '3' && !appIdInfo.HasAfterSale) ? true : i.ShowStatus === -1,
          selected: (i.Id === 27 && standard === '3') ? (!!emergency) : (i.ShowStatus === 1 || i.ShowStatus === -1),
          required: i.ShowStatus === -1,
        }));
        setTreeData(buildNestedArray(temp, 'id', 'parentId'));
        setRequiredServiceId(temp.filter(i => i.required).map(i => i.id));
        setInitServiceId(temp.filter(i => i.selected).map(i => i.id));
      })
      .catch((err) => {
        console.log(err);
      });
  }

  // 获取节点的实例
  function getInstance(NodeUuid) {
    return new Promise((resolve, reject) => {
      const params = {
        MapId: archId,
        NodeUuid,
        Offset: 0,
        Limit: 2000,
        Filter: [],
      };
      DescribeArchGuardProductInstances(params)
        .then((res: any) => {
          resolve(res || {});
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  // 其他产品实例
  function getAllInstance() {
    const tempProductDesc = [];
    const resultList = [];
    // 查询每一种产品绑定的实例
    otherProductNode.map((i) => {
      resultList.push(getInstance(i.NodeUuid));
    });
    Promise.all(resultList).then((res) => {
      const obj = {};
      if (res?.length) {
        res.map((i) => {
          if (i.Product) {
            // 相同产品的实例合并到一起
            obj[i.Product] = (obj[i.Product] || []).concat(i.Instance);
          }
        });
        const keys = Object.keys(obj);
        keys.map((i) => {
          // 将查询到的实例和已经保存的实例合并，去重
          const oldInstanceIds = (guardInfo.ProductDesc || []).find(m => m.Product === i)?.InstanceIds || '';
          const resultInstanceIds = obj[i].map(j => j.InstanceId).concat(oldInstanceIds ? oldInstanceIds.split('\n') : []);
          // 只有产品绑定了实例才能默认保存
          if (obj[i]?.length) {
            tempProductDesc.push({
              AppId: Number(appId),
              NodeUuid: '',
              Comment: '',
              Product: i,
              InstanceIds: (Array.from(new Set(resultInstanceIds))).join('\n'),
            });
          }
        });
        // 合并ProductDesc
        const resultProductDesc = [
          // 过滤掉tempProductDesc中有的
          ...otherProductDesc.filter(other => !tempProductDesc.some(temp => other.Product === temp.Product)),
          ...tempProductDesc,
        ];
        // 初始化
        setOtherProductDesc(_.cloneDeep(resultProductDesc));
      }
    });
  }

  // 查询当前护航单数据
  function getGuardSheet() {
    DescribeOtherPlatformGuardSheet({ Filters: [{ Name: 'platform_unique_id', Values: [archInfo?.archInfo?.archId] }] })
      .then((res: any) => {
        // 记录历史护航单
        setCopyList((res.Guard || [])
          .filter(i => i.GuardId !== guardInfo.GuardId)
          .map((i) => {
            i.value = i.GuardId;
            i.text = i.GuardName;
            return i;
          }));
      })
      .catch((err) => {
        console.log(err);
      });
  }

  useEffect(() => {
    if (isISA) {
      // 获取驻场护航配置
      getDescribeOnsiteGuardConfig();
      getGuardSheet();
      getProjects();
      // 只有ProductDesc为空数组时，才需要用图上完整的兜底产品的ProductDesc
      !guardInfo.ProductDesc?.length && getAllInstance();
    }
  }, []);

  useEffect(() => {
    // 护航类型变化，去查询对应的服务
    if (isISA) {
      getEscortTypeService();
    }
  }, [standard]);

  useEffect(() => {
    // 只有客户保存过服务并且当前护航类型和已保存的护航类型一致，才会展示客户保存过的服务
    if (guardInfo.GuardService?.length && guardInfo.Standard === parseInt(standard)) {
      // 客户保存的服务和必填服务并集
      const savedServiceId = guardInfo.GuardService.map(i => String(i.ID));
      setSelectIds(Array.from(new Set([...savedServiceId, ...requiredServiceId])));
    } else {
      setSelectIds(initServiceId);
    }
  }, [initServiceId]);

  // 复制护航单
  function copyGuardMethod(next = false) {
    setLoading(true);
    CopyOneArchGuard({
      GuardId: guardInfo.GuardId,
      FromGuardId: copyGuard,
      Operator: rtx,
    })
      .then(() => {
        // 更新缓存的护航单信息
        updateGuardInfo();
        next && updateIndex && updateIndex('next');
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  }

  useImperativeHandle(ref, () => ({
    save: (next = true, submit = false, saveNode = false) => {
      setErrorLayerVisible(false);
      // 必填项校验,项目只在运营端做校验
      // 驻场护航时间大于单次或者一年时，要校验负责人
      if (!guardName || (isISA && !project) || !startTime || ((isMoreThanOne || isMoreThanYear) && !leaderName)) {
        setErrorLayerVisible(true);
        return;
      }
      // 当是运营端，当护航时间大于7天后，要校验重保护航必填
      if (isISA && moment(endTime.format('YYYY-MM-DD')).diff(moment(startTime.format('YYYY-MM-DD')), 'days') > 7 && !importantGuardTime.length) {
        return;
      }
      if (!isEdit && moment(startTime).isBefore(moment())) {
        message.error({ content: t('护航日期必须大于等于当前时间，请填写有效的护航时间。') });
        return;
      }
      // 当是运营端，账号未转售，且护航类型不是售中迁云割接或自助护航时，不允许建单
      if (isISA && !appIdInfo.HasAfterSale && standard !== '1' && standard !== '3') {
        return;
      }
      if (!isISA && !confirmAuthor) {
        message.error({ content: t('必须确认客户已授权') });
        return;
      }
      // 租户端要校验资源列表
      if (saveNode) {
        // 如果资源信息没有填写完，无法下一步
        if (!resourceFinish || !guardInfo?.Products?.length) {
          message.warning({ content: t('请完善护航资源') });
          return;
        }
        // 子组件保存完节点后，进行保存
        resourceListRef.current.save(submit)
          .then(() => createGuard(next, submit))
          .catch(() => {});
      } else {
        createGuard(next, submit);
      }
    },
  }));

  function createGuard(next, submit) {
    const PlatformInfo = (guardInfo.CloudGuardBaseInfoOtherPlatform || [])
      .find(i => i.PlatformUniqueId === archInfo.archInfo?.archId) || {};
    let params = {
      ...guardInfo,
      CreatedBy: guardInfo.CreatedBy ? guardInfo.CreatedBy : rtx, // 创建人，如果是新建护航单，创建人不变
      UpdatedBy: rtx, // 更新人
      GuardId: guardInfo.GuardId || 0,
      Status: 1, // 草稿
      MainAppId: parseInt(appId),
      CustomerName: appIdInfo?.CustomerName?.trim() || '',
      Platform: PlatformInfo?.Platform || env,
      PlatformUniqueId: PlatformInfo?.PlatformUniqueId || archId,
      GuardName: guardName,
      StatementOfNeeds: statementOfNeeds,
      Project: parseInt(project),
      StartTime: startTime.format('YYYY-MM-DD HH:mm:ss'),
      EndTime: endTime.format('YYYY-MM-DD HH:mm:ss'),
      ImportantGuardTime: importantGuardTime,
      OnsiteTime: onsiteDates,
      CustomerContacts: customerContacts.trim(),
      CustomerPhone: customerPhone.trim(),
      Standard: parseInt(standard),
      GuardServiceIds: selectIds.map(i => Number(i)),
      // 传空数组，防止云护航报错
      OnsiteProducts: guardInfo.OnsiteProducts || [],
      ProductDesc: [...nodeProductDesc, ...otherProductDesc],
      RelatedAppId: guardInfo.RelatedAppId || [],
      RelatedCustomerNames: guardInfo.RelatedCustomerNames || [],
      Products: guardInfo.Products || [],
    };
    // 适配租户端传参
    if (!isISA) {
      params = {
        CustomerName: appIdInfo?.CustomerName?.trim() || '',
        GuardId: guardInfo.GuardId,
        Status: guardInfo.Status || 1,
        Platform: PlatformInfo?.Platform || env,
        PlatformUniqueId: PlatformInfo?.PlatformUniqueId || archId,
        GuardName: guardInfo.GuardName,
        StatementOfNeeds: statementOfNeeds,
        StartTime: startTime.format('YYYY-MM-DD HH:mm:ss'),
        EndTime: endTime.format('YYYY-MM-DD HH:mm:ss'),
        OnsiteTime: onsiteDates,
        CustomerContacts: customerContacts.trim(),
        CustomerPhone: customerPhone.trim(),
        CustomerAuthStatus: confirmAuthor ? ConfirmAuthorStatus.CONFIRM : ConfirmAuthorStatus.NOAUTHOR,
        AccountName: accountName,
      };
      // 点击提交，要添加字段。只有租户端这里会有提交的操作
      if (submit) {
        params.PlatformStatus = 2;
      } else {
        params.PlatformStatus = 1;
      }
    }
    setLoading(true);
    // 区分是新建还是编辑
    const methods = isEdit ? UpdateGuardSheet(params) : CreateGuardSheet(params);
    methods.then((res: any) => {
      if (res.Id) {
        message.success({ content: t('保存成功') });
        if (isISA) {
        // 新建护航单时，保存总监
          !isEdit && updateLeader(res.Id);
          // 修改弹框标志
          store.dispatch(setDrawerVisibleMapAction({ infoChangeDrawerVisible: false }));
          // 如果是复制，则执行复制逻辑
          if (copySwitch && copyGuard) {
            copyGuardMethod(next);
          } else {
          // 更新缓存的护航单信息
            updateGuardInfo();
            next && updateIndex && updateIndex('next');
          }
        } else {
        // 更新缓存的护航单信息
          updateGuardInfo();
          // 更改图的协作状态
          ModifyShareStatus({
            ArchId: archId,
            ShareLevel: 3,
            ShareStatus: true,
            Username: rtx,
          }).catch((err) => {
            console.log(err);
          });
          if (submit) {
          // 设置发起护航抽屉内容
            archInfo.setDrawerProps({
              title: t('发起护航'),
              children: <EscortDrawer uuid={nanoid()} />,
              className: 'escortDrawerWrap',
              extra: { outerClickClosable: false },
            });
          }
        }
        // 更新
        store.dispatch(setGuardUpdateFlagAction(!drawerUpdateFlag));
        // archInfo.setSlotComponent(<EscortTool uuid={nanoid()} />);
      }
    })
      .finally(() => {
        setLoading(false);
      });
  }

  const steps: any = [
    {
      id: 'info',
      label: <div className='baseInfoLabel'>
            <div>{t('护航项目信息')}</div>
          {isISA && <div className='copyWrap'>
              <Switch disabled={isEdit} value={copySwitch} onChange={(val) => {
                if (!val) {
                  // 如果关闭复制开关，清空选择的护航单
                  setCopyGuard(null);
                }
                setCopySwitch(val);
              }}>{t('复制信息')}</Switch>
               <Select
                className='copySelect'
                clearable
                disabled={!copySwitch}
                searchable
                matchButtonWidth
                appearance="button"
                options={copyList}
                value={copyGuard}
                onChange={value => setCopyGuard(value)}
              />
            </div>}
      </div>,
      detail: (
        <>
          {isISA && <Row verticalAlign="top">
            <Col span={3} >
              <Text theme="label">{t('护航名称')}</Text>
              <Text style={{ color: 'red', marginLeft: 4 }} verticalAlign="middle">*</Text>
            </Col>
            <Col span={21}>
              <Bubble placement="top" content={t('不能为空')} visible={!guardName && errorLayerVisible} error>
                <Input
                  size='full'
                  disabled={noEdit('guardName')}
                  value={guardName}
                  onChange={(value) => {
                    setGuardName(value);
                  }}
                  placeholder={t('请输入护航名称')}
                />
              </Bubble>
            </Col>
          </Row>}
          <Row verticalAlign="top">
            <Col span={3} ><Text theme="label">{t('护航需求')}</Text></Col>
            <Col span={21}>
              <TextArea
                size='full'
                rows={3}
                disabled={noEdit('StatementOfNeeds')}
                value={statementOfNeeds}
                onChange={(value) => {
                  setStatementOfNeeds(value);
                }}
                placeholder={t('请输入护航需求')}
              />
            </Col>
          </Row>
          {isISA && <Row verticalAlign="top">
            <Col span={3} >
              <Text theme="label">{t('护航项目')}</Text>
              <Text style={{ color: 'red', marginLeft: 4 }} verticalAlign="middle">*</Text>
            </Col>
            <Col span={21}>
              <Bubble placement="top" content={t('不能为空')} visible={!project && errorLayerVisible} error>
                <Select
                  disabled={noEdit('project')}
                  value={project}
                  options={projectOptions}
                  onChange={(v) => {
                    setProject(v);
                  }}
                  size="full"
                  appearance="button"
                  searchable
                />
              </Bubble>
            </Col>
          </Row>}
          {isEdit ? <Row verticalAlign="middle">
          <Col span={3} >
            <Text theme="label">{t('开始时间')}</Text>
            <Text style={{ color: 'red', marginLeft: 4 }} verticalAlign="middle">*</Text>
          </Col>
          <Col span={9}>
            <Bubble placement="top" content={t('不能为空')} visible={!startTime && errorLayerVisible} error>
              <DatePicker
                  className='datePickerWrap'
                  showTime
                  disabled={noEdit('startTime')}
                  value={startTime}
                  range={[moment(), moment(endTime)]}
                  onChange={(value) => {
                    setStartTime(value);
                    // 要清空重保日期和驻场日期
                    setImportantGuardTime([]);
                    setOnsiteDates([]);
                  }}
                />
            </Bubble>
          </Col>
          <Col span={3} ><Text theme="label">{t('结束时间')}</Text></Col>
          <Col span={9}>
            <DatePicker
              className='datePickerWrap'
              showTime
              disabled={noEdit('endTime')}
              value={endTime}
              range={[moment(startTime), null]}
              onChange={(value) => {
                setEndTime(value);
                // 要清空重保日期和驻场日期
                setImportantGuardTime([]);
                setOnsiteDates([]);
              }}
            />
          </Col>
        </Row>
            : <Row verticalAlign="middle">
            <Col span={3} >
              <Text theme="label">{t('护航时间')}</Text>
              <Text style={{ color: 'red', marginLeft: 4 }} verticalAlign="middle">*</Text>
            </Col>
            <Col span={9}>
              <Bubble placement="top" content={t('不能为空')} visible={!startTime && errorLayerVisible} error>
                <RangePicker
                  className='RangePicker'
                  showTime
                  value={[startTime, endTime]}
                  range={[moment(), null]}
                  onChange={(v) => {
                    setStartTime(v[0]);
                    setEndTime(v[1]);
                    setImportantGuardTime([]);
                    setOnsiteDates([]);
                  }}
                />
              </Bubble>
            </Col>
          </Row>}

          {/* 护航时间大于7天才支持选择护航重点时间 */}
          {(moment(endTime.format('YYYY-MM-DD')).diff(moment(startTime.format('YYYY-MM-DD')), 'days') > 7 && isISA)
            && <Row verticalAlign="top">
              <Col span={3} ><Text theme="label">{t('重保日期')}</Text></Col>
              <Col span={21}>
                <Form.Control
                  showStatusIcon={false}
                  status={importantGuardTime?.length ? 'success' : 'error'}
                  message={importantGuardTime?.length ? '' : t('重保日期不能为空')}
                >
                  <DatePicker
                    disabled={noEdit('importantGuardTime')}
                    className='datePickerWrap'
                    range={[startTime, endTime]}
                    onChange={(value) => {
                      const tmp = _.cloneDeep(importantGuardTime);
                      const d = value.format('YYYY/MM/DD');
                      if (tmp.indexOf(d) === -1) {
                        tmp.push(d);
                      }
                      setImportantGuardTime(tmp);
                    }}
                  />
                </Form.Control>
                {importantGuardTime.length > 0 && (
                  <div style={{ margin: '10px 0' }}>
                    {importantGuardTime.map((i, index) => (
                        <Tag
                          onClose={() => {
                            !noEdit('importantGuardTime') && setImportantGuardTime(importantGuardTime.filter((j) => {
                              if (j !== i) {
                                return j;
                              }
                            }));
                          }}
                          key={index}
                        >
                          {i}
                        </Tag>
                    ))}
                  </div>
                )}
              </Col>
            </Row>}
          <Row verticalAlign="top">
            <Col span={3} ><Text theme="label">{t('驻场日期')}</Text></Col>
            <Col span={21}>
              <DatePicker
                disabled={noEdit('onsiteDates')}
                className='datePickerWrap'
                range={[startTime, endTime]}
                onChange={(value) => {
                  const tmp = _.cloneDeep(onsiteDates);
                  const d = value.format('YYYY/MM/DD');
                  if (tmp.indexOf(d) === -1) {
                    tmp.push(d);
                  }
                  setOnsiteDates(tmp);
                }}
              />
              {onsiteDates.length > 0 && (
                <div style={{ margin: '10px 0' }}>
                  {onsiteDates.map((i, index) => (
                      <Tag
                        onClose={() => {
                          !noEdit('onsiteDates') && setOnsiteDates(onsiteDates.filter((j) => {
                            if (j !== i) {
                              return j;
                            }
                          }));
                        }}
                        key={index}
                      >
                        {i}
                      </Tag>
                  ))}
                </div>
              )}
            </Col>
          </Row>
         { (isMoreThanOne || isMoreThanYear) && <Row verticalAlign="middle" gap={20}>
            <Bubble placement="top" content={t('不能为空')} visible={!leaderName && errorLayerVisible} error>
              <Col span={14}>
                <div className='leaderWrap'>
                  <div className='labelWrap'>
                    <Text theme="label" className='leaderLabel'>{t('总监审批')}</Text>
                    <Text style={{ color: 'red', marginLeft: 4 }} verticalAlign="middle">*</Text>
                  </div>
                  <RTXPicker
                    placeholder={t('请输入需要添加的协作人英文ID')}
                    value={leaderName}
                    onChange={(name: string) => setLeaderName(name)}
                  />
                </div>
              </Col>
            </Bubble>
            <Col span={8}>
              <div className='leaderWrap'>
                  <Text theme="label" className='tamLabel'>{t('售后总监')}</Text>
                  <Text>{onsiteGuardConfig?.TamDirector}</Text>
              </div>
            </Col>
            <Col span={1}>
                  <Bubble
                    placement='top-end'
										dark
										overlayClassName='leaderBubble'
										arrowPointAtCenter
										content={leaderTip()}
									  >
										<Icon type="info" />
									</Bubble>
            </Col>
          </Row>}
          <Row verticalAlign="top">
            <Col span={3} ><Text theme="label">{t('客户接口人')}</Text></Col>
            <Col span={21}>
              <Input
                size='full'
                disabled={noEdit('customerContacts')}
                value={customerContacts}
                onChange={(value) => {
                  setCustomerContacts(value);
                }}
                placeholder={t('请输入客户接口人')}
              />
            </Col>
          </Row>
          <Row verticalAlign="top">
            <Col span={3} ><Text theme="label">{t('联系方式')}</Text></Col>
            <Col span={21}>
              <Input
                size='full'
                disabled={noEdit('customerPhone')}
                value={customerPhone}
                onChange={(value) => {
                  setCustomerPhone(value);
                }}
                placeholder={t('请输入联系方式')}
              />
            </Col>
          </Row>
          {!isISA && <Row verticalAlign="top">
            <Col span={21}>
              <Checkbox
                style={{ marginTop: 15 }}
                display="block"
                value={confirmAuthor}
                onChange={(value) => {
                  setConfirmAuthor(value);
                }}
              >
                <Text style={{ color: 'red' }} verticalAlign="middle">*</Text>
                <Text style={{ color: 'blue' }}>
                  {/* 我授权开启架构图协作（授权说明）以支持本次护航对绑定资源运行巡检等应用 */}
                  {t('我授权开启架构图协作')}
                  （<a target='_blank' rel='noopener noreferrer' href='https://cloud.tencent.com/document/product/301/98644'>{t('授权说明')}</a>）
                  {t('以支持本次护航对绑定资源运行巡检等应用。')}
                </Text>
              </Checkbox>
            </Col>
          </Row>}
        </>
      ),
    },
    {
      id: 'type', label: t('护航类型'), detail: (
        <>
          <Tabs className='typeTabWrap' tabs={tabs} activeId={standard} onActive={({ id }) => {
            setStandard(id);
          }}></Tabs>
          <div className='typeTip'>{getTypeTip()}</div>
          <Row verticalAlign={'top'}>
            <Col span={6}>
              <Text theme="label" verticalAlign="middle">
                {t('护航阶段｜护航服务')}
              </Text>
            </Col>
            <Col span={18}>
              <div style={{ width: '100%' }} className='serviceWrap'>
                {
                  treeData?.length > 0
                  && <Collapse defaultActiveIds={treeData.map(i => i.id)} icon={active => <Icon type={active ? 'arrowdown' : 'arrowright'} />}>
                    {treeData.map(i => <>
                        <Collapse.Panel id={i.id} title={i.content}>
                          {i.children?.length > 0 && <Tree
                            style={{ maxWidth: '100%' }}
                            data={i.children}
                            selectable={!(isEdit && noEdit('selectIds'))}
                            selectedIds={selectIds}
                            defaultExpandAll
                            selectValueMode='onlyLeaf'
                            onSelect={(value) => {
                              setSelectIds(value);
                            }}
                          />}
                        </Collapse.Panel>
                        <div style={{ height: 1, background: '#eee', margin: '10px 0 16px 0' }}></div>
                      </>)}
                  </Collapse>
                }
              </div>
            </Col>
          </Row>
        </>
      ),
    },
    {
      id: 'operation',
      detail: <></>,
    },
  ];
  // 如果是租户端，不展示护航类型，展示资源列表
  if (!isISA) {
    steps[1] = {
      id: 'list',
      label: <>
        {t('护航资源列表（进入')}
        <Button
          type="link"
          style={{ fontSize: 14, verticalAlign: 'baseline' }}
          onClick={() => {
            store.dispatch(setDrawerVisibleMapAction({}));
            // 设置节点设置抽屉
            archInfo.setDrawerProps({
              title: t('节点设置'),
              children: <ResourceList canEdit={true} />,
              className: 'nodeSettingWrap',
              extra: { outerClickClosable: false },
            });
            // 打开抽屉
            archInfo.openDrawer();
          }}
        >
          {t('节点设置')}
        </Button>
        {t('调整护航资源）')}
      </>,
      detail: <ResourceList consoleBaseInfo={true} ref={resourceListRef} statusChange={(finish) => {
        setResourceFinish(finish);
      }}></ResourceList>,
    };
  }

  return <>
    <Stepper className='baseInfoWrap verticalSteps' type="process-vertical-dot" steps={steps} />
    <Loading show={loading} />
  </>;
};
export default forwardRef(BaseInfo);
