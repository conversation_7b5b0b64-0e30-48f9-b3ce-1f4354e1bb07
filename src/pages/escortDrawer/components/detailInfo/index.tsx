import React, { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import { Stepper, Row, Col, Text, Input, SelectMultiple, InputNumber, Button, Icon, Bubble, message, TimeRangePicker, Switch, Modal, Select } from '@tencent/tea-component';
import { archInfo, updateGuardInfo, productDict, unSupportedProducts } from '@src/utils/caching';
import moment, { isMoment } from 'moment';
import { CreateGuardSheet, DescribeOtherPlatformGuardSheet } from '@src/service/api/baseInfo';
import { t } from '@tea/app/i18n';
import ResourceList from '@src/components/resourceList';
import OtherProduct from '@src/pages/otherProduct';
import { getStatu } from '@src/utils/common';
import Loading from '@src/components/Loading';
import store from '@src/origin-store/store';
import { setDrawerVisibleMapAction } from '@src/origin-store/guardAction';
const { TextArea } = Input;
const TIME_FORMAT_HM = 'HH:mm';
// 返回左闭右闭有序列表
const range = (s, t) => Array(t - s + 1)
  .fill(0)
  // eslint-disable-next-line @typescript-eslint/naming-convention
  .map((_, i) => s + i);

interface IProps  {
  updateIndex?: (index: string) => void;
}
// 护航第二步：详细信息
const DetailInfo = ({ updateIndex }: IProps, ref) => {
  // 护航信息
  const [guardInfo, setGuardInfo] = useState<any>({});
  // 播报开始时间
  const [startClock, setStartClock] = useState(moment('20:00', TIME_FORMAT_HM));
  // 播报结束时间
  const [endClock, setEndClock] = useState(moment('23:59', TIME_FORMAT_HM));
  // 每日巡检
  const [cronType, setCronType] = useState(0);
  // 封网需求
  const [closeNetworkDemand, setCloseNetworkDemand] = useState('');
  // 封网需求开关
  const [showBlockModal, setShowBlockModal] = useState(!!closeNetworkDemand);
  // 封网提示弹框
  const [blockVisible, setBlockVisible] = useState(false);
  // 放量预估选项
  const expectedEnlargeDaysOptions = [
    { text: t('近3天'), value: '3' },
    { text: t('近7天'), value: '7' },
    { text: t('近15天'), value: '15' },
    { text: t('近30天'), value: '30' },
  ];
  // 整体放量预估天数
  const [expectedEnlargeDays, setExpectedEnlargeDays] = useState<number>(7);
  // 整体放量预估倍数
  const [expectedEnlargeTimes, setExpectedEnlargeTimes] = useState<number>(1.5);
  // 柔性限流降级策略
  const [limitStrategy, setLimitStrategy] = useState<string>('');
  // 业务压测计划
  const [pressureTestPlan, setPressureTestPlan] = useState<string>('');
  // 业务主链路应急预案
  const [businessEmergencyPlan, setBusinessEmergencyPlan] = useState<string>('');
  // 资源是否填写完毕
  const [resourceFinish, setResourceFinish] = useState(false);
  // 其他产品选项
  const otherProductOptions = (unSupportedProducts || []).map(i => ({
    text: productDict[i],
    value: i,
  }));
  // 选中的其他产品
  const [productList, setProductList] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const steps = [
    {
      id: 'config',
      label: t('护航配置信息'),
      detail: (
        <>
          <Row verticalAlign="middle">
            <Col span={3} ><Text theme="label">{t('播报时段')}</Text></Col>
            <Col span={10}>
              <TimeRangePicker
                className='datePickerWrap'
                value={[startClock, endClock]}
                format={TIME_FORMAT_HM}
                disableAutoAdjust={true}
                disabledTime={(time, partial) => {
                  const [start] = time;
                  if (partial === 'start') {
                    return {
                      disabledMinutes: () => range(1, 59),
                    };
                  }
                  if (partial === 'end' && isMoment(start)) {
                    return {
                      disabledHours: () => range(0, start.hour()),
                      disabledMinutes: () => range(0, 58),
                    };
                  }
                  return {};
                }}
                onChange={(value) => {
                  setStartClock(value[0]);
                  setEndClock(value[1]);
                }}
              />
            </Col>
            <Col span={4} >
              <Text theme="label">{t('每日巡检')}</Text>
              <Bubble
                arrowPointAtCenter
                placement="top"
                content={t('开启后，护航期间将自动发起护航巡检和生成新的护航报告')}
              >
                <Icon type="info" style={{ marginLeft: 4, verticalAlign: 'top' }} />
              </Bubble>
            </Col>
            <Col span={3}>
              <Switch
                value={cronType !== 0}
                onChange={(val) => {
                  if (val) {
                    setCronType(1);
                  } else {
                    setCronType(0);
                  }
                }}
              />
            </Col>
          </Row>
          <Row verticalAlign="top">
            <Col span={3} >
              <Text theme="label">{t('封网需求')}</Text>
              <Switch
                value={showBlockModal}
                onChange={(val) => {
                  if (val) {
                    setBlockVisible(true);
                  } else {
                    setCloseNetworkDemand('');
                    setShowBlockModal(false);
                  }
                }}
              />
            </Col>
            <Col span={21}>
              <TextArea
                rows={3}
                size='full'
                disabled={!showBlockModal}
                onChange={(value) => {
                  setCloseNetworkDemand(value);
                }}
                value={closeNetworkDemand}
                placeholder={t('说明需要封网管控发布变更的地区和时间')}
              />
            </Col>
          </Row>
          <Row verticalAlign={'middle'} style={{ margin: '4px 0 4px -10px' }}>
            <Col span={3} >
              <Text theme="label" verticalAlign="middle">{t('放量预估')}</Text>
            </Col>
            <Col span={18}>
              <Row verticalAlign={'middle'}>
                <Text style={{ padding: '6px 10px' }}>{t('相比')}</Text>
                <Select
                  size="s"
                  appearance='button'
                  options={expectedEnlargeDaysOptions}
                  value={expectedEnlargeDays.toString()}
                  onChange={(v) => {
                    setExpectedEnlargeDays(parseInt(v));
                  }}
                />
                <Text style={{ padding: '6px 10px' }}>{t('增加')}</Text>
                <InputNumber
                  style={{ margin: 5 }}
                  onChange={(value) => {
                    setExpectedEnlargeTimes(value);
                  }}
                  value={expectedEnlargeTimes}
                  min={1}
                  step={0.1}
                  size="m"
                />
                <Text>{t('倍')}</Text>
              </Row>
            </Col>
          </Row>
          <Row verticalAlign="top">
            <Col span={3} ><Text theme="label">{t('限流降级')}</Text></Col>
            <Col span={21}>
              <TextArea
                size='full'
                rows={3}
                value={limitStrategy}
                onChange={(value) => {
                  setLimitStrategy(value);
                }}
                placeholder={t('柔性限流降级策略')}
              />
            </Col>
          </Row>
          <Row verticalAlign="top">
            <Col span={3} ><Text theme="label">{t('业务压测')}</Text></Col>
            <Col span={21}>
              <TextArea
                size='full'
                rows={3}
                value={pressureTestPlan}
                onChange={(value) => {
                  setPressureTestPlan(value);
                }}
                placeholder={t('业务压测计划')}
              />
            </Col>
          </Row>
          <Row verticalAlign="top">
            <Col span={3} ><Text theme="label">{t('应急预案')}</Text></Col>
            <Col span={21}>
              <TextArea
                size='full'
                rows={3}
                value={businessEmergencyPlan}
                onChange={(value) => {
                  setBusinessEmergencyPlan(value);
                }}
                placeholder={t('业务主链路应急预案')}
              />
            </Col>
          </Row>
        </>
      ),
    },
    {
      id: 'list',
      label: <>{t('护航资源列表（进入')} <Button
        type="link"
        style={{ fontSize: 14, verticalAlign: 'baseline' }}
        onClick={() => {
          store.dispatch(setDrawerVisibleMapAction({}));
          // 设置节点设置抽屉
          archInfo.setDrawerProps({
            title: t('节点设置'),
            children: <ResourceList canEdit={true} />,
            className: 'nodeSettingWrap',
            extra: { outerClickClosable: false },
          });
          // 打开抽屉
          archInfo.openDrawer();
        }}
      >{t('节点设置')}</Button> {t('调整护航资源）')}</>,
      detail: <ResourceList statusChange={(finish) => {
        setResourceFinish(finish);
      }}></ResourceList>,
    },
    {
      id: 'otherProduct',
      label: <>
        {t('其他产品（产品未接入云护航，需手动输入护航实例。可进入')}
        <Button
          type="link"
          style={{ fontSize: 14, verticalAlign: 'baseline' }}
          onClick={() => {
            store.dispatch(setDrawerVisibleMapAction({}));
            // 设置其它产品抽屉
            archInfo.setDrawerProps({
              title: t('其他产品'),
              children: <OtherProduct />,
              className: 'otherProductWrap',
              extra: { outerClickClosable: false },
            });
            // 打开抽屉
            archInfo.openDrawer();
          }}
        >
          {t('其他产品')}
        </Button> {t('录入并保存）')}
        </>,
      detail: <SelectMultiple
        style={{ width: '100%' }}
        disabled
        appearance="button"
        options={otherProductOptions}
        value={productList}
      />,
    },
    {
      id: 'list',
    },
  ];

  // 查询护航单
  function getGuard() {
    // 查询架构图对应的护航单信息
    DescribeOtherPlatformGuardSheet({ Filters: [{ Name: 'platform_unique_id', Values: [archInfo.archInfo?.archId] }] })
      .then((res: any) => {
        // 最后一条护航单
        const guard = res.Guard?.length ? res.Guard[0] : {};
        // 如果最后一条已经护航完成，则不缓存
        setGuardInfo(getStatu(guard) === 'finish' ? {} : guard);
      });
  }

  useEffect(() => {
    // 获取最新的护航单
    getGuard();
  }, []);

  useEffect(() => {
    setStartClock(moment(guardInfo.StartClock ? guardInfo.StartClock : '20:00', TIME_FORMAT_HM));
    setEndClock(moment(guardInfo.EndClock ? guardInfo.EndClock : '23:59', TIME_FORMAT_HM));
    setCronType(guardInfo.CronType || 0);
    setCloseNetworkDemand(guardInfo.ClosedNetworkDemand || '');
    setShowBlockModal(!!closeNetworkDemand);
    setExpectedEnlargeDays(guardInfo.ExpectedEnlargeDays || 7);
    setExpectedEnlargeTimes(guardInfo.ExpectedEnlargeTimes || 1.5);
    setLimitStrategy(guardInfo.LimitStrategy || '');
    setPressureTestPlan(guardInfo.PressureTestPlan || '');
    setBusinessEmergencyPlan(guardInfo.BusinessEmergencyPlan || '');
    setProductList((guardInfo.ProductDesc || []).map(i => i.Product));
  }, [guardInfo]);

  useImperativeHandle(ref, () => ({
    save: (next = true, prev = false) => {
      // 如果资源信息没有填写完，无法下一步
      if (!resourceFinish || !guardInfo?.Products?.length) {
        message.warning({ content: t('请完善护航资源') });
        return;
      }
      const params = {
        ...guardInfo,
        StartClock: startClock.format(TIME_FORMAT_HM),
        EndClock: endClock.format(TIME_FORMAT_HM),
        CronType: cronType,
        ClosedNetworkDemand: closeNetworkDemand,
        ExpectedEnlargeDays: expectedEnlargeDays,
        ExpectedEnlargeTimes: expectedEnlargeTimes,
        LimitStrategy: limitStrategy,
        PressureTestPlan: pressureTestPlan,
        BusinessEmergencyPlan: businessEmergencyPlan,
      };
      setLoading(true);
      CreateGuardSheet(params).then((res: any) => {
        if (res.Id) {
          updateGuardInfo();
          message.success({ content: t('保存成功') });
          next && updateIndex && updateIndex('next');
          prev && updateIndex && updateIndex('prev');
        }
      })
        .finally(() => {
          setLoading(false);
        });
    },
  }));

  return <>
    <Stepper className='detailInfoWrap verticalSteps' type="process-vertical-dot" steps={steps} />
    <Modal
      visible={blockVisible}
      caption={t('特别提示')}
      onClose={() => {
        setBlockVisible(false);
      }}
    >
      <Modal.Body>
        <div>
          <Text theme={'text'}>{t('根据客户实际情况评估是否需要封网，如有需求：')}</Text>
        </div>
        <div>
          <Text theme={'text'}>
            {t('1、请确认按照')}
            <a
              href="https://csig.lexiangla.com/teams/k100054/docs/3c4398280cd111eab7100a58ac1312dd?company_from=csig"
              target={'_blank'} rel="noreferrer"
            >
              {t('《腾讯云封网流程》')}
            </a>
            {t('申请封网；')}
          </Text>
        </div>
        <div>
          <Text theme={'text'}>{t('2、护航单中说明需要管控发布变更的地区和时间')}</Text>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button
          type="primary"
          onClick={() => {
            setShowBlockModal(true);
            setBlockVisible(false);
          }}
        >
          {t('确定')}
        </Button>
        <Button
          type="weak"
          onClick={() => {
            setBlockVisible(false);
          }}
        >
          {t('取消')}
        </Button>
      </Modal.Footer>
    </Modal>
    <Loading show={loading} />
  </>;
};
export default forwardRef(DetailInfo);
