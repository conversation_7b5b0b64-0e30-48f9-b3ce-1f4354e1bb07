/* eslint-disable no-nested-ternary */
import React, { useEffect, useRef, useState } from 'react';
import { Stepper, Affix, StatusTip, Button, Text, Bubble } from '@tencent/tea-component';
import { archInfo, reportGuard } from '@src/utils/caching';
import BaseInfo from './components/baseInfo';
import DetailInfo from './components/detailInfo';
import Confirm from './components/confirm';
import './index.less';
import GuardStatu from '@src/components/GuardStatu';
import { DescribeOtherPlatformGuardSheet } from '@src/service/api/baseInfo';
import { getStatu, processState, getProcessEnv } from '@src/utils/common';
import { t, Trans, Slot } from '@tea/app/i18n';
import store from '@src/origin-store/store';
import { setDrawerVisibleMapAction } from '@src/origin-store/guardAction';
// 护航步骤
const steps = [
  { id: 'baseInfo', label: t('护航启动') },
  { id: 'detailInfo', label: t('详细信息') },
  { id: 'confirm', label: t('信息确认') },
];

interface Iprops {
  uuid?: string;
  currentnStep?: string;
  copyId?: number
}

// 发起护航抽屉
const EscortDrawer = ({ uuid = '', currentnStep = 'baseInfo', copyId }: Iprops) => {
  // 当前环境
  const env = getProcessEnv();
  // 是否是运营端
  const isISA = archInfo.env === 'ISA';
  const [guardInfo, setGuardInfo] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const antoolPlatformInfo = guardInfo?.CloudGuardBaseInfoOtherPlatform?.find(i => i.Platform === 'Antool');
  const URL = `https://${env === 'production' ? '' : 'test-'}antool.woa.com/fe-base/antool-page/visit/info?taskId=${antoolPlatformInfo?.SubTaskId}&processInstanceId=${antoolPlatformInfo?.PlatformUniqueId}`;
  const approvalStep = [
    {
      id: 'step1',
      label: isISA ? t('护航项目审批信息') : t('护航需求处理中'),
      detail: <>
      {
        isISA &&  antoolPlatformInfo?.SubTaskId && <Text className='antoolTask'  theme="text">
           <Trans>已生成antool任务单(
                <a href={URL} target="_blank" rel="noreferrer" className='link'><Slot content={antoolPlatformInfo?.SubTaskId} /></a>
                )</Trans>
            </Text>
      }
      {!isISA && <>
        <Text theme="text" parent='div'>{t('腾讯云服务专家工程师已收悉您的护航需求，正在为您处理，请稍候。')}</Text>
        <Text theme="text" parent='div'>{t('如您有相关问题，请联系架构师确认。')}</Text>
      </>}
      <GuardStatu item={guardInfo} />
      </>,
    },
    { id: 'step2', label: '' },
  ];
  // 护航启动 ref
  const baseInfoRef = useRef(null);
  // 详细信息 ref
  const detailInfoRef = useRef(null);
  // 信息确认 ref
  const confirmRef = useRef(null);
  // 当前显示步骤
  const [current, setCurrent] = useState(currentnStep || 'baseInfo');

  // 更新步骤
  function updateIndex(type) {
    const currentIndex = steps.findIndex(i => i.id === current);
    if (type === 'next') {
      setCurrent(steps[currentIndex + 1].id);
    } else {
      setCurrent(steps[currentIndex - 1].id);
    }
  }

  	// 查询是否有修改护航单基本信息
  function baseInfoModifyAllowed() {
    let stateAllowed = true;
    let guysAllowed = true;
    // 护航期间，草稿状态状态不允许修改
    if (guardInfo.Status <= processState.onNoteId || !guardInfo.GuardInfoSupportUpdate) {
      stateAllowed = false;
    }
    // 护航负责人
    if (guardInfo.Responser.indexOf(archInfo?.userName) === -1) {
      guysAllowed = false;
    }
    return stateAllowed && guysAllowed;
  }

  useEffect(() => {
    setLoading(true);
    DescribeOtherPlatformGuardSheet({ Filters: [{ Name: 'platform_unique_id', Values: [archInfo?.archInfo?.archId] }] })
      .then((res: any) => {
        // 最后一条护航单
        const guard = res.Guard?.length ? res.Guard[0] : {};
        // 如果最后一条已经护航完成，则不缓存
        setGuardInfo(getStatu(guard) === 'finish' ? {} : guard);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [uuid]);

  return <div className='escortDrawer'>
    {/* 没有正在护航的护航单 */}
    {
    loading
      // 加载效果
      ? <StatusTip status='loading' />
      // 展示内容
      : isISA
        // 运营端展示内容
        ? ((!guardInfo.Status || guardInfo.Status === 1)
          // 草稿单，展示填写信息流程
          ? <>
            <Stepper steps={steps} current={current} />
            <div className='escortDrawerContent' >
              {current === 'baseInfo' && <BaseInfo copyId={copyId} ref={baseInfoRef} updateIndex={updateIndex} />}
              {current === 'detailInfo' && <DetailInfo ref={detailInfoRef} updateIndex={updateIndex} />}
              {current === 'confirm' && <Confirm ref={confirmRef} updateIndex={updateIndex} />}
            </div>
            <Affix offsetBottom={0} target={() => document.querySelector('.affix-target')}>
              <div className='saveAffix'>
                {current !== 'confirm' && <Button type="weak"
                  onClick={() => {
                    reportGuard(1);
                    if (current === 'baseInfo') {
                      baseInfoRef.current.save(false);
                    } else if (current === 'detailInfo') {
                      detailInfoRef.current.save(false);
                    }
                  }}>
                  {t('保存')}
                </Button>
                }
                {current !== 'baseInfo' && <Button type="weak"
                  onClick={() => {
                    reportGuard(1);
                    if (current === 'detailInfo') {
                      detailInfoRef.current.save(false, true);
                    } else {
                      confirmRef.current.save(true);
                    }
                  }}>
                    {t('上一步')}
                </Button>
                }
                <Button
                  type="primary"
                  className='nextBtn'
                  onClick={() => {
                    if (current === 'baseInfo') {
                      reportGuard(1);
                      baseInfoRef.current.save();
                    } else if (current === 'detailInfo') {
                      reportGuard(1);
                      detailInfoRef.current.save();
                    } else {
                      reportGuard(2);
                      confirmRef.current.save();
                    }
                  }}
                  >
                    {current === 'confirm' ? t('确认提交') : t('下一步')}
                </Button>
              </div>
            </Affix>
          </>
          // 已经提交的护航单
          : (guardInfo.Status >= 50
            // 审批流程已完成，展示护航单详情
            ? <>
                <div className='escortDrawerContent' >
                  <Confirm updateIndex={() => { }} />
                </div>
                <Affix offsetBottom={0} target={() => document.querySelector('.affix-target')}>
                  <div className='saveAffix'>
                  <Button type="weak" onClick={() => {
                    archInfo.closeDrawer();
                  }}>{t('关闭')}</Button>
                     <Button
                      disabled={!baseInfoModifyAllowed()}
                      type="primary"
                      className='editBtn'
                      onClick={() => {
                        // 需要关闭打开的抽屉，不然会盖住基本信息变更的抽屉
                        archInfo.closeDrawer();
                        store.dispatch(setDrawerVisibleMapAction({ infoChangeDrawerVisible: true }));
                      }}
                      tooltip={<Bubble trigger="click">
                                  <Text style={{ whiteSpace: 'pre-line' }}>
                                    {t(' - 什么时候可修改信息？护航期间，非草稿状态\n - 谁能修改？护航负责人\n{{attr0}}', { attr0: guardInfo.Responser })}
                                  </Text>
                                </Bubble>}>
                        {t('信息变更')}
                    </Button>
                  </div>
              </Affix>
            </>
            // 审批流程未完成，展示审批流程
            : <Stepper className='verticalSteps' type="process-vertical-dot" steps={approvalStep} />
          ))
        // 租户端展示内容
        : (((guardInfo.CloudGuardBaseInfoOtherPlatform || []).
          find(i => i.PlatformUniqueId === archInfo.archInfo?.archId)?.Status === 2 || guardInfo.Status > 1)
          // 租户端已点击提交，或者已经已经提单了的护航单
          ? (guardInfo.Status >= 50
            // 审批流程已完成，展示护航单详情
            ? <>
                <div className='escortDrawerContent' >
                  <Confirm updateIndex={() => { }} />
                </div>
            </>
            // 审批流程未完成，展示审批流程
            : <Stepper className='verticalSteps' type="process-vertical-dot" steps={approvalStep} />
          )
          // 租户端未提交的护航单，展示基本信息
          : <>
            <Stepper steps={steps.slice(0, 1)} current='baseInfo' />
            <div className='escortDrawerContent' >
              <BaseInfo ref={baseInfoRef} updateIndex={updateIndex} />
            </div>
            <Affix offsetBottom={0} target={() => document.querySelector('.affix-target')}>
              <div className='saveAffix'>
                <Button type="weak"
                  onClick={() => {
                    reportGuard(1);
                    baseInfoRef.current.save(false, false, true);
                  }}>{t('保存')}
                </Button>
                <Button type="primary"
                  onClick={() => {
                    reportGuard(2);
                    baseInfoRef.current.save(false, true, true);
                  }}>{t('提交')}
                </Button>
              </div>
            </Affix>
          </>)
    }
  </div>;
};
export default EscortDrawer;
