/* eslint-disable no-param-reassign */
/* eslint-disable */
import React, { useEffect, useState } from 'react';
import { reportGuard } from '@src/utils/caching';
import PlaySvgIcon from '@src/assets/svg/play.svg';
import SettingSvgIcon from '@src/assets/svg/setting.svg';
import InfoIconSvg from '@src/assets/svg/info.svg'
import DownloadSvgIcon from '@src/assets/svg/download.svg';
import { setDrawerVisibleMapAction } from '@src/origin-store/guardAction';
import store from '@src/origin-store/store';
import PendingSvgIcon from '@src/assets/svg/pending.svg';
import ReadySvgIcon from '@src/assets/svg/preparation-icon.svg';
import { t } from '@tea/app/i18n';
import EscortDrawer from '@src/pages/escortDrawer/index';
import ResourceList from '@src/components/resourceList/index';
import OtherProduct from '@src/pages/otherProduct/index';
import { map, set } from 'lodash';
import { useCommonSelector } from '@src/store/app-common';
import './index.less';
import {  processState } from '@src/utils/common';

interface Iprops {
  uuid?: string;
  fromPlatform?:  string;
}
const updateOptions = (options, guardInfoDetail, type) => map(options, (item) => {
  if (item.desc === type) {
    if (guardInfoDetail.Status >= 32 && guardInfoDetail?.IsConfirm === 1) {
      // 护航负责人审批后才启用播报策略
      set(item, 'disabled', false);
    } else {
      set(item, 'disabled', true);
    }
  } else {
    set(item, 'disabled', false);
  }
  return item;
});

// 底部工具栏
const EscortTool = ({}: Iprops) => {
  const { guardStatus, guardInfoDetail }  = useCommonSelector();
  const [hoverText, setHoverText] = useState<any>();
  const {
    graphApi,
    drawerUpdateFlag,
  }  = store.getState().guard;
  const isISA = graphApi.env === 'ISA';

  // 工具栏操作项
  const defaultOptions = [
    {
      iconNode: <img src={PlaySvgIcon} alt={t('发起护航')} />,
      desc: t('发起护航'),
      disabled: false,
      hoverText: t('发起护航'),
      onClick: () => {
        reportGuard(0);
        store.dispatch(setDrawerVisibleMapAction({}));
        // 设置发起护航抽屉内容
        graphApi.setDrawerProps({
          title: t('发起护航'),
          children: <EscortDrawer />,
          className: 'escortDrawerWrap',
          extra: { outerClickClosable: false },
        });
        // 打开抽屉
        graphApi.openDrawer();
      },
    },
    {
      iconNode: <img src={SettingSvgIcon} alt={t('策略')} />,
      desc: isISA ? t('播报策略') : t('护航播报'),
      disabled: true,
      hoverText: t('护航负责人审批与播报群创建后启用'),
      onClick: () => {
        store.dispatch(setDrawerVisibleMapAction({ broadcastVisible: true }));
      },
    },
    {
      iconNode: <img src={SettingSvgIcon} alt={t('设置')} />,
      desc: t('节点设置'),
      disabled: false,
      onClick: () => {
        store.dispatch(setDrawerVisibleMapAction({}));
        // 设置节点设置抽屉
        graphApi.setDrawerProps({
          title: t('节点设置'),
          children: <ResourceList canEdit={true} />,
          className: isISA ? 'nodeSettingWrap wrapPlus' : 'nodeSettingWrap',
          extra: { outerClickClosable: false },
        });
        // 打开抽屉
        graphApi.openDrawer();
      },
    },
    {
      iconNode: <img src={InfoIconSvg} alt="其他产品" />,
      desc: t('其他产品'),
      disabled: true,
      onClick: () => {
        store.dispatch(setDrawerVisibleMapAction({}));
        // 设置其它产品抽屉
        graphApi.setDrawerProps({
          title: t('其他产品'),
          children: <OtherProduct />,
          className: 'otherProductWrap',
          extra: { outerClickClosable: false },
        });
        // 打开抽屉
        graphApi.openDrawer();
      },
    },
    {
      iconNode: <img src={DownloadSvgIcon} />,
      desc: t('历史护航'),
      disabled: false,
      onClick: () => {
        store.dispatch(setDrawerVisibleMapAction({ historyDrawerVisible: true }));
      },
    },
    {
      iconNode: <img src={PendingSvgIcon} alt={t('护航中')} />,
      desc: t('护航中'),
      disabled: false,
      color: '#0ABF5B',
      onClick: () => {
        store.dispatch(setDrawerVisibleMapAction({}));
        // 设置发起护航抽屉内容-展示审批信息
        graphApi.setDrawerProps({
          title: t('护航中'),
          children: <EscortDrawer />,
          className: 'escortDrawerWrap',
          extra: { outerClickClosable: false },
        });
        // 打开抽屉
        graphApi.openDrawer();
      },
    },
    {
      iconNode: <img src={ReadySvgIcon} alt={t('处理中')} />,
      desc: isISA ? t('护航前准备') : t('需求处理中'),
      disabled: false,
      color: '#FF7200',
      onClick: () => {
        const {
          IsConfirm: afterSaleIsConfirm,
          IsNeedConfirm,
        } = guardInfoDetail.Approvals?.AfterSaleConfirmStatus || {};
        if (IsNeedConfirm && !afterSaleIsConfirm && isISA && guardInfoDetail.Status > processState.onSaleApprovalId) {
          store.dispatch(setDrawerVisibleMapAction({ cloudResourceVisible: true }));
        } else {
          store.dispatch(setDrawerVisibleMapAction({}));
          // 设置发起护航抽屉内容-展示审批信息
          graphApi.setDrawerProps({
            title: isISA ? t('护航前准备') : t('需求处理中'),
            children: <EscortDrawer />,
            className: 'escortDrawerWrap',
            extra: { outerClickClosable: false },
          });
          // 打开抽屉
          graphApi.openDrawer();
        }
      },
    },
  ];

  // 查询是有修改护航单实例的人员：建单人、改单人、APPID负责人、审批人及售后指派人
  function getInstanceEditor() {
    let guys = [];
    guys = guys.concat(guardInfoDetail?.Approvals?.AfterSalesStatus?.Handler?.split(';'))
      .concat(guardInfoDetail?.Approvals?.AfterSalesStatus?.Supporter?.split(';'));
    if (guardInfoDetail?.Approvals?.ExpertStatus) {
      guardInfoDetail.Approvals.ExpertStatus.map((i) => {
        guys = guys.concat(i.Handler.split(';'));
      });
    }
    if (guardInfoDetail?.Approvals?.ScanResultStatus) {
      guardInfoDetail.Approvals.ScanResultStatus.map((i) => {
        guys = guys.concat(i.Handler.split(';'));
      });
    }
    guys = guys.concat(guardInfoDetail?.CreatedBy?.trim())
      .concat(guardInfoDetail?.UpdatedBy?.trim())
      .concat(guardInfoDetail?.Responser?.split(';'));
    return Array.from(new Set(guys)).filter(i => i !== '');
  }

  // 查询是否有修改护航单实例权限
  function isInstanceModifyAllowed() {
    let stateAllowed = true;
    let guysAllowed = true;
    // 护航期间，草稿状态和计算状态不允许修改
    if (!guardInfoDetail.GuardInfoSupportUpdate || guardInfoDetail.Status <= processState.onNoteId
            || [processState.onRunningId, processState.instanceAltering, processState.instanceAlteredRun]
              .includes(guardInfoDetail.Status)) {
      stateAllowed = false;
    }
    // 护航负责人审批状态，允许修改。该状态较特殊
    if (processState.onRunningId === guardInfoDetail.Status && !guardInfoDetail.Approvals.AfterSalesStatus.IsConfirm) {
      stateAllowed = true;
    }
    // 有权限修改的人员
    const insEditorList = getInstanceEditor();
    if (insEditorList.indexOf(graphApi?.userName) === -1) {
      guysAllowed = false;
    }
    return stateAllowed && guysAllowed;
  }

  // 判断当前护航状态
  // function initGuardStatu(guardInfoDetail) {
  //   if (guardInfoDetail?.Status) {
  //     // 护航开始、结束时间
  //     const { StartTime, EndTime } = guardInfoDetail;
  //     const today = moment();
  //     const start = moment(StartTime, 'YYYY-MM-DD HH:mm:ss');
  //     const end = moment(EndTime, 'YYYY-MM-DD HH:mm:ss');
  //     if (today.isAfter(end)) {
  //       // 过期
  //       setGuardStatus(0);
  //     } else if (guardInfoDetail.Status === 1) {
  //       // 未发起护航有草稿
  //       setGuardStatus(1);
  //     } else {
  //       if (today.isBefore(start)) {
  //         // 护航前准备
  //         setGuardStatus(3);
  //       } else {
  //         // 护航中
  //         setGuardStatus(2);
  //       }
  //     }
  //   } else {
  //     // 未发起护航且无草稿
  //     setGuardStatus(0);
  //   }
  // }

  // 根据护航状态改变工具栏状态
  
  function changeTools() {
    let options = defaultOptions.slice(0, 5);
    switch (guardStatus) {
      case 1:
        // 存在草稿单
        if (isISA) {
          options.forEach((item) => {
            if (item.desc === t('播报策略')) {
              item.disabled = true;
            } else item.disabled = false;
          });
        } else {
          // 发起护航、节点设置
          options = [defaultOptions[0]];
          options[0].disabled = false;

          // 如果有客户接口人，说明用户已经点击过提交护航单了
          if ((guardInfoDetail.CloudGuardBaseInfoOtherPlatform || [])
            .find(i => i.PlatformUniqueId === graphApi.archInfo?.archId)?.Status === 2) {
            options[0] = defaultOptions[6];
          }
        }
        break;
      case 2:
        // 护航中
        if (isISA) {
          options = [defaultOptions[5], ...defaultOptions.slice(1, 3), defaultOptions[4]];
          options.forEach((item) => {
            if (item.desc === t('节点设置')) {
              item.disabled = !isInstanceModifyAllowed();
              item.hoverText = item.disabled ? hoverText : item.hoverText;
            } else {
              item.disabled = false;
            }
          });
          options = updateOptions(options, guardInfoDetail, t('播报策略'));
        } else {
          // 护航中
          options = [defaultOptions[5], defaultOptions[1]];
          options = updateOptions(options, guardInfoDetail, t('护航播报'));
        }
        break;
      case 3:
        // 护航前准备
        if (isISA) {
          options = [defaultOptions[6], ...defaultOptions.slice(1, 3), defaultOptions[4]];
          options.forEach((item) => {
            if (item.desc === t('节点设置')) {
              item.disabled = !isInstanceModifyAllowed();
              item.hoverText = item.disabled ? hoverText : item.hoverText;
            }
          });
          options = updateOptions(options, guardInfoDetail, t('播报策略'));
        } else {
          // 需求处理中、节点设置
          options = [defaultOptions[6]];
        }
        break;
      default:
        // 0无权限发起护航
        if (isISA) {
          options = defaultOptions.slice(0, 5);
        } else {
          // 发起护航、节点设置
          options = [defaultOptions[0]];
          options[0].disabled = true;
          options[0].hoverText = t('护航服务目前为腾讯云服务计划企业版/旗舰版客户专用');
          
        }
    }
    graphApi.setOperations(options);
    graphApi.showTools();
  }

  // // 获取架构图对应的护航单状态
  // const getDescribeOtherPlatformGuardSheet = async () => {
  //   const data = { Filters: [{ Name: 'platform_unique_id', Values: [archInfo.archInfo?.archId] }] };
  //   try {
  //     const res: any = await DescribeOtherPlatformGuardSheet(data);
  //     if (res.Error) {
  //       const message = res.Error.Message || '';
  //       app.tips.error(message);
  //       return;
  //     }
  //     setGuardInfo(res.Guard?.[0] || {});
  //   } catch (err) {
  //     const message = err.msg || err.toString() || t('未知错误');
  //     app.tips.error(t('{{message}}', { message }));
  //   }
  // };

  // useEffect(() => {
  //   getDescribeOtherPlatformGuardSheet();
  // }, [drawerUpdateFlag]);

  useEffect(() => {
    // initGuardStatu(guardInfoDetail);
    setHoverText(<>
        <p>{t('- 什么时候修改？护航期间，在草稿或计算以外的状态')}</p>
        <p>{t('- 谁能修改？建单人、负责人、审批人')}</p>
        <p>{t('{{attr0}}', { attr0: getInstanceEditor().join(',') })}</p>
      </>
    );
  }, [guardInfoDetail]);

  useEffect(() => {
    const {
      IsConfirm: afterSaleIsConfirm,
      IsNeedConfirm,
    } = guardInfoDetail.Approvals?.AfterSaleConfirmStatus || {};
    if (IsNeedConfirm && !afterSaleIsConfirm && isISA && guardInfoDetail.Status > processState.onSaleApprovalId) {
      store.dispatch(setDrawerVisibleMapAction({ cloudResourceVisible: true }));
    } else {
      store.dispatch(setDrawerVisibleMapAction({ cloudResourceVisible: false }));
    }
  }, [guardInfoDetail]);

  useEffect(() => {
    changeTools();
  }, [guardStatus, guardInfoDetail]);

  return <>
    {/* <EscortResource guardStatus={guardStatus} uuid={nanoid()} fromPlatform={fromPlatform} /> */}
    {/* <GuardRoot /> */}
  </>;
};
export default EscortTool;
