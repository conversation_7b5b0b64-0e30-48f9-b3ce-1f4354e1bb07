// 面板生成结果
import { t } from '@tea/app/i18n';
export enum EMonitorResultType {
  FAILURE = 3,
  SUCCESS = 2,
  PENDDING = 1,
}

export const EMonitorResult  = {
  [EMonitorResultType.SUCCESS]: t('成功'),
  [EMonitorResultType.PENDDING]: t('进行中'),
  [EMonitorResultType.FAILURE]: t('失败'),
};

// 终端类型
export enum EENDTYPE {
  CUSTOMER = 'CUSTOMER', // 租户端
  OPERATOR = 'OPERATOR', // 运营端
}

export const SOURCE_STATUS = {
  ERROR: 0,
  SUCCESS: 1,
  EMPTY: 2,
};

export const sourceOption = [
  { text: t('全部'), value: 'all' },
  { text: t('正常'), value: `${SOURCE_STATUS.SUCCESS}` },
  { text: t('异常'), value: `${SOURCE_STATUS.ERROR}` },
  { text: t('空'), value: `${SOURCE_STATUS.EMPTY}` },
];

export enum guardStatusEnum {
  NO_GUARD_DRAFT = 0, // 未发起护航且无草稿或过期
  GUARD_DRAFT = 1, // 未发起护航有草稿
  GUARDING = 2, // 护航中
  GUARD_BEFORE_PARPARE = 3, // 护航前准备
}


export enum ENodeTaskStatus {
  INIT = 'init',
  RUNNING = 'running',
  SUCCESS = 'success',
  FAILURE = 'fail',
}

