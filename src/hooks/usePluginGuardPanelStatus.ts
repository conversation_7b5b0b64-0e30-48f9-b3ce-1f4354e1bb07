import useSWR from 'swr';
import { some, isEmpty } from 'lodash';
import { DescribeArchGuardNodeMonitorPanel } from '@src/service/api/baseInfo';
/**
 * 查询节点监控面板
 *
 * @param {string} type - 类型.
 * @param {object} params - 入参.
 * @return {object} - 返回的对象.
 * @return {object} result.data - 接口返回的数据.
 * @return {object} result.error - 接口错误信息.
 * @return {function} result.reload - 重载.
 * @return {boolean} result.isLoading - 是否正在请求.
 * @example
 */
const usePluginGuardPanelStatus = (
  params,
  isFinished,
) => {
  const isIllegal = obj => isEmpty(obj) || some(obj, v => v === null || v === undefined || v === '');
  const {
    data, error, mutate, isLoading, isValidating,
  } = useSWR(
    !isIllegal(params) && !isFinished
      ? `/api/DescribeArchGuardNodeMonitorPanel?${JSON.stringify(params)}`
      : null,
    () => DescribeArchGuardNodeMonitorPanel({ ...params }),
    {
      revalidateOnReconnect: false,
      revalidateOnFocus: false,
      shouldRetryOnError: false,
      refreshInterval: 3000,
    },
  );
  return {
    result: data,
    error,
    isLoading: isLoading || isValidating,
    reload: mutate,
  };
};
export default usePluginGuardPanelStatus;
