/* eslint-disable */
import React from 'react';
import { t } from '@tea/app/i18n';

import './index.less';

interface IProps {
  url?: string;
}


/** Root
 * @param props
 * @returns React.ReactElement
 */
export default function IframeBox(props: IProps): React.ReactElement {
  const { url = 'https://observe.woa.com/d/LsO-5J3Ik/tceo?orgId=1258345099' } = props;
  return (
    <iframe className="responsive-iframe" src={url}></iframe>
  );
}
