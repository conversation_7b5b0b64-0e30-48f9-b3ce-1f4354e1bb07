import React, { useEffect, useState } from 'react';
import { t } from '@tea/app/i18n';
import { Collapse } from 'tdesign-react';
import './index.less';
import { Text, Table, StatusTip, Icon, Button, Modal } from '@tencent/tea-component';
import {
  DescribeArchNodeBroadcastStatusShow,
  DescribeArchNodeStatusBroadCastList,
  DescribeArchNodeSingleBroadcastRecordDetail,
} from '@src/service/api/baseInfo';
import { useCommonSelector } from '@src/store/app-common';
import store from '@src/origin-store/store';
const { Panel } = Collapse;
const { pageable, scrollable } = Table.addons;;

/** Root
 * @param props
 * @returns React.ReactElement
 */
export default function StatusReport(): React.ReactElement {
  const { graphApi, currNode } = store.getState().guard;
  const { guardInfoDetail, appId, allNodeList }  = useCommonSelector();
  // 是否是租户端
  const isCONSOLE = graphApi.env === 'CONSOLE';

  // 播报列
  const columns = [
    {
      key: 'BroadcastStrategy',
      header: t('播报项'),
    },
    {
      key: 'Status',
      header: t('资源状态'),
      render: (item) => {
        const IsNormal = item.Status === 1 || item.Status === 2;
        return (<div style={{ display: 'flex', alignItems: 'center' }}>
              <Icon type={IsNormal ? 'success' : 'error'} />
              <span style={{ marginLeft: '3px' }}>
                  <Text theme={IsNormal ? 'success' : 'danger'}>{IsNormal ? t('正常') : t('异常')}</Text>
              </span>
          </div>);
      },
    },
    {
      key: 'BroadcastTime',
      header: t('播报时间'),
    },
    {
      key: 'SendTime',
      header: t('详情'),
      render: item => <Button type="link" onClick={() => openDetail(item)}>{t('查看详情')}</Button>,
    },
  ];

  // 播报状态信息
  const [statusInfo, setStatusInfo] = useState<any>({});
  // 播报列表
  const [broadcastList, setBroadcastList] = useState([]);
  // 列表查询接口常规参数
  const [offset, setOffset] = useState<number>(0);
  const [limit, setLimit] = useState<number>(20);
  const [loading, setLoading] = useState<boolean>(false);
  const [total, setTotal] = useState<number>(0);
  // 播报详情
  const [broadcastDetail, setBroadcastDetail] = useState<any>({});
  // 播报详情弹框显示标志
  const [broadcastDetailVisible, setBroadcastDetailVisible] = useState<any>(false);
  // 播报详情弹框加载标志
  const [detailLoading, setDetailLoading] = useState<any>(false);

  useEffect(() => {
    if (currNode.key) {
      // 云护航支持的产品才调接口
      if ((allNodeList || [])?.find(i => i.NodeUuid === currNode.key)?.Product) {
        getBroadcastStatus();
        getBroadcastList(offset, limit);
      } else {
        setStatusInfo({ BroadcastStatus: 1 });
      }
    }
  }, [currNode.key, allNodeList]);

  // 查询播报状态
  function getBroadcastStatus() {
    const params = {
      GuardId: guardInfoDetail.GuardId,
      ArchId: graphApi?.archInfo?.archId,
      NodeUuid: currNode.key,
      Product: (allNodeList || [])?.find(i => i.NodeUuid === currNode.key)?.Product,
      ...(!isCONSOLE && { AppId: Number(appId) }),
    };
    DescribeArchNodeBroadcastStatusShow(params).then((res) => {
      setStatusInfo(res || {});
    })
      .catch((err) => {
        console.log(err);
      });
  }

  // 查询播报列表
  function getBroadcastList(Offset?, Limit?) {
    setLoading(true);
    const params = {
      GuardId: guardInfoDetail.GuardId,
      ArchId: graphApi?.archInfo?.archId,
      NodeUuid: currNode.key,
      Offset,
      Limit,
      Product: (allNodeList || [])?.find(i => i.NodeUuid === currNode.key)?.Product,
      ...(!isCONSOLE && { AppId: Number(appId) }),
    };
    DescribeArchNodeStatusBroadCastList(params)
      .then((res: any) => {
        setTotal(res?.TotalCount || 0);
        setBroadcastList(res?.StatusBroadcastList || []);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  }

  // 打开播报详情
  function openDetail(data) {
    setDetailLoading(true);
    setBroadcastDetailVisible(true);
    const params = {
      Code: data.Code,
      ...(!isCONSOLE && { AppId: Number(appId) }),
    };

    DescribeArchNodeSingleBroadcastRecordDetail(params).then((res: any) => {
      setBroadcastDetail(res || {});
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setDetailLoading(false);
      });
  }

  // 构造dom返回
  function getDom(abnormal = false) {
    const domList = [];
    (broadcastDetail?.BroadcastArchResultDetailList || []).map((i, index) => {
      const replacedMessage = i.Message.replace(/(\d+(\.\d+)?%)/g, '<span class="value">$1</span>');
      const tempDom = <>
        <div className='lineWrap'>
          <div className='lineIndex'>{index + 2}</div>
          <div className='line-index'>{i.ResourceId}</div>
          <div className='lineMessage' dangerouslySetInnerHTML={{ __html: replacedMessage }} />
          <div>{i.Status === 0 ? <Text theme="danger">{t('异常')}</Text> : t('正常')}</div>
        </div>
      </>;
      if (abnormal) {
        if (i.Status === 0) {
          domList.push(tempDom);
        }
      } else {
        domList.push(tempDom);
      }
    });
    return domList;
  }

  // 没有播报的提示
  function noBroadcastTip() {
    let str = '';
    switch (statusInfo?.BroadcastStatus) {
      case 1:
        str = t('<该产品目前不支持播报>');
        break;
      case 2:
        str = t('<未配置播报策略>');
        break;
      case 3:
        str = t('<播报策略未启用>');
        break;
    }
    return str ? <div className='noBroadcastWrap'>{str}</div> : '';
  }

  return (<div className='statusReportWrap'>
      <div className='title'>
        {t('状态播报')}
      </div>
     {
      statusInfo?.BroadcastStatus === 0
        ? <Collapse expandIconPlacement='right'>
          <Panel header={
            <div className='report-title'>
              {t('当前播报周期')}
              <Text className='statusText' theme={statusInfo?.TotalException ? 'danger' : 'success'}>{statusInfo?.TotalException ? t('{{attr0}}项异常', { attr0: statusInfo?.TotalException }) : t('全部正常')}</Text>
              {statusInfo?.BroadcastTime && (statusInfo?.BroadcastTime)}
            </div>
          }>
            <Table
              className='instanceTableWrap'
              bordered={true}
              records={broadcastList}
              recordKey="Code"
              columns={columns}
              topTip={
                (loading || broadcastList.length === 0) && (
                  <StatusTip status={loading ? 'loading' : 'empty'} />
                )
              }
              addons={[
                pageable({
                  recordCount: total,
                  pageSize: limit,
                  pageIndex: offset / limit + 1,
                  onPagingChange: (query) => {
                    if (loading) {
                      return;
                    }
                    setOffset((query.pageIndex - 1) * query.pageSize);
                    setLimit(query.pageSize);
                    getBroadcastList((query.pageIndex - 1) * query.pageSize, query.pageSize);
                  },
                }),
                scrollable({ maxHeight: 530 }),
              ]}
            />
          </Panel>
        </Collapse> : noBroadcastTip()}

      <Modal
        maskClosable
        size="xl"
        visible={broadcastDetailVisible}
        caption={t('详情')}
        onClose={() => setBroadcastDetailVisible(false)}
      >
        <Modal.Body>
          {detailLoading ?  <StatusTip status='loading' />
            : <div className='detailWrap'>
             {getDom(true)?.length > 0 && <div className='abnormalWrap'>
                { getDom(true) }
              </div>}
                <div className='codeWrap'>
                  <div className='lineWrap'>
                    <div className='lineIndex'>1</div>
                    <div>===={broadcastDetail.PolicyName}====</div>
                  </div>
                  { getDom() }
                </div>
            </div>
           }
        </Modal.Body>
      </Modal>
    </div>);
}
