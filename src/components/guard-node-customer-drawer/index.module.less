.datePicker {
  :global {
    .t-range-input {
      border-radius: 0 !important;
      height: 30px;
      overflow: hidden;
      border: 1px solid #ddd;
      box-shadow: none !important;
    }
  }
}
.fullScreen {
  :global {
    .tea-dialog__header .tea-btn--icon {
      right: 0 !important;
    }
    .tea-dialog__inner {
      overflow: auto;
    }
  }
}
.rangePicker {
  input {
    width: 200px !important;
  }
  span {
    margin: 0 10px !important;
  }
}
.collapse {
  margin-top: 15px;
  :global {
    .tea-accordion:not(:first-child) {
      margin-top: 10px;
    }
    .tea-accordion__body {
      margin-top: 5px;
      padding: 0 5px;
      &>div {
        padding: 0px !important;
      }
    }
    .tea-accordion__header-title {
      margin-left: 5px;
    }
    .tea-icon-arrowup {
      transform: rotate(180deg);
    }
  }
}