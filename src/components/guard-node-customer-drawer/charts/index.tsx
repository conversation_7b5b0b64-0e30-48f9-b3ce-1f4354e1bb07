/* eslint-disable no-param-reassign */
import React, { useState, useEffect, useMemo } from 'react';
import ReactEChartsCore from 'echarts-for-react/lib/core';
import * as echarts from 'echarts/core';
import store from '@src/origin-store/store';
import { Table, Card } from '@tencent/tea-component';
import fetchData from '@src/service/fetch';
import { setDrawerMetrics, setCurDrawerMetrics } from '@src/origin-store/guardAction';
import { Loading } from 'tdesign-react';
import { DescribeArchGuardNodeMetricInfos } from '@src/service/api/customer-drawer';
import moment from 'moment';
import { LineChart, BarChart } from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
  LegendScrollComponent,
  LegendPlainComponent,
  DataZoomComponent,
  DataZoomInsideComponent,
  DataZoomSliderComponent,
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { t } from '@tea/app/i18n';
import s from './index.module.less';

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  BarChart,
  LineChart,
  CanvasRenderer,
  LegendComponent,
  LegendScrollComponent,
  LegendPlainComponent,
  DataZoomComponent,
  DataZoomInsideComponent,
  DataZoomSliderComponent,
]);
interface IProps {
  metric: {
    MetricName: string;
    Metric: string;
  };
  query?: {
    ArchId: string;
    NodeUuid: string;
    StartTime: string;
    EndTime: string;
    InstanceIdList: string[];
  };
  bordered?: boolean;
}
// 如果子组件接受了一个方法作为属性，我们在使用 React.memo 这种避免子组件做没必要的渲染时候，就需要用 useCallback 进行配合，否则 React.memo 将无意义
const Child = React.memo(({ option, chartRef }: { option: any; chartRef: any }) => (
		<>
			<ReactEChartsCore
				echarts={echarts}
				option={option}
				notMerge={true}
				lazyUpdate={true}
				ref={chartRef}
				theme={'theme_name'}
				style={{ minHeight: '320px' }}
				onChartReady={() => {
				  //
				}}
				showLoading={false}
				loadingOption={{
				  text: t('加载中...'),
				  showSpinner: true,
				  spinnerRadius: 5,
				}}
				onEvents={{
				  click: () => {},
				}}
				opts={{}}
			/>
		</>
));
Child.displayName = 'Child';
/**
 * 图表组件。
 *
 * @param {IProps} props - 组件的属性。
 * @return {JSX.Element} 渲染后的图表组件。
 */
const App = (props: IProps) => {
  const { query, bordered = false, metric } = props;
  const { drawerMetrics = [], curDrawerMetrics = [] } = store.getState().guard;
  const chartRef = React.useRef(null);
  const isMoreThanOneDay = useMemo(() => {
    const startTime = moment(query.StartTime, 'YYYY-MM-DD HH:mm:ss');
    const endTime = moment(query.EndTime, 'YYYY-MM-DD HH:mm:ss');
    // 计算两个时间之间的天数差
    const daysDifference = endTime.diff(startTime, 'days');
    // 判断时间差是否超过一天
    const moreThanOneDay = daysDifference > 1;
    return moreThanOneDay;
  }, [query.StartTime, query.EndTime]);
  const [insList, setInsList] = useState([]);
  const [option, setOption] = useState<any>({
    backgroundColor: '#fff',
    grid: {
      left: '8%',
      right: '6%',
      top: '15%',
      bottom: 110,
    },
    title: [
      {
        left: 'center',
        top: 0,
        text: metric.MetricName,
        textStyle: {
          fontSize: 13,
        },
      },
    ],
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#363A50',
      confine: true, // 防止溢出
      textStyle: {
        color: '#fff', // 设置字体颜色
      },
      borderColor: '#fff',
      borderWidth: 0,
      borderRadius: 0,
      transitionDuration: 0.2,
      formatter(params) {
        let result = `${params[0].name}<br/>`; // 获取时间或类别
        params.forEach((item) => {
          result += `<p>
              <span style="display:inline-block; margin-right:5px; width:10px; height:10px; background-color:${
  item.color
}; border-radius: 50%"></span>
              ${item.seriesName}: &nbsp;&nbsp;<strong>${item?.value ?? ''}</strong><br/>
          </p>`;
        });
        return result;
      },
    },
    xAxis: {
      show: false,
      type: 'category',
      data: [],
      axisTick: {
        // 隐藏刻度线
        show: false,
      },
      axisLabel: {
        formatter(value) {
          const label = isMoreThanOneDay ? value : moment(value).format('HH:mm');
          return label;
        },
      },
    },
    yAxis: {
      show: false,
      type: 'value',
      min: 0,
      axisTick: {
        alignWithLabel: true,
      },
      axisLabel: {
        formatter: '{value}',
      },
    },
    dataZoom: [
      {
        type: 'slider', // 滑动条
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: 100,
        top: 250,
        width: '55%',
        left: 'center',
      },
      // {
      //   type: 'inside', // 内置缩放
      //   xAxisIndex: [0],
      //   start: 0,
      //   end: 100,
      //   top: 250,
      // },
    ],
    legend: {
      orient: 'horizontal',
      left: 'center',
      bottom: 0,
      data: [],
      type: 'scroll',
    },
    series: [],
  });
  const {
    result: metricRes,
    error: metricError,
    isLoading: metricLoading,
    reload: metricReload,
  } = fetchData({
    name: 'DescribeArchGuardNodeMetricInfos',
    params: {
      ...query,
      Metric: metric.Metric,
      // IsAllInsMetric: query?.InstanceIdList?.length === 0,
    },
    // eslint-disable-next-line max-len
    cancel: query?.InstanceIdList?.length === 0 || (!(window as any)?.swrTable?.[metric.Metric] && !curDrawerMetrics.includes(metric.Metric)), // 当前查询指标不在在并发控制内时不请求
    fetch: DescribeArchGuardNodeMetricInfos,
    config: {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      onSuccess: (data) => {
        if (!(window as any)?.swrTable) {
          (window as any).swrTable = {};
        } else {
          (window as any).swrTable[metric.Metric] = true;
        }
        setTimeout(() => {
          try {
            delete (window as any).swrTable[metric.Metric];
          } catch (error) {
            console.log(error);
          }
        }, 60000);
        metricReload(data, false); // 更新缓存数据 & 不重新校验
        // 待查指标 往 正在查询的指标 插入
        const newArr = drawerMetrics.splice(0, 1);
        curDrawerMetrics.splice(0, 0, ...newArr);
        const newCurDrawerMetrics = curDrawerMetrics.filter(v => v !== metric.Metric);
        store.dispatch(setDrawerMetrics(drawerMetrics));
        store.dispatch(setCurDrawerMetrics(newCurDrawerMetrics));
      },
      dedupingInterval: 60000,
    },
  });

  /**
   * 如果 data 是一个小于 0 的数字，则返回 '-'，否则返回 data。
   * 如果 data 不是一个数字，或者是 NaN、null、undefined 等值，则返回 '-'。
   * @param {number} [data] - 需要处理的数字
   * @param {string} [symbol] - 负数的符号，缺省时为 '-'
   * @returns {string|number} 处理后的数字
   */
  const negativeNumber = (data, symbol?: string) => {
    if (typeof data !== 'number' || isNaN(data)) {
      return '-';
    }
    return data < 0 ? symbol || '-' : data;
  };

  useEffect(() => {
    if (metricRes) {
      if (!metricRes?.Error && !metricError && metricRes?.InstanceMetricInfoList) {
        const newInsList = metricRes.InstanceMetricInfoList.map((item: any) => ({
          instance: item.InstanceId,
          current: negativeNumber(item.Values[item.Values.length - 1]) ?? '-',
          min: item.Values.length ? negativeNumber(item.Values.reduce((acc, cur) => Math.min(acc, cur), Infinity)) : '-',
          max: item.Values.length ? negativeNumber(item.Values.reduce((acc, cur) => Math.max(acc, cur), -Infinity)) : '-',
        }));
        setInsList(newInsList);
        setOption((last) => {
          const curMetric = metricRes.InstanceMetricInfoList.find(v => v.Timestamps.length) || [];
          last.legend.data = metricRes.InstanceMetricInfoList.map((item: any) => item.InstanceId);
          last.xAxis.data = (curMetric?.Timestamps ?? []).map(v => moment.unix(v).format('YYYY-MM-DD HH:mm:ss'));
          last.xAxis.axisLabel = {
            formatter(value) {
              const label = isMoreThanOneDay ? moment(value).format('MM/DD HH:mm') : moment(value).format('HH:mm');
              return label;
            },
          };
          last.title = [
            {
              left: 'center',
              top: 0,
              text: `${metric.MetricName}${metricRes.Unit ? `(${metricRes.Unit})` : ''}`,
              textStyle: {
                fontSize: 13,
              },
            },
          ];
          last.xAxis.show = true;
          last.yAxis.show = true;
          last.series = metricRes.InstanceMetricInfoList.map((item: any) => ({
            showSymbol: false,
            name: item.InstanceId,
            type: 'line',
            data: item?.Values?.map?.(v => (v < 0 ? null : v)) || [],
          }));
          // 如果没有数据，则显示一条提示信息
          if (!metricRes?.InstanceMetricInfoList?.length) {
            last.legend = {
              orient: 'horizontal',
              left: 'center',
              bottom: 5,
              data: [t('暂无数据')],
              icon: 'none',
              textStyle: {
                fontSize: 13,
              },
            };
            last.series = [
              {
                name: t('暂无数据'),
                type: 'line',
                data: [0],
                showSymbol: false,
              },
            ];
            last.yAxis = {
              show: true,
              type: 'value',
              min: 0,
              max: 100,
              interval: 25,
              axisTick: {
                alignWithLabel: true,
              },
            };
          }
          // 如果有数据：有实例，但是纵坐标没有值
          if (metricRes?.InstanceMetricInfoList?.every(item => !item?.Values?.length)) {
            last.yAxis.max = 100;
            last.yAxis.interval = 25;
          } else {
            delete last.yAxis.max;
            delete last.yAxis.interval;
          }
          return { ...last };
        });
      } else {
        setOption((last) => {
          last.legend.data = [];
          last.xAxis.data = [];
          last.series = [];
          return { ...last };
        });
        setInsList([]);
      }
    }
  }, [metricRes, metricError]);

  useEffect(() => {
    if (chartRef.current) {
      if (option.series.length) {
        chartRef.current.getEchartsInstance().setOption(option);
      } else {
        // 如果没有数据，则显示一条提示信息
        chartRef.current.getEchartsInstance().setOption({
          ...option,
          legend: {
            orient: 'horizontal',
            left: 'center',
            bottom: 5,
            data: [t('暂无数据')],
            icon: 'none',
            textStyle: {
              fontSize: 13,
            },
          },
          series: [
            {
              name: t('暂无数据'),
              type: 'line',
              data: [0],
              showSymbol: false,
            },
          ],
          yAxis: {
            show: true,
            type: 'value',
            min: 0,
            max: 100,
            interval: 25,
            axisTick: {
              alignWithLabel: true,
            },
          },
        });
      }
    }
  }, [option]);

  return (
		<div className={s.wrapper}>
			{/* <div className={s.triangle}>
        <Bubble
          dark
          arrowPointAtCenter
          placement="left-start"
          content={t('统计粒度 1m；统计方式 avg。')}
        >
          <Icon style={{ position: 'absolute', top: -33, left: 2, transform: 'scale(0.9)' }} type="info" />
        </Bubble>
      </div> */}
			<Card bordered={bordered} className={!bordered ? s.noBordered : ''}>
				<Card.Body>
					<div style={{ position: 'relative', marginTop: 5 }}>
						<Loading
							indicator
							loading={metricLoading}
							size="small"
							text={t('加载中...')}
							preventScrollThrough
							showOverlay
						>
							<Child option={option} chartRef={chartRef} />
							<Table
								compact
                key={insList?.map(v => v?.InstanceId).join(',')}
								bordered={false}
								verticalTop
								disableTextOverflow
								records={insList}
								recordKey="instance"
								columns={[
								  {
								    key: 'instance',
								    header: '',
								  },
								  {
								    key: 'min',
								    width: 100,
								    header: 'min',
								  },
								  {
								    key: 'max',
								    width: 100,
								    header: 'max',
								  },
								  {
								    key: 'current',
								    width: 100,
								    header: 'current',
								  },
								]}
								// topTip={
								// 	!insList.length && (
								// 		<StatusTip
								// 			// @ts-ignore
								// 			status={'empty'}
								// 		/>
								// 	)
								// }
							/>
						</Loading>
					</div>
				</Card.Body>
			</Card>
		</div>
  );
};

export default App;
