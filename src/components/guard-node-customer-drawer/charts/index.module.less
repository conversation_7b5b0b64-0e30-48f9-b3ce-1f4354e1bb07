.wrapper {
  // background: #F3F4F7;
  margin-top: 10px;
  padding: 0px;
  border-radius: 5px;
  position: relative;
  .radios {
    background-color: #fff;
    margin-right: 9px;
    :global {
      input {
        width: auto !important;
      }
    }
  }
}
.triangle {
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 35px 35px 0px 0px;
  border-color: #D9DFE5 transparent transparent transparent;
}
.noBordered {
  box-shadow: none !important;
}
.other {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  span:nth-child(1) {
    color: #888;
  }
  span:nth-child(2) {
    margin-left: 10px;
  }
}
.back {
  display: flex;
  align-items: center;
  margin-left: 8px;
  margin-top: 3px;
  img {

    margin-right: 10px;
    &:hover {
      cursor: pointer;
    }
  }
  span {
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    text-align: left;
  }
}
.flex {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  &>p{
    display: flex;
    align-items: center;
  }
  .title {
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 500;
    line-height: 18px;
    text-align: left;
    color: #000000;
    max-width: 161px;
    overflow: hidden;
    display: inline-block;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .period {
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    text-align: left;
    color: #888888;
    margin-left: 10px;
  }
}