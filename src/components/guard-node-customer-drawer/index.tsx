/* eslint-disable no-nested-ternary */
import React, { useEffect, useMemo, useState, useCallback, useRef } from 'react';
import { t } from '@tea/app/i18n';
import moment from 'moment';
import { DateRangePicker, Row, Col, Space } from 'tdesign-react';
import type { DateRangePickerProps } from 'tdesign-react';
import { Modal, Drawer, Button, SelectMultiple, Collapse, StatusTip, Status } from '@tencent/tea-component';
import store from '@src/origin-store/store';
import { EMonitorResultType, ENodeTaskStatus } from '@src/constants';
import nodata from '@src/assets/svg/nodata.svg';
import Charts from './charts';
import StatusReport from '@src/components/status-report';
import BusinessHealthAlert from '@src/components/business-health-alert';
import {
  setDrawerVisibleMapAction,
  setNodeCustomerDrawerRangeTimeAction,
  setDrawerMetrics,
  setCurDrawerMetrics,
} from '@src/origin-store/guardAction';
import { find, isEmpty } from 'lodash';
import { getShapeStyleLable } from '@src/utils/guard-node';
import { DescribeArchGuardProductMetricConfig, DescribeArchGuardNodeInstances } from '@src/service/api/customer-drawer';

import fetchData from '@src/service/fetch';

import { useCommonSelector } from '@src/store/app-common';
import './index.less';
import s from './index.module.less';
import { guardInfo } from '@src/utils/caching';

const defaultMonitorResult = {
  Status: EMonitorResultType.PENDDING,
  DashBoardUrl: '',
  IsSupport: true,
};

/** Root
 * @param props
 * @returns React.ReactElement
 */
export default function GuardNodeDrawer(): React.ReactElement {
  const start = moment(guardInfo.StartTime, 'YYYY-MM-DD HH:mm:ss');
  const end = moment(guardInfo.EndTime, 'YYYY-MM-DD HH:mm:ss');
  const { taskResult } = useCommonSelector();
  // 是否在护航中
  const isGuarding = guardInfo.Status > 1 && moment().isBetween(start, end);

  const { currNode, graphApi, nodeCustomerDrawerRangeTime, metricsFetchThrottle } = store.getState().guard;
  const [modalVisible, setModalVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [key, setKey] = useState('');
  const firstLoadData = useRef(true);
  const [presets] = useState<DateRangePickerProps['presets']>({
    近1小时: [moment().subtract(1, 'hour')
      .toDate(), moment().toDate()],
    近6小时: [moment().subtract(6, 'hours')
      .toDate(), moment().toDate()],
    近12小时: [moment().subtract(12, 'hours')
      .toDate(), moment().toDate()],
    今天: [moment().startOf('day')
      .toDate(), moment().endOf('day')
      .toDate()],
    近3天: [moment().subtract(2, 'day')
      .startOf('day')
      .toDate(), moment().endOf('day')
      .toDate()],
    近7天: [moment().subtract(6, 'day')
      .startOf('day')
      .toDate(), moment().endOf('day')
      .toDate()],
    近30天: [moment().subtract(29, 'day')
      .startOf('day')
      .toDate(), moment().endOf('day')
      .toDate()],
  });
  const [insState, setInsState] = useState({
    insOptions: [],
    insValue: [], // 当前选中的实例
    insLoading: false,
  });
  const [metric, setMetric] = useState([]);
  // 指标选项
  const [metricOptions, setMetricOptions] = useState([]);
  // 当前选择的指标
  const [selectMetric, setSelectMetric] = useState([]);
  const { archInfo } = graphApi;
  const isInstanceLoading = useMemo(() => {
    const loadStatus = find(taskResult?.NodeDetail, item => item?.NodeUuId === currNode?.key) || {};
    if (!isEmpty(loadStatus)) {
      if (loadStatus.Status === ENodeTaskStatus.INIT || loadStatus.Status === ENodeTaskStatus.RUNNING) {
        return true;
      }
      return false;
    }
    return false;
  }, [currNode.key, taskResult?.NodeDetail]);

  const allShapes = graphApi.getArchNodes();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [monitorResult, setMonitorResult] = useState<any>(defaultMonitorResult);

  const currShapeName = useMemo(() => {
    if (currNode) {
      const currLabelKey = currNode?.sticky;
      if (currLabelKey) {
        // 产品图元有sticky关联label节点
        const currLabel = allShapes[currLabelKey];
        return currLabel?.styles?.label?.value || getShapeStyleLable(currLabel);
      }
      // 组类型图元没有sticky属性
      return currNode?.styles?.label?.value || currNode?.styles?.label?.default;
    }
    return '';
  }, [currNode]);

  useEffect(() => {
    setMonitorResult(defaultMonitorResult);
  }, [currNode.key]);

  const {
    result: insRes,
    error: insError,
    isLoading: insLoading,
    reload: insReload,
  } = fetchData({
    name: 'DescribeArchGuardNodeInstances',
    params: {
      NodeUuid: currNode.key,
      ArchId: archInfo.archId,
      Limit: -1,
      Offset: 0,
    },
    key,
    fetch: DescribeArchGuardNodeInstances,
  });

  useEffect(() => {
    if (!insRes?.Error && !insError && insRes?.InstanceIdList) {
      const insOptions = insRes.InstanceIdList.map(v => ({
        text: v,
        value: v,
      }));
      setInsState(last => ({
        ...last,
        insOptions,
        insValue: insRes.InstanceIdList,
      }));
    } else {
      setInsState(last => ({
        ...last,
        insOptions: [],
        insValue: [],
      }));
    }
  }, [insRes, insError]);

  const {
    result: metricConfigRes,
    error: metricConfigError,
    isLoading: metricConfigLoading,
    reload: metricConfigReload,
  } = fetchData({
    name: 'DescribeArchGuardProductMetricConfig',
    params: {
      Product: currNode.name,
    },
    fetch: DescribeArchGuardProductMetricConfig,
  });

  const restoreDefaultThrottleFetch = useCallback(
    (metricConfigRes) => {
      const metrics = metricConfigRes?.MetricConfigList?.map(v => v.Metric) || [];
      // 用于 查询指标并发控制 的数据
      store.dispatch(setDrawerMetrics(metrics.slice(metricsFetchThrottle)));
      store.dispatch(setCurDrawerMetrics(metrics.slice(0, metricsFetchThrottle)));
    },
    [metricConfigRes]
  );

  useEffect(() => {
    if (!metricConfigRes?.Error && !metricConfigError && metricConfigRes?.MetricConfigList) {
      setMetric(metricConfigRes.MetricConfigList);
	  // 构造指标选项
	  const tmpOptions = (metricConfigRes.MetricConfigList || []).map(i => ({
        value: i.Metric,
        text: i.MetricName,
      }));
	  setMetricOptions(tmpOptions);
	  // 默认全部勾选上
	  setSelectMetric(tmpOptions.map(i => i.value));
      restoreDefaultThrottleFetch(metricConfigRes);
    } else {
      setMetric([]);
    }
  }, [metricConfigRes, metricConfigError]);

  useEffect(() => {
    const nodeStatus = find(taskResult?.NodeDetail, item => item?.NodeUuId === currNode?.key) || {};
    if (nodeStatus?.Status === ENodeTaskStatus.SUCCESS && firstLoadData.current) {
      setKey(`${currNode?.key}`);
      metricConfigReload();
      firstLoadData.current = false;
    }
  }, [currNode.key, taskResult?.NodeDetail]);

  return (
		<>
			<Drawer
				style={{ width: 700 }}
				visible
				title={t('节点监控 - {{attr0}}', { attr0: currShapeName ?? '-' })}
				destroyOnClose
				outerClickClosable={false}
				subtitle={isInstanceLoading && <StatusTip status="loading" loadingText={t('数据更新中...')}/>}
				className="monitor-drawer"
				footer={
					<div className="monitor-drawer-footer">
						<Button
							type="primary"
							onClick={() => {
							  restoreDefaultThrottleFetch(metricConfigRes);
							  setModalVisible(true);
							}}
							disabled={!metric.length}
						>
							{t('全屏查看')}
						</Button>
						<Button
							type="weak"
							style={{ marginLeft: 15 }}
							onClick={() => {
							  store.dispatch(setDrawerVisibleMapAction({ nodeCustomerDrawerVisible: false }));
							  store.dispatch(setNodeCustomerDrawerRangeTimeAction([moment().startOf('day')
							    .toDate(), moment().endOf('day')
							    .toDate()]));
							}}
						>
							{t('关闭')}
						</Button>
					</div>
				}
				onClose={() => {
				  store.dispatch(setDrawerVisibleMapAction({ nodeCustomerDrawerVisible: false }));
				  store.dispatch(setNodeCustomerDrawerRangeTimeAction([moment().startOf('day')
				    .toDate(), moment().endOf('day')
				    .toDate()]));
				}}
			>
				{isGuarding && <StatusReport />}
				<div className="monitor-content">
					<div style={{ display: 'flex', alignItems: 'center' }}>
						<label
							style={{
							  display: 'inline-block',
							  width: 75,
							  textAlign: 'left',
							}}
						>
							{t('时间范围：')}
						</label>
						<DateRangePicker
							className={s.datePicker}
							disableDate={{ after: moment().format() }}
							value={nodeCustomerDrawerRangeTime}
							presets={presets}
							onChange={(val) => {
							  restoreDefaultThrottleFetch(metricConfigRes);
							  store.dispatch(setNodeCustomerDrawerRangeTimeAction(val));
							}}
							enableTimePicker
						/>
					</div>
					<div style={{ display: 'flex', alignItems: 'center', marginTop: 10 }}>
						<div style={{ marginRight: 15 }}>
							<label
								style={{
								  display: 'inline-block',
								  width: 75,
								  textAlign: 'left',
								}}
							>
								{t('实例列表：')}
							</label>
							<SelectMultiple
								allOption={{
								  value: 'all',
								  text: t('全部实例'),
								  tooltip: t('全部实例'),
								}}
								value={insState.insValue}
								defaultValue={insState.insOptions.map(v => v?.value)}
								searchValue={inputValue}
								staging
								searchable
								size="m"
								placeholder={t('实例列表')}
								onlySubmitFilterFromAll
								appearance="button"
								options={insState.insOptions}
								onSearchValueChange={(keyword) => {
								  setInputValue(keyword);
								}}
								tips={insLoading && <StatusTip status={'loading'} onRetry={() => insReload()} />}
								onChange={(value) => {
								  restoreDefaultThrottleFetch(metricConfigRes);
								  setInsState(last => ({
								    ...last,
								    insValue: value,
								  }));
								}}
							/>
						</div>
						<div style={{ marginRight: 15 }}>
							<label
								style={{
								  display: 'inline-block',
								  width: 75,
								  textAlign: 'left',
								}}
							>
								{t('指标选择：')}
							</label>
							<SelectMultiple
								allOption={{
								  value: 'all',
								  text: t('全部指标'),
								  tooltip: t('全部指标'),
								}}
								searchable
								value={selectMetric}
								size="m"
								placeholder={t('请选择监控指标')}
								appearance="button"
								options={metricOptions}
								tips={insLoading && <StatusTip status={'loading'} onRetry={() => insReload()} />}
								onChange={(value) => {
								  setSelectMetric(value);
								}}
							/>
						</div>
					</div>
					<BusinessHealthAlert timeRange={nodeCustomerDrawerRangeTime} instanceList={insState.insValue} />
					<div>
						<Collapse defaultActiveIds={['1']} className={s.collapse}>
							<Collapse.Panel id="1" title={currNode?.cName || currNode?.name}>
								{metric?.map((v, i) => {
								  // 过滤掉没有选中的指标
								  if (!selectMetric.includes(v.Metric)) {
								    return '';
								  }
								  return (
									<Charts
										key={`metric-drawer-${i}-${v.Metric}`}
										bordered
										metric={v}
										query={{
										  ArchId: archInfo.archId,
										  NodeUuid: currNode.key,
										  InstanceIdList: insState.insValue,
										  StartTime: moment(nodeCustomerDrawerRangeTime[0]).format('YYYY-MM-DD HH:mm:ss'),
										  EndTime: moment(nodeCustomerDrawerRangeTime[1]).format('YYYY-MM-DD HH:mm:ss'),
										}}
									/>
								  );
								})}
								{/* 未选择监控指标的提示 */}
								{!selectMetric?.length && !metricConfigLoading && <div style={{ display: 'flex', justifyContent: 'center' }}>
									<Status  icon='chart' title={t('请选择监控指标')} />
								</div>}
								<div style={{ display: 'flex', justifyContent: 'center' }}>
									{metricConfigLoading && <StatusTip status={'loading'} onRetry={() => metricConfigReload()} />}
									{!metricConfigLoading && !metric?.length && (
										<div
											className="LoadingWrap"
											style={{
											  display: 'flex',
											  flexDirection: 'column',
											  alignItems: 'center',
											  marginTop: 50,
											}}
										>
											<img src={nodata} />
											<p style={{ marginTop: 15, fontSize: 13 }}>{t('暂无数据')}</p>
										</div>
									)}
								</div>
							</Collapse.Panel>
						</Collapse>
					</div>
				</div>
			</Drawer>
			<Modal
				visible={modalVisible}
				onClose={() => setModalVisible(false)}
				className={`full-screen-modal ${s.fullScreen}`}
				size={'98%'}
			>
				<Space direction="vertical" style={{ width: '100%', padding: '0 15px', boxSizing: 'border-box' }}>
					<div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', marginTop: -20 }}>
						<div style={{ display: 'flex', alignItems: 'center', marginRight: 25, marginTop: 10 }}>
							<label
								style={{
								  display: 'inline-block',
								  width: 65,
								  textAlign: 'left',
								}}
							>
								{t('时间范围：')}
							</label>
							<DateRangePicker
								className={s.datePicker}
								disableDate={{ after: moment().format() }}
								value={nodeCustomerDrawerRangeTime}
								presets={presets}
								onChange={(val) => {
								  restoreDefaultThrottleFetch(metricConfigRes);
								  store.dispatch(setNodeCustomerDrawerRangeTimeAction(val));
								}}
								enableTimePicker
							/>
						</div>
						<div style={{ marginTop: 10 }}>
							<label
								style={{
								  display: 'inline-block',
								  width: 65,
								  textAlign: 'left',
								}}
							>
								{t('实例列表：')}
							</label>
							<SelectMultiple
								allOption={{
								  value: 'all',
								  text: t('全部实例'),
								  tooltip: t('全部实例'),
								}}
								value={insState.insValue}
								defaultValue={insState.insOptions.map(v => v?.value)}
								searchValue={inputValue}
								staging
								searchable
								size="m"
								placeholder={t('实例列表')}
								onlySubmitFilterFromAll
								appearance="button"
								options={insState.insOptions}
								onSearchValueChange={(keyword) => {
								  setInputValue(keyword);
								}}
								tips={insLoading && <StatusTip status={'loading'} onRetry={() => insReload()} />}
								onChange={(value) => {
								  restoreDefaultThrottleFetch(metricConfigRes);
								  setInsState(last => ({
								    ...last,
								    insValue: value,
								  }));
								}}
							/>
						</div>
					</div>
					<Row gutter={20}>
						{metric?.map((v, i) => (
							<Col key={`metric-full-${i}-${v.Metric}`} xs={12} sm={12} md={12} lg={6} xl={6} xxl={6}>
								<Charts
									bordered
									metric={v}
									query={{
									  ArchId: archInfo.archId,
									  NodeUuid: currNode.key,
									  InstanceIdList: insState.insValue,
									  StartTime: moment(nodeCustomerDrawerRangeTime[0]).format('YYYY-MM-DD HH:mm:ss'),
									  EndTime: moment(nodeCustomerDrawerRangeTime[1]).format('YYYY-MM-DD HH:mm:ss'),
									}}
								/>
							</Col>
						))}
					</Row>
				</Space>
			</Modal>
		</>
  );
}
