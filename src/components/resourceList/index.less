.nodeSettingWrap {
  width: 725px !important;

  .app-cloud-arch-drawer__body-inner{
    height: 100%;
  }

  .tea-drawer__body-inner {
    height: 100%;
  }
}

.wrapPlus{
  width: 1030px !important;
}

.batchModal {
  .batchItem {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

}

.resourceListWrap {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;

  .resourceList {
    height: 100%;
    flex: 1;
    overflow: auto;

    .tea-accordion {
      margin-bottom: 10px;
    }

    .tea-table__header .tea-icon {
      margin-top: 0 !important;
    }

    .tea-accordion__header-title{
      width: calc(100% - 16px);
    }

    .tea-icon {
      margin-top: 2px;
      vertical-align: top !important;
    }

    .resourceTitle {
      font-size: 14px;
      font-weight: 600;
      color: #13161B;

      .resourceWrap {
        display: flex;
        align-items: center;

        .icon {
          margin-right: 20px;
        }

        .resourceNum {
          background-color: #F1F2F6;
          padding: 2px 10px 6px 10px;
          flex: 1;
          .numType {
            margin-right: 10px;

            span {
              font-size: 20px;
              font-weight: 600;
              margin-left: 4px;
            }
          }
        }
      }
    }

    .resourceConfig {
      padding: 16px;

      .label {
        font-size: 14px;
        font-weight: 700;
        color: #262F3E;
        margin: 10px 0 20px 0;
      }

      .tea-accordion__header-title {
        font-size: 14px;
        font-weight: 600;
        color: #000;
      }

      .searchWrap {
        display: flex;
        align-items: center;
        margin: 10px 0;

        &>button {
          margin-left: 10px;
        }

        .searchBox {
          width: 300px !important;
          min-width: 300px;
        }
      }
    }
  }

  .instanceTableWrap {
    tr.is-disabled {
      pointer-events: none;
    }
  }
}

.btnWrap {
  border-top: 1px solid #ebebeb;
  padding: 20px;
  background: #fff;
  text-align: right;
 
  .saveBtn {
    margin-right: 10px;
  }
}

.productPolicyWrap {
  .tea-datetimepicker__input {
    width: 100% !important;
  }

  .tea-inputnum {
    .tea-input {
      width: calc(100% - 60px) !important;
    }
  }
}