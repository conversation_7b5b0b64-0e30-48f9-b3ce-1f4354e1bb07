import React, { useState, useEffect, forwardRef } from 'react';
import { Bubble, Input, Form } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
interface Props {
  inputId?: string,
  value?: string,
  CallBack?: Function,
  placeholder?: string,
  required?: Boolean,
  regRule?: RegExp,
  inputValidate?: Function
}

function MyInput({ inputId, value, CallBack, placeholder = '', required = false, regRule, inputValidate }: Props) {
  // 输入框状态
  const [status, setStatus] = useState<any>('success');
  // 输入框提示文字
  const [message, setMessage] = useState('');

  useEffect(() => {
    // 必填校验
    if (required && value === '') {
      setStatus('error');
      setMessage(t('此项为必填项'));
      return;
    }
    // 正则校验
    if (regRule) {
      if (regRule.test(value)) {
        setStatus('success');
        setMessage('');
      } else {
        setStatus('error');
        setMessage(t('请输入正确的值'));
      }
      return;
    }
    // 通过必填校验或没有必填校验
    setStatus('success');
    setMessage('');
  }, [value]);

  useEffect(() => {
    inputValidate?.(status === 'success');
  }, [status]);

  return (
        <>
            <Bubble placement={'top'} visible={false}>
                <Form hideLabel>
                    <Form.Item status={status} message={message} showStatusIcon={false}>
                        <Input id={inputId}
                            onChange={(value) => {
                              CallBack(value.trim());
                            }}
                            defaultValue={value}
                            placeholder={placeholder}
                            size="full" />
                    </Form.Item>
                </Form>
            </Bubble>
        </>
  );
}

export default forwardRef(MyInput);
