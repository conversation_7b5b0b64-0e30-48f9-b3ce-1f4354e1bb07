import React, { useState, useEffect, useMemo } from 'react';
import { TagSearchBox, message } from '@tencent/tea-component';
import { AttributeValue } from '@tencent/tea-component/src/tagsearchbox/AttributeSelect';
import _ from 'lodash';
import { archInfo } from '@src/utils/caching';
import { DescribeTags } from '@src/service/api/baseInfo';
import { t } from '@tea/app/i18n';

interface IProps {
  filterInfo: Array<any>;
  nodeInfo: any;
  filterChange: (tags: Array<any>) => void;
}

function SearchBox({ filterInfo, nodeInfo, filterChange }: IProps) {
  // tag
  const [tags, setTags] = useState([]);
  const [currenttags, setcurrenttags] = useState([]);
  // 标签选项
  const [tagInfo, setTagInfo] = useState([]);

  // 把filterInfo 转换为 attributes
  const attributes: Array<AttributeValue> = useMemo(() => {
    const tagOptions = getTagOptions(tagInfo);
    const tmp: Array<AttributeValue> = [];
    filterInfo.map((i) => {
      if (i.Type == 'Tag') {
        tmp.push({
          type: ['multiple', { all: false, searchable: true }],
          key: i.FilterName,
          name: i.FilterAlias,
          values: tagOptions,
        });
      } else {
        tmp.push({
          type: 'input',
          key: i.FilterName,
          name: i.FilterAlias,
        });
      }
    });
    return tmp;
  }, [filterInfo, tagInfo]);

  // 查询产品标签信息
  const getTags = () => {
    let params: any = {
      Product: nodeInfo.Product,
    };
    // 兼容租户端传参
    if (archInfo.env === 'CONSOLE') {
      params = {
        MapId: archInfo?.archInfo?.archId || '',
        NodeUuid: nodeInfo.NodeUuid,
      };
    }
    DescribeTags(params).then((res: any) => {
      if (_.isEmpty(res.Tags)) {
        setTagInfo([]);
      } else {
        setTagInfo(res.Tags);
      }
    });
  };

  // 获取标签下拉选项
  function getTagOptions(tagInfo) {
    const tmpList = [];
    (tagInfo || []).map((t) => {
      const tk = t.TagKey;
      t.TagValues.map((tv) => {
        const tmp = {
          key: tk,
          name: `${tk}::${tv}`, // 指定分隔符
        };
        tmpList.push(tmp);
      });
    });

    return tmpList;
  }

  // 监听tags变化
  useEffect(() => {
    const Filter = [];
    const tagsTmp = [];
    tags.map((i) => {
      const tmp = _.cloneDeep(i);
      if (i.attr) {
        const Name = i.attr.key;
        const Values = [];
        // 判断是否只支持单个，如果只支持单个，则默认取第一个
        if (filterInfo.filter((item) => {
          if (item.FilterName === Name) {
            return item;
          }
        }).length) {
          const item = filterInfo.filter((item) => {
            if (item.FilterName === Name) {
              return item;
            }
          })[0];
          if (item.Type === 'Single') {
            tmp.values = [i.values[0]];
            Values.push(i.values[0].name);
            if (i.values.length > 1) {
              message.warning({ content: t('{{attr0}}只支持单个实例搜索', { attr0: item.FilterAlias }) });
            }
          } else {
            i.values.map((j) => {
              if (Values.indexOf(j.name) === -1) {
                Values.push(j.name);
              }
            });
          }
        }
        Filter.push({
          Name,
          Values,
        });
        tagsTmp.push(tmp);
      }
    });
    filterChange(Filter);
    setcurrenttags(tagsTmp);
  }, [tags]);

  useEffect(() => {
    getTags();
  }, []);

  return (
		<>
			<TagSearchBox
				className='searchBox'
				hideHelp
				attributes={attributes}
				value={currenttags}
				onChange={(tags) => {
				  setTags(tags);
				}}
			/>
		</>
  );
}

export default SearchBox;
