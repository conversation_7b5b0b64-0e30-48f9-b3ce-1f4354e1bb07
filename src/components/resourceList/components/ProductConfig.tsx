import React, { useEffect, useState } from 'react';
import { message, Row, Col, Text, Input, Form, Tag, DatePicker, SelectMultiple, InputNumber, Icon, Bubble, Select } from '@tencent/tea-component';
import { archInfo, guardInfo } from '@src/utils/caching';
import moment from 'moment';
import { t } from '@tea/app/i18n';
import _ from 'lodash';
import store from '@src/origin-store/store';
const { TextArea } = Input;
const { RangePicker } = DatePicker;
// 未填写
const noSet = <Text style={{ color: 'LightGray' }} verticalAlign="middle">{t('未填写')}</Text>;

interface IProps {
  canEdit: boolean;
  productPolicy: any[];
  productPolicyChange: (policy: any[]) => void;
  nodeInfo: any;
  cdnPolicyChange?: (cdnPolicy: boolean) => void;
}
// 护航资源列表
const ProductConfig = ({
  canEdit,
  productPolicy = [],
  productPolicyChange,
  nodeInfo,
  cdnPolicyChange,
}: IProps) => {
  // 历史护航信息
  const { historyGuard, drawerVisibleMap } = store.getState().guard;

  // 已经保存过的产品报备信息
  const savedProductPolicy = (guardInfo.ProductTemplate || [])
    .find(i => nodeInfo.NodeUuid === i.NodeUuid)?.Policy || [];

  // 产品报备信息,如果没有则使用最新的报备信息
  const [ProductPolicy, setProductPolicy] = useState(_.cloneDeep(savedProductPolicy.length
    ? savedProductPolicy
    : productPolicy));

  useEffect(() => {
    // 如果是打开的历史护航,就显示旧单的产品报备信息
    if (drawerVisibleMap.historyDrawerVisible && historyGuard?.GuardId) {
      const oldProductPolicy = (historyGuard.ProductTemplate || [])
        .find(i => nodeInfo.NodeUuid === i.NodeUuid)?.Policy || [];
      setProductPolicy(oldProductPolicy);
    }
  }, [historyGuard]);

  // 更新产品报备信息
  function updateProductPolicy(metricName, value) {
    setProductPolicy((oldPolicy) => {
      // 找到原有policy,并修改值
      const policy = oldPolicy.find(i => i.MetricName === metricName);
      policy.Value = value;
      return _.cloneDeep(oldPolicy);
    });
  }

  useEffect(() => {
    // 更新上层
    productPolicyChange(ProductPolicy);
  }, [ProductPolicy]);

  return <div style={{ marginTop: 10 }}>
    <div>
      <Row className='productPolicyWrap'>
        <Col>
          {canEdit ? ProductPolicy.map((policy: any, idx) => {
            // 格式化信息
            let FormatType: any = {};
            if (archInfo.env === 'ISA') {
              FormatType = policy.FormatType?.[0] || {};
            } else {
              FormatType = policy.FormatType ? JSON.parse(policy.FormatType)[0] : {};
            }
            let inputDom = <TextArea
              style={{ width: '100%' }}
              rows={4}
              placeholder={policy.Desc || ''}
              value={policy.Value || ''}
              onChange={(value) => {
                updateProductPolicy(policy.MetricName, value);
              }}
            />;
            if (policy.FormatType) {
              if (FormatType.Type === 'DatetimeMultiple' || FormatType.Type === 'DatetimeSingle') {
                const value = policy.Value ? policy.Value.split(';') : [];
                // 显示的值
                const [LiveTimeShow, setLiveTimeShow] = useState<any>('');
                inputDom = <div>
                  <Bubble arrowPointAtCenter placement="top" content={t('请选择到小时')} >
                    <RangePicker
                      value={LiveTimeShow}
                      range={[moment(guardInfo.StartTime), moment(guardInfo.EndTime)]}
                      disabled={FormatType.Type === 'DatetimeSingle' && value.length >= 1}
                      format="YYYY-MM-DD HH:mm"
                      showTime={{ format: 'HH:mm' }}
                      onChange={(v) => {
                        setLiveTimeShow('');
                        const tmp = _.cloneDeep(value);
                        const d = `${v[0].format('YYYY/MM/DD HH:mm')}~${v[1].format('YYYY/MM/DD HH:mm')}`;
                        if (tmp.indexOf(d) === -1) {
                          tmp.push(d);
                        }
                        // 时间排序
                        tmp.sort((a, b) => {
                          const dateA = new Date(a.split('~')[0]);
                          const dateB = new Date(b.split('~')[0]);
                          // @ts-ignore
                          return dateA - dateB;
                        });
                        updateProductPolicy(policy.MetricName, tmp.join(';'));
                      }}
                    />
                  </Bubble>
                  {value.length > 0 && (
                    <div>
                      {value.map((i, index) => (
                          <Tag
                            onClose={() => {
                              updateProductPolicy(policy.MetricName, value.filter(j => j !== i).join(';'));
                            }}
                            key={index}
                          >
                            {i}
                          </Tag>
                      ))}
                    </div>
                  )}
                </div>;
              } else if (FormatType.Type === 'SelectSingle' || FormatType.Type === 'SelectMultiple') {
                let selectDom = <></>;
                // 临时保存当前值
                const temp = _.cloneDeep(policy.Value ? policy.Value.split(';') : []);
                let lastOne = '';
                let relatedValue = '';
                if (temp.length) {
                  // 下拉关联的最后一个值
                  lastOne = temp[temp.length - 1];
                  const list = lastOne ? lastOne.split(/::(.+)/) : [];
                  temp[temp.length - 1] = list[0] || '';
                  relatedValue = list.slice(1).join('');
                }
                // 构造下拉选项
                const options = FormatType.Value.map(i => ({ value: i })) || [];
                if (FormatType.Type === 'SelectSingle') {
                  // 当前产品信息的值
                  const Value = temp?.[0] || '';
                  // 初始化实例Policy是否展示
                  if (policy.MetricName === 'CDNRequirementType') {
                    if (!Value) {
                      updateProductPolicy(policy.MetricName, t('常量护航需求'));
                    }
                    cdnPolicyChange?.(Value === '突发护航需求');
                  }
                  selectDom = <Select
                    style={{ width: policy.MetricName === 'CDNConverageDemand' ? 160 : '100%', verticalAlign: 'top' }}
                    appearance="button"
                    options={options}
                    value={Value}
                    onChange={(value) => {
                      updateProductPolicy(policy.MetricName, value);
                      if (policy.MetricName === 'CDNRequirementType') {
                        if (value === '突发护航需求') {
                          message.warning({ content: t('如果选择“突发护航需求”，请完善“已选资源”的报备信息') });
                        }
                        cdnPolicyChange?.(value === '突发护航需求');
                      }
                    }}
                  />;
                } else {
                  // 当前产品信息的值
                  const Value = temp || [];
                  selectDom = <SelectMultiple
                    style={{ width: '100%', verticalAlign: 'top' }}
                    clearable
                    options={options}
                    staging={false}
                    appearance="button"
                    value={Value}
                    onChange={(value) => {
                      updateProductPolicy(policy.MetricName, value.join(';'));
                    }}
                  />;
                }
                // 关联的类型
                let relatedDom = <></>;
                if (policy.FormatType?.length > 1) {
                  const relatedData = policy.FormatType[1] || {};
                  if (relatedData.Type === 'SelectRelated' && relatedData.Value?.[0]?.RelatedType === 'Text') {
                    relatedDom = <TextArea
                      disabled={!temp.includes(relatedData.Value?.[0]?.RelatedValidWithValue)}
                      style={{ marginLeft: 20, width: 'calc(100% - 180px)' }}
                      rows={4}
                      value={relatedValue || ''}
                      onChange={(value) => {
                        if (value) {
                          temp[temp.length - 1] += `::${value}`;
                        } else {
                          temp[temp.length - 1] = temp[temp.length - 1];
                        }
                        updateProductPolicy(policy.MetricName, temp.join(';'));
                      }}
                    />;
                  }
                }
                inputDom = <div>{selectDom}{relatedDom}</div>;
              } else if (FormatType.Type === 'Num') {
                // 这里组件的value绑定有问题，无法满足校验，要拆成两个
                inputDom = <InputNumber
                  key={`has${idx}`}
                  value={Number(policy.Value)}
                  onChange={(value: any) => {
                    updateProductPolicy(policy.MetricName, String(value));
                  }}
                  min={FormatType.Value[0] || 0}
                  max={FormatType.Value[1]}
                  step={1}
                />;
              }
            }
            return <section key={policy?.CNName}>
              <Row verticalAlign={'top'}>
                <Col span={6} >
                  {policy.IsRequired && guardInfo.Standard !== 3 && <Text style={{ color: 'red' }} verticalAlign="middle">*</Text>}
                  <Text theme="label" verticalAlign="middle">{policy.CNName || ''}</Text>
                  {
                    policy.Desc && <Bubble
                      arrowPointAtCenter
                      placement="top"
                      content={policy.Desc}
                    >
                      <Icon style={{ marginLeft: 4 }} type="info" />
                    </Bubble>
                  }
                </Col>
                <Col span={18}>
                  <Form.Control showStatusIcon={false} status={(!policy.Value && policy.IsRequired && guardInfo.Standard !== 3) ? 'error' : 'success'} message={(!policy.Value && policy.IsRequired && guardInfo.Standard !== 3) ? t('此项为必填项') : ''}>
                    {inputDom}
                  </Form.Control>
                </Col>
              </Row>
            </section>;
          })
            : ProductPolicy.map((policy: any) => {
              // 初始化实例Policy是否展示
              if (policy.MetricName === 'CDNRequirementType') {
                cdnPolicyChange?.(policy.Value === '突发护航需求');
              }
              return <section key={policy?.CNName}>
                <Row verticalAlign={'top'}>
                  <Col span={10} >
                    <Text theme="label" verticalAlign="middle">{policy.CNName || ''}</Text>
                    {
                      policy.Desc && <Bubble
                        arrowPointAtCenter
                        placement="top"
                        content={policy.Desc}
                      >
                        <Icon style={{ marginLeft: 4 }} type="info" />
                      </Bubble>
                    }
                  </Col>
                  <Col span={14}><Text theme="label" verticalAlign="middle">{policy.Value || noSet}</Text></Col>
                </Row>
              </section>;
            })
          }
        </Col>
      </Row>
    </div>
  </div >;
};
export default ProductConfig;
