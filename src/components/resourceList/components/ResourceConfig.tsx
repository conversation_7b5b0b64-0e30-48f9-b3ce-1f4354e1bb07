import React, { useEffect, useState } from 'react';
import { Collapse, Icon, Row, Col, Text, Input } from '@tencent/tea-component';
import InstanceConfig from './InstanceConfig';
import ProductConfig from './ProductConfig';
import { t } from '@tea/app/i18n';
import { archInfo } from '@src/utils/caching';
// 未填写
const noSet = <Text style={{ color: 'LightGray' }} verticalAlign="middle">{t('未填写')}</Text>;

interface Iprops {
  remarkChange?: (val: any) => void;
  remark?: any;
  requested?: any;
  nodeInfo?: any;
  productFilter?: any;
  productColumn?: any;
  instancePolicy?: any;
  productPolicy?: any;
  productPolicyChange?: (val: any) => void;
  instanceChange?: (val: any) => void;
  savedInstance?: any;
  canEdit?: boolean;
  consoleBaseInfo?: boolean;
}
// 护航资源列表
const ResourceConfig = ({
  remarkChange,
  remark = {},
  requested,
  nodeInfo,
  productFilter,
  productColumn,
  instancePolicy,
  productPolicy = [],
  instanceChange,
  savedInstance,
  productPolicyChange,
  canEdit,
  consoleBaseInfo = false,
}: Iprops) => {
  // 是否是运营端
  const isISA = archInfo.env === 'ISA';
  // 产品备注
  const [productRemark, setProductRemark] = useState<any>([]);
  // cdn是否需要填写实例信息
  const [cdnInstancePolicy, setCdnInstancePolicy] = useState(false);
  useEffect(() => {
    setProductRemark(remark);
  }, [remark]);

  useEffect(() => {
    remarkChange?.(productRemark);
  }, [JSON.stringify(productRemark)]);

  return <div className='resourceConfig'>
    <div className='label'>{t('{{NodeName}}护航配置', { NodeName: nodeInfo.NodeName })}</div>
    <Collapse defaultActiveIds={['instance', 'product']} icon={active => (active ? <Icon type="arrowdown" /> : <Icon type="arrowright" />)}>
      <Collapse.Panel id='instance' title={t('节点资源')}>
        <InstanceConfig
          cdnInstancePolicy={cdnInstancePolicy}
          canEdit={canEdit}
          consoleBaseInfo={consoleBaseInfo}
          savedInstance={savedInstance}
          instanceChange={instanceChange}
          requested={requested}
          nodeInfo={nodeInfo}
          productFilter={productFilter}
          productColumn={productColumn}
          instancePolicy={instancePolicy}
        />
      </Collapse.Panel>
      {productPolicy.length > 0 && isISA && <Collapse.Panel id='product' title={t('配置详情')}>
        {/* 配置详情组件 */}
        <ProductConfig
          cdnPolicyChange={(val) => {
            setCdnInstancePolicy(val);
          }}
          canEdit={canEdit}
          nodeInfo={nodeInfo}
          productPolicy={productPolicy}
          productPolicyChange={productPolicyChange}
        />
      </Collapse.Panel>}
      {canEdit ? <Row verticalAlign="middle" style={{ marginTop: 15, marginBottom: 10 }}>
        <Col span={6} >
          <Text theme="label">{t('备注')}</Text>
        </Col>
        <Col span={18} >
          <Input
            size='full'
            placeholder={t('护航特殊需求备注')}
            value={productRemark.Comment}
            onChange={(v) => {
              setProductRemark({ ...productRemark, Comment: v });
            }}
          />
        </Col>
      </Row>
        : <Row verticalAlign="middle" style={{ marginTop: 15, marginBottom: 10 }}>
          <Col span={10} >
            <Text theme="label">{t('备注')}</Text>
          </Col>
          <Col span={14} >
            <Text theme="label">{productRemark.Comment || noSet}</Text>
          </Col>
        </Row>
      }
    </Collapse>
  </div>;
};
export default ResourceConfig;
