import React, { useEffect, useState } from 'react';
import {
  Table,
  StatusTip,
  Button,
  Input,
  Form,
  SelectMultiple,
  InputNumber,
  Icon,
  Bubble,
  Switch,
  Modal,
  Select,
} from '@tencent/tea-component';
import { archInfo, appId, guardInfo } from '@src/utils/caching';
import { t } from '@tea/app/i18n';
import SearchBox from './SearchBox';
import { DescribeArchGuardProductInstances } from '@src/service/api/baseInfo';
import InstanceInput from './InstanceInput';
import _ from 'lodash';
import { findValueByPath, updateNestedField, Limit } from '@src/utils/common';
const { pageable, scrollable, selectable } = Table.addons;
// 报备类型
const reportType = ['Switch', 'Input', 'SelectSingle', 'SelectMultiple'];

interface IProps {
  nodeInfo: any;
  productFilter: any;
  productColumn: any;
  instancePolicy?: any;
  instanceChange?: any;
  savedInstance?: any;
  cdnInstancePolicy?: any;
  canEdit?: boolean;
  consoleBaseInfo?: boolean;
  requested?: boolean;
}

// 护航资源列表
const InstanceConfig = ({
  cdnInstancePolicy,
  canEdit,
  consoleBaseInfo = false,
  requested,
  nodeInfo,
  productFilter,
  productColumn,
  instancePolicy = [],
  instanceChange,
  savedInstance,
}: IProps) => {
  // 可批量操作的报备信息
  const [batchList, setBatchList] = useState([]);
  // 架构图id
  const archId = archInfo?.archInfo?.archId || '';
  // 搜索框选择值
  const [filter, setFilter] = useState([]);
  // 实例列表数据
  const [instanceList, setInstanceList] = useState([]);
  // 实例列表查询接口常规参数
  const [offset, setOffset] = useState<number>(0);
  const [limit, setLimit] = useState<number>(20);
  const [loading, setLoading] = useState<boolean>(false);
  const [total, setTotal] = useState<number>(0);

  // 修改过的实例
  const [modifiedInstance, setModifiedInstance] = useState([]);
  // 勾选中的实例
  const [selectInstanceId, setSelectInstanceId] = useState([]);

  // 组合列 实例ID
  const comColumn: any = [
    {
      key: 'Instance',
      header: t('ID/实例名'),
      render: instance => (
        <>
          <p>
            <a>{instance.InstanceId}</a>
          </p>
          <div style={{ overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }} title={instance.InstanceName} >
            {instance.InstanceName}
          </div>
        </>
      ),
    },
  ];
  const guardColumn = {
    width: 120,
    key: 'JoinGuard',
    header: t('护航资源（{{guardTotalNumer}}）', { guardTotalNumer: modifiedInstance.filter(i => i.JoinGuard === 1)?.length || 0 }),
    render: instance => (
      <>
        <Switch
          onChange={(v) => {
            valueChange(instance, v, 'JoinGuard', 'Info', '', 'Switch');
          }}
          value={instance.JoinGuard === 1}
        />
      </>
    ),
  };

  // 实例展示列
  const [columns, setColumns] = useState(_.cloneDeep(comColumn) || []);
  // 批量编辑--弹窗
  const [batchEditorVisible, setBatchEditorVisible] = useState(false);

  // init
  function initInstance(searchList, notAll = true) {
    const newList = [];
    const oldList = [];
    searchList.map((i) => {
      // 是否已经保存在修改的实例中
      const instance = modifiedInstance.find(item => item.InstanceId === i.InstanceId);
      if (instance) {
        // 如果有，直接显示
        oldList.push(instance);
      } else {
        //  如果没有，需要构造policy
        const policys = _.cloneDeep(instancePolicy || []);
        (instancePolicy || []).map((i, index) => {
          policys[index].Value = guardInfo.ExpectedEnlargeTimes || 1.5;
          policys[index].Days = guardInfo.ExpectedEnlargeDays || 7;
          if (nodeInfo.Product === 'cdn') {
            policys[index].IsRequired = cdnInstancePolicy ? i.IsRequired : false;
            policys[index].FieldType = cdnInstancePolicy ? i.FieldType : (i.FieldType === 'float' ? 'int' : i.FieldType);
          }
          // CDN默认值为1
          if (policys[index].MetricName.trim() == 'CDNBandwidth' || policys[index].MetricName.trim() == 'CDNQPS') {
            policys[index].Value = 0.0;
          }
        });
        // 标签
        const showTags = [];
        if (i.Tag?.length) {
          (i.Tag || []).map((i) => {
            const item = `${i.TagKey}:${i.TagValues}`;
            showTags.push(item);
          });
        }
        // 构造之后的实例
        const obj = {
          ...i,
          MapId: archId,
          NodeUuid: nodeInfo.NodeUuid,
          AppId: Number(appId),
          Product: nodeInfo.Product,
          InstanceTag: showTags.length ? showTags.join('; ') : '',
          Policy: policys,
        };
        newList.push(obj);
      }
    });
    setModifiedInstance(list => [...list, ...newList]);
    // 不是全选，就说明说搜索查询，更新当前展示的实例。是全选，就设置选中Id
    if (notAll) {
      setInstanceList([...newList, ...oldList]);
    } else {
      setSelectInstanceId([...newList, ...oldList].map(i => i.InstanceId));
    }
  }

  // 分页查询实例
  function getInstanceList(Offset?, Limit?) {
    setLoading(true);
    const params = {
      MapId: archId,
      NodeUuid: nodeInfo.NodeUuid,
      Offset,
      Limit,
      Filter: filter,
    };
    DescribeArchGuardProductInstances(params)
      .then((res: any) => {
        setTotal(res.TotalCount || 0);
        initInstance(res.Instance || []);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function getPageInstance(pageSize = 1) {
    return new Promise((resolve, reject) => {
      const params = {
        MapId: archId,
        NodeUuid: nodeInfo.NodeUuid,
        Offset: (pageSize - 1) * Limit,
        Limit,
        Filter: filter,
      };
      DescribeArchGuardProductInstances(params)
        .then((res: any) => {
          resolve(res.Instance || []);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  // 获取当前节点全部实例
  function getAllInstance() {
    // 分页查询结果
    const resultList = [];
    // 总页数
    const pages = Math.ceil(nodeInfo.Count / Limit);
    for (let i = 1; i <= pages; i++) {
      resultList.push(getPageInstance(i));
    }
    Promise.all(resultList).then((res) => {
      if (res?.length) {
        initInstance((res.flat() || []), false);
      }
    });
  }

  // 资源勾选
  function selectChange(keys, ctx) {
    // 点击全选框
    if (ctx.check.all) {
      if (ctx.check.value) {
        getAllInstance();
      } else {
        setSelectInstanceId([]);
      }
    } else {
      // 点击复选框
      setSelectInstanceId(keys);
    }
  }

  /*
    resource:要修改的实例
    value：要修改的值
    FilterCorrespondName：DataFrom为Info时要修改的字段名
    DataFrom：数据来源，Info、Extra、Policy
    Location：DataFrom为Extra、Policy时要修改的字段名
    Type：数据的显示类型
    index：当前点击数据的下标
    inputType：输入框类型
    */
  function valueChange(
    resource,
    value,
    FilterCorrespondName?,
    DataFrom?,
    Location?,
    Type?,
    index?,
    inputType?,
    inputId?
  ) {
    const tmp = _.cloneDeep(modifiedInstance);
    tmp.map((i: any, index) => {
      if (i.InstanceId === resource.InstanceId) {
        if (DataFrom === 'Info') {
          tmp[index][FilterCorrespondName] = value;
          if (Type === 'Switch') {
            tmp[index][FilterCorrespondName] = value ? 1 : 0;
          }
        } else if (DataFrom === 'Extra') {
          const extraData = tmp[index].Extra ? JSON.parse(tmp[index].Extra) : {};
          // 修改Extra里对应的值
          updateNestedField(extraData, Location, value);
          if (Type === 'Switch') {
            updateNestedField(extraData, Location, value ? 1 : 0);
          }
        } else if (DataFrom === 'Policy') {
          const policy = _.cloneDeep(tmp[index].Policy);
          policy.map((j, index1) => {
            if (j.MetricName === Location) {
              if (j.Type === 'Other') {
                policy[index1].OtherValue = value;
                // j.Value = 0
              } else {
                policy[index1].Value = value;
                // 如果输入类型是string，需要将值转换为string
                if (i.inputType === 'string') {
                  policy[index1].Value = `${value}`;
                }
                // 如果是开关，value要从Boolean转位number
                if (Type === 'Switch') {
                  policy[index1].Value = Number(!value);
                }
              }
            }
          });
          tmp[index].Policy = policy;
        }
      }
    });
    setModifiedInstance(tmp);
    updateShowInstance(tmp);
    if (inputId && document.getElementById(inputId)) {
      document.getElementById(inputId).focus();
    }
  }

  // 重新渲染当前展示实例
  function updateShowInstance(data) {
    const newList = [];
    instanceList.map((i) => {
      const instance = data.find(j => j.InstanceId === i.InstanceId);
      newList.push(_.cloneDeep(instance));
    });
    // 更新当前展示的实例
    setInstanceList([...newList]);
  }

  useEffect(() => {
    // 初始化已经保存的实例
    setModifiedInstance([...savedInstance]);
  }, [JSON.stringify(savedInstance)]);

  useEffect(() => {
    // 当修改的实例有变化时，更新最新的加入护航的实例到上层
    instanceChange(modifiedInstance.filter(i => i.JoinGuard === 1));
  }, [modifiedInstance]);

  // 初始化产品展示列
  useEffect(() => {
    setColumns([]);
    const newColumns = (_.cloneDeep(comColumn).map((i) => {
      i.width = 120;
      return i;
    })) || [];
    let colList = _.cloneDeep(productColumn);
    // cdn 的Policy过滤
    if (!cdnInstancePolicy && nodeInfo.Product === 'cdn') {
      colList = colList.filter(i => i.DataFrom !== 'Policy');
    }
    colList.map((i, index) => {
      // 列结构
      const colData: any = {
        key: i.FilterCorrespondName || i.Location || i.Uses + index,
        header: <>
          {i.FilterCorrespondName === 'Important'
            ? `${i.FilterName}（${modifiedInstance.filter(i => i.Important)?.length || 0}）`
            : i.FilterName}
          {/* {i.FilterName} */}
          {i.Desc.trim() !== '' && <Bubble
            arrowPointAtCenter
            placement="top"
            content={i.Desc}
          >
            <Icon type="info" />
          </Bubble>}
        </>,
        render: (resource, recordKey, recordIndex) => {
          // 实例列显示的值
          let result: any = '';
          if (i.Type === 'Switch') {
            // 开关列考虑三种情况，DataFrom为Info、Policy、Extra
            // 目前有Info、Policy
            let value: any = false;
            if (i.DataFrom === 'Info') {
              // 1:true表示勾选、0:false表示未勾选。
              value = resource[i.FilterCorrespondName] === 1;
            } else if (i.DataFrom === 'Policy') {
              (resource.Policy || []).map((j) => {
                if (j.MetricName === i.Location) {
                  // 0:true表示勾选、1:false表示未勾选。
                  value = j.Value === 0;
                }
              });
            } else if (i.DataFrom === 'Extra') {
              const extraData = resource.Extra ? JSON.parse(resource.Extra) : {};
              value = findValueByPath(extraData, i.Location);
            }
            return <>
              <Switch
                onChange={(v) => {
                  valueChange(resource, v, i.FilterCorrespondName, i.DataFrom, i.Location, i.Type, recordIndex);
                }}
                value={value}
              />
            </>;
          } if (i.Type === 'Input') {
            // 输入框考虑三种情况，DataFrom为Info、Policy、Extra
            // 目前只有Policy一种情况
            let value: any = 0;
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            let unit = '';
            let inputType = '';
            let inputId = '';
            let isRequired = false;
            let regRule: any = '';
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            let MetricName: any = '';
            if (i.DataFrom === 'Info') {
              value = resource[i.FilterCorrespondName];
            } else if (i.DataFrom === 'Extra') {
              const extraData = resource.Extra ? JSON.parse(resource.Extra) : {};
              value = findValueByPath(extraData, i.Location);
            } else if (i.DataFrom === 'Policy') {
              (resource.Policy || []).map((j) => {
                if (j.MetricName === i.Location) {
                  // eslint-disable-next-line @typescript-eslint/no-unused-vars
                  unit = j.Unit;
                  inputType = j.FieldType;
                  inputId = resource.InstanceId
                    + i.FilterCorrespondName
                    + i.Location
                    + i.DataFrom
                    + j.Type
                    + j.MetricName;
                  isRequired = j.IsRequired || false;
                  regRule = j.RegRule || null;
                  // eslint-disable-next-line @typescript-eslint/no-unused-vars
                  MetricName = j.MetricName;
                  if (j.Type === 'Other') {
                    value = j.OtherValue;
                  } else {
                    value = j.Value;
                  }
                }
              });
            }
            return <>
              {inputType === 'string'
                ? <InstanceInput
                  inputId={inputId}
                  value={value || ''}
                  CallBack={(v) => {
                    valueChange(
                      resource,
                      v,
                      i.FilterCorrespondName,
                      i.DataFrom,
                      i.Location,
                      i.Type,
                      recordIndex,
                      inputType,
                      inputId
                    );
                  }}
                  required={!resource.JoinGuard && isRequired}
                  regRule={regRule}
                />
                : <>
                  {/* 数值类型的policy的值为0无意义，要进行非0校验 */}
                  <Form hideLabel>
                    <Form.Item status={(resource.JoinGuard && value == 0) ? 'error' : 'success'} message={(resource.JoinGuard && value == 0) ? t('该值不能为0') : ''} showStatusIcon={false}>
                      <InputNumber
                        size="m"
                        onChange={(v) => {
                          valueChange(resource, v, i.FilterCorrespondName, i.DataFrom, i.Location, i.Type, recordIndex);
                        }}
                        min={0}
                        step={inputType === 'int' ? 1 : 0.1}
                        value={value}
                        hideButton={true}
                      // unit={unit}
                      />
                    </Form.Item>
                  </Form>
                </>
              }
            </>;
          } if (i.Type === 'Tag') {
            result = resource.InstanceTag.split('；');
            return <div style={{ overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }} title={result.join('\n')} >
              {result.join('；')}
            </div>;
          } if (i.Type === 'SelectSingle' || i.Type === 'SelectMultiple') {
            // 目前只考虑Policy一种情况
            if (i.DataFrom === 'Policy') {
              let value: any = '';
              let isRequired = false;
              const options = (i.ExtraShowName || []).map(item => ({ text: item.Value, value: item.Key }));
              (resource.Policy || []).map((j) => {
                if (j.MetricName === i.Location) {
                  if (j.Type === 'Other') {
                    value = j.OtherValue || '';
                  } else {
                    value = j.Value || '';
                  }
                  isRequired = j.IsRequired || false;
                }
              });
              const selectDom = i.Type === 'SelectSingle'
                ? <Select
                  style={{ width: '100%', verticalAlign: 'top' }}
                  appearance="button"
                  options={options}
                  value={value}
                  onChange={(value) => {
                    valueChange(resource, value, i.FilterCorrespondName, i.DataFrom, i.Location, i.Type, recordIndex);
                  }
                  }
                />
                : <SelectMultiple
                  style={{ width: '100%', verticalAlign: 'top' }}
                  clearable
                  options={options}
                  appearance="button"
                  value={value ? value.split(';') : []}
                  onChange={(value) => {
                    valueChange(resource, value.join(';'), i.FilterCorrespondName, i.DataFrom, i.Location, i.Type, recordIndex);
                  }}
                />;
              const reultDom =                isRequired
                ? <Form hideLabel style={{ width: '100%' }}>
                    <Form.Item status={(resource.JoinGuard && !value.length) ? 'error' : 'success'} message={(resource.JoinGuard && !value.length) ? t('此项为必填项') : ''} showStatusIcon={false}>
                      {selectDom}
                    </Form.Item>
                  </Form>
                :                  selectDom;
              return reultDom;
            }
          } else {
            // 普通信息展示列
            if (i.DataFrom === 'Info') {
              // 值从实例本身读取
              result = resource[i.FilterCorrespondName];
            } else {
              // 值从实例Extra字段里读取
              const extraData = resource.Extra ? JSON.parse(resource.Extra) : {};
              result = findValueByPath(extraData, i.Location);
            }
            // 根据映射展示对应的值
            if (i.ExtraShowName?.length) {
              const colValueItem = i.ExtraShowName.filter(i => i.Key === result);
              result = colValueItem.length ? colValueItem[0].Value : result;
            }
            return <div style={{ overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }} title={result} >
              {result}
            </div>;
          }
        },
      };
      // 排除ID/实例名
      if (i.FilterCorrespondName !== 'InstanceId' && i.FilterCorrespondName !== 'InstanceName') {
        // 设置宽度
        const data = _.cloneDeep(colData);
        if (data.key === 'AggregateQuery') {
          data.width = 210;
        } else {
          data.width = 120;
        }
        newColumns.push(_.cloneDeep(data));
      }
    });
    // 重点关注下标
    const index = newColumns.findIndex(i => i.key === 'Important');
    // 在重点关注前添加 ‘护航资源’ 列
    newColumns.splice(index, 0, guardColumn);
    setColumns([...newColumns]);
  }, [productColumn, modifiedInstance, instanceList]);

  useEffect(() => {
    if (nodeInfo.Product === 'cdn') {
      setModifiedInstance((oldValue) => {
        const newValue = _.cloneDeep(oldValue);
        // 默认的policy
        const initPolicy = _.cloneDeep(instancePolicy || []);
        newValue.map((i) => {
          (i.Policy || []).map((j) => {
            const policyItem = initPolicy.find(m => m.MetricName === j.MetricName) || {};
            // 突发护航需求,需要校验实例报备信息
            j.IsRequired = cdnInstancePolicy ? policyItem.IsRequired : false;
            j.FieldType = cdnInstancePolicy ? policyItem.FieldType : (policyItem.FieldType === 'float' ? 'int' : policyItem.FieldType);
          });
        });
        return newValue;
      });
    }
  }, [cdnInstancePolicy]);

  // 批量操作
  function batchOperate(type) {
    const list = _.cloneDeep(modifiedInstance);
    // 判断是否全部开启,如果全部开启，则全部关闭。反之则全部开启。
    selectInstanceId.map((InstanceId) => {
      const instance = list.find(item => item.InstanceId === InstanceId);
      instance[type] = allTrue(type) ? 0 : 1;
    });
    setModifiedInstance(_.cloneDeep(list));
    updateShowInstance(list);
  }

  // 判断加入护航/重点关注 全部开启
  function allTrue(type) {
    let allTrue = false;
    if (selectInstanceId.length) {
      allTrue = selectInstanceId.every((InstanceId) => {
        // 在所有操作的数组中找到实例
        const instance = modifiedInstance.find(item => item.InstanceId === InstanceId);
        return instance && instance[type] === 1;
      });
    }
    return allTrue;
  }

  function updateBatchList(id, v) {
    setBatchList((oldBatchList) => {
      const item = oldBatchList.find(j => j.DataFrom + j.FilterCorrespondName + j.Location === id);
      item.BatchValue = v;
      return _.cloneDeep(oldBatchList);
    });
  }

  // 批量操作更新到 modifiedInstance
  function batchSave() {
    const tempList = _.cloneDeep(modifiedInstance);
    batchList.map((i) => {
      const { DataFrom, FilterCorrespondName, Type, BatchValue = 0, Location } = i;
      selectInstanceId.map((InstanceId) => {
        // 在所有操作的数组中找到勾选的实例
        const instance = tempList.find(item => item.InstanceId === InstanceId);
        if (DataFrom === 'Info') {
          instance[FilterCorrespondName] = BatchValue;
          if (Type === 'Switch') {
            instance[FilterCorrespondName] = BatchValue ? 1 : 0;
          }
        } else if (DataFrom === 'Extra') {
          const extraData = instance.Extra ? JSON.parse(instance.Extra) : {};
          // 修改Extra里对应的值
          updateNestedField(extraData, Location, BatchValue);
          if (Type === 'Switch') {
            updateNestedField(extraData, Location, BatchValue ? 1 : 0);
          }
        } else if (DataFrom === 'Policy') {
          const policy = _.cloneDeep(instance.Policy);
          policy.map((j, index1) => {
            if (j.MetricName === Location) {
              if (j.Type === 'Other') {
                policy[index1].OtherValue = BatchValue;
                // j.Value = 0
              } else {
                policy[index1].Value = BatchValue;
                // 如果输入类型是string，需要将值转换为string
                if (i.inputType === 'string') {
                  policy[index1].Value = `${BatchValue}`;
                }
                // 如果是开关，value要从Boolean转位number
                if (Type === 'Switch') {
                  policy[index1].Value = Number(!BatchValue);
                }
              }
            }
          });
          instance.Policy = policy;
        }
      });
    });
    setModifiedInstance(_.cloneDeep(tempList));
    updateShowInstance(_.cloneDeep(tempList));
    // 关闭批量编辑的弹框
    setBatchEditorVisible(false);
  }

  // 当搜索条件变化时，重新搜索
  useEffect(() => {
    requested && getInstanceList(offset, limit);
  }, [filter]);

  useEffect(() => {
    setBatchList(_.cloneDeep(productColumn.filter(i => (reportType.includes(i.Type) && i.FilterCorrespondName !== 'Important'))));
  }, [productColumn]);

  return <>
    {/* 节点资源组件 */}
    <div className='searchWrap' >
      {requested && <SearchBox nodeInfo={nodeInfo} filterInfo={productFilter} filterChange={(newFilter) => {
        setFilter([...newFilter]);
      }} />}
      {canEdit && <>
        <Button type="weak" disabled={!selectInstanceId.length} onClick={() => {
          batchOperate('JoinGuard');
        }}>
          {allTrue('JoinGuard') ? t('剔出护航') : t('加入护航')}
        </Button>
        <Button type="weak" disabled={!selectInstanceId.length} onClick={() => {
          batchOperate('Important');
        }}>
          {allTrue('Important') ? t('剔出关注') : t('重点关注')}
        </Button>
        <Button type="weak" disabled={!selectInstanceId.length || batchList.length == 0} onClick={() => {
          setBatchEditorVisible(true);
        }}>
          {t('报备信息')}
        </Button>
      </>}
    </div>
    <Table
      className='instanceTableWrap'
      rowDisabled={() => (!(consoleBaseInfo || canEdit))}
      bordered={true}
      records={instanceList}
      recordKey="InstanceId"
      columns={columns}
      topTip={
        (loading || instanceList.length === 0) && (
          <StatusTip status={loading ? 'loading' : 'empty'} />
        )
      }
      addons={[
        pageable({
          recordCount: total,
          pageSize: limit,
          pageIndex: offset / limit + 1,
          onPagingChange: (query) => {
            if (loading) {
              return;
            }
            setOffset((query.pageIndex - 1) * query.pageSize);
            setLimit(query.pageSize);
            getInstanceList((query.pageIndex - 1) * query.pageSize, query.pageSize);
          },
        }),
        scrollable({ maxHeight: 330 }),
        selectable({
          value: selectInstanceId,
          onChange: (keys, ctx) => {
            selectChange(keys, ctx);
          },
          rowSelect: false,
        }),
      ]}
    />
    <Modal size="s" visible={batchEditorVisible} caption={t('批量编辑报备信息')} disableCloseIcon className='batchModal'>
      <Modal.Body>
        {batchList.map((i) => {
          // 目前只有policy有input
          const inputPolicy = instancePolicy.find(m => m.MetricName === i.Location) || {};
          // policy的唯一id
          const id = i.DataFrom + i.FilterCorrespondName + i.Location;
          // 选项
          const options = (i.ExtraShowName || []).map(item => ({ text: item.Value, value: item.Key }));
          return <div className='batchItem' key={JSON.stringify(i)}>
            <div style={{ marginRight: 20, width: 80 }}>{i.FilterName}</div>
            {i.Type === 'Switch' && <Switch
              onChange={(v) => {
                updateBatchList(id, v ? 1 : 0);
              }}
              value={i.BatchValue || 0}
            />}
            {i.Type === 'Input' && (<>
              {inputPolicy.FieldType === 'string'
                ? <Input
                  onChange={(v) => {
                    updateBatchList(id, v);
                  }}
                  value={i.BatchValue || ''}
                />
                : <>
                  <InputNumber
                    size="m"
                    onChange={(v) => {
                      updateBatchList(id, v);
                    }}
                    min={0}
                    step={inputPolicy.FieldType === 'int' ? 1 : 0.1}
                    value={i.BatchValue || 0}
                  />
                  {/* <Text>{inputPolicy.Unit || ''}</Text> */}
                </>
              }
            </>)}
            {i.Type === 'SelectSingle' && <Select
              style={{ width: 240 }}
              appearance="button"
              options={options}
              value={i.BatchValue || ''}
              onChange={(v) => {
                updateBatchList(id, v);
              }}
            />
            }
            {i.Type === 'SelectMultiple' && <SelectMultiple
              style={{ width: 240 }}
              appearance="button"
              options={options}
              value={i.BatchValue ? i.BatchValue.split(';') : []}
              onChange={(v) => {
                updateBatchList(id, v.join(';'));
              }}
            />
            }
          </div>;
        })}

      </Modal.Body>
      <Modal.Footer>
        <Button type="primary" onClick={() => {
          batchSave();
        }}>{t('确定')}</Button>
        <Button type="weak" onClick={() => {
          setBatchEditorVisible(false);
        }}>
          {t('取消')}
        </Button>
      </Modal.Footer>
    </Modal>
  </>;
};
export default InstanceConfig;
