/* eslint-disable no-nested-ternary */
import React, { useEffect, useMemo, useState, useRef } from 'react';
import { t } from '@tea/app/i18n';
import { Skeleton, Empty } from 'tdesign-react';
import { StatusTip,
  Status,
  Modal,
  Drawer,
  Button,
} from '@tencent/tea-component';
import store from '@src/origin-store/store';
import StatusReport from '@src/components/status-report';
import { EMonitorResultType, EENDTYPE, ENodeTaskStatus } from '@src/constants';
import { guardInfo } from '@src/utils/caching';
import usePluginGuardPanelStatus  from '@src/hooks/usePluginGuardPanelStatus';
import { setDrawerVisibleMapAction } from '@src/origin-store/guardAction';
import { getShapeStyleLable } from '@src/utils/guard-node';
import { find, isEmpty } from 'lodash';
import IframeBox from '@src/components/iframe-box';
import moment from 'moment';
import { useCommonSelector } from '@src/store/app-common';
import './index.less';

interface monitorType {
  MapId: string;
  NodeUuid: string;
  DashBoardUrl?: string;
  IsSupport: boolean;
  Status: number;
}

interface MonitorStateResult {
  NodeMonitorPanelList?: monitorType[]; // 根据实际情况定义数组元素的类型
}

const defaultMonitorResult = {
  Status: EMonitorResultType.PENDDING,
  DashBoardUrl: '',
  IsSupport: true,
};

/** Root
 * @param props
 * @returns React.ReactElement
 */
export default function GuardNodeDrawer(): React.ReactElement {
  const { taskResult } = useCommonSelector();
  const { currNode, graphApi } = store.getState().guard;
  const [modalVisible, setModalVisible] = useState(false);
  const firstLoadData = useRef(true);
  const {
    uin, env, archInfo,
  } = graphApi;

  const type = {
    CONSOLE: EENDTYPE.CUSTOMER,
    ISA: EENDTYPE.OPERATOR,
  }[env];

  const allShapes = graphApi.getArchNodes();
  const start = moment(guardInfo.StartTime, 'YYYY-MM-DD HH:mm:ss');
  const end = moment(guardInfo.EndTime, 'YYYY-MM-DD HH:mm:ss');
  const [monitorResult, setMonitorResult] = useState<any>(defaultMonitorResult);
  const isGuarding = guardInfo.Status > 1 && moment().isBetween(start, end);

  const isInstanceLoading = useMemo(() => {
    const loadStatus = find(taskResult?.NodeDetail, item => item?.NodeUuId === currNode?.key) || {};
    if (!isEmpty(loadStatus)) {
      if (loadStatus.Status === ENodeTaskStatus.INIT || loadStatus.Status === ENodeTaskStatus.RUNNING) {
        return true;
      }
      return false;
    }
    return false;
  }, [currNode.key, taskResult?.NodeDetail]);

  const currShapeName = useMemo(() => {
    if (currNode) {
      const currLabelKey = currNode?.sticky;
      if (currLabelKey) {
        // 产品图元有sticky关联label节点
        const currLabel = allShapes[currLabelKey];
        return currLabel?.styles?.label?.value || getShapeStyleLable(currLabel);
      }
      // 组类型图元没有sticky属性
      return currNode?.styles?.label?.value || currNode?.styles?.label?.default;
    }
    return '';
  }, [currNode]);

  useEffect(() => {
    setMonitorResult(defaultMonitorResult);
  }, [currNode.key]);

  const {
    result: monitorStateResult,
    // isLoading: monitorStateLoading = true,
    // error: monitorStateError,
    // reload: reloadMonitorState,
  } = usePluginGuardPanelStatus(
    {
      MapId: archInfo?.archId,
      NodeList: [{ NodeUuid: currNode?.key, ProductId: currNode?.name }],
      ...(type === EENDTYPE.OPERATOR && { Uin: uin }),
    },
    (monitorResult?.Status === EMonitorResultType.SUCCESS
      || monitorResult?.Status === EMonitorResultType.FAILURE
      || !monitorResult?.IsSupport
    ),
  );

  useEffect(() => {
    if (monitorStateResult) {
      const typedMonitorStateResult = monitorStateResult as MonitorStateResult;
      setMonitorResult(typedMonitorStateResult?.NodeMonitorPanelList[0]);
    }
  }, [monitorStateResult]);

  useEffect(() => {
    const nodeStatus = find(taskResult?.NodeDetail, item => item?.NodeUuId === currNode?.key) || {};
    if (nodeStatus?.Status === ENodeTaskStatus.SUCCESS
      && firstLoadData.current
      && monitorResult?.Status === EMonitorResultType.SUCCESS
    ) {
      setMonitorResult({ ...monitorResult, Status: EMonitorResultType.PENDDING });
      firstLoadData.current = false;
    }
  }, [currNode.key, taskResult?.NodeDetail]);

  return (
    <>
      <Drawer
        style={{ width: 700 }}
        visible
        title={ t('节点监控 - {{attr0}}', { attr0: currShapeName ?? '-' }) }
        destroyOnClose
        subtitle={isInstanceLoading && monitorResult.IsSupport && <StatusTip status="loading" loadingText={t('数据更新中...')}/>}
        outerClickClosable={false}
        className='monitor-drawer'
        footer={<div className='monitor-drawer-footer'>
          <Button
            type="primary"
            onClick={() => setModalVisible(true)}
            disabled={monitorResult?.Status !== EMonitorResultType.SUCCESS}
          >
            {t('全屏查看')}
          </Button>
          <Button
            type="weak"
            style={{ marginLeft: 15 }}
            onClick={() => store.dispatch(setDrawerVisibleMapAction({ nodeDrawerVisible: false }))}
            >
            {t('关闭')}
          </Button>
        </div>}
        onClose={() => {
          store.dispatch(setDrawerVisibleMapAction({ nodeDrawerVisible: false }));
        }}
      >
        <div>
        {isGuarding && <StatusReport />}
        </div>
        <div className='monitor-content'>
          {
            monitorResult.IsSupport
              ? <>
                {
                monitorResult?.Status === EMonitorResultType.PENDDING
                  ? <Skeleton theme="article"></Skeleton>
                  : monitorResult?.Status === EMonitorResultType.FAILURE
                    ? <Status
                      size="l"
                      title={t('监控面板生成失败')}
                    />
                    : monitorResult?.Status === EMonitorResultType.SUCCESS
                      && <IframeBox url={monitorResult?.DashBoardUrl}/>
              }
              </>
              : <Empty title={t('该产品目前暂未接入护航节点监控')} style={{ marginTop: 200 }}/>
          }
        </div>
      </Drawer>
      <Modal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        className='full-screen-modal-box'
        size={'99.1%'}
      >
        <IframeBox url={monitorResult?.DashBoardUrl}/>
      </Modal>
    </>
  );
}
