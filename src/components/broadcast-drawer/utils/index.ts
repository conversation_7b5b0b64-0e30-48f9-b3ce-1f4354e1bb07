import { t } from '@tea/app/i18n';
const ENV_INFO = {
  'isa.woa.com': { name: t('正式环境'), value: 'production' },
  'isa-pre.woa.com': { name: t('预发布环境'), value: 'pre' },
  'isa-test.woa.com': { name: t('测试环境'), value: 'test' },
  'isa-intl.woa.com': { name: t('海外正式环境'), value: 'production-abroad' },
};
export const getProcessEnv = () => {
  const { hostname } = location;
  return ENV_INFO[hostname]?.value || 'others';
};
