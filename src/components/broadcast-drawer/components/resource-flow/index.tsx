import React, { useState, useEffect, FC, useMemo } from 'react';
import { map, filter, find } from 'lodash';
import { DescribeGuardSheet } from '@src/service/api/broadcast-drawer';
import ExtensibleInput  from '../extensible-input';
import originStore from '@src/origin-store/store';
import { EENDTYPE } from '@src/constants';
import { message, Button, Table, Select } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';

interface IResourceSupplePanelProps {
  guardId: number
  editable: boolean
  resourceDetail: any
  onSourceListChange: Function
}

const ResourceFlow: FC<IResourceSupplePanelProps> = ({
  guardId,
  editable,
  resourceDetail,
  onSourceListChange,
}: IResourceSupplePanelProps) => {
  const queryParams = new URLSearchParams(location.search);
  const appId = +queryParams.get('appid');
  const {
    graphApi,
  }  = originStore.getState().guard;
  const type = {
    CONSOLE: EENDTYPE.CUSTOMER,
    ISA: EENDTYPE.OPERATOR,
  }[graphApi.env];
  const { Product, ResourceIds } = resourceDetail;
  const [instanceList, setInstanceList] = useState([]);

  const [appIdOption, setAppIdOption] = useState([]);

  useEffect(() => {
    const instances = map(ResourceIds, ({ InstanceId }, index) => {
      const resourceInfo = InstanceId.split('/');
      if (resourceInfo[0]) {
        return {
          Id: index,
          AppId: resourceInfo[0],
          InstanceIds: resourceInfo[1]?.split(';'),
        };
      }

      return {};
    });
    setInstanceList(instances);
  }, [ResourceIds]);

  useEffect(() => {
    const targetResource = map(instanceList, ({ AppId, InstanceIds }) => ({
      AppId: +AppId,
      Product,
      InstanceId: InstanceIds.join(';'),
    }));
    onSourceListChange(targetResource, Product);
  }, [instanceList]);

  useEffect(() => {
    getGuardSheet();
  }, []);

  const filterSelectOption = useMemo(() => map(appIdOption, (i) => {
    const temp = find(instanceList, item => item.AppId === i.value);
    return temp ? { ...i, disabled: true } : i;
  }), [appIdOption, instanceList]);

  // 获取护航信息
  const getGuardSheet = () => {
    const filters = [{ Name: 'guard_id', Values: [`${guardId}`] }];
    DescribeGuardSheet({
      Filters: filters.filter((i) => {
        if (i.Values.length) {
          return i;
        }
      }),
      Offset: 0,
      Limit: 1,
      ...(type === EENDTYPE.OPERATOR && { AppId: appId }),
    })
      .then((res: any) => {
        if (res.Error) {
          const msg = res.Error.Message;
          message.error({ content: msg });
          return;
        }
        const item = res.Guard[0];
        if (item) {
          const appIdList = item.InstanceTemplateCount ? Object.keys(item.InstanceTemplateCount) : [];
          setAppIdOption(map(appIdList, item => ({ text: item, value: item })));
        } else {
          setAppIdOption([]);
        }
      })
      .catch((err) => {
        const msg = err.msg || err.toString() || t('未知错误');
        message.error({ content: msg });
      });
  };

  const columns = [
    {
      key: 'AppId',
      header: 'APPID',
      render: (item) => {
        const handleSelectChange = (value) => {
          const cur = map(instanceList, (i) => {
            if (i.Id === item.Id) {
              i.AppId = value;
            }
            return i;
          });
          setInstanceList(cur);
        };
        return <>
                <Select
                    value={item.AppId}
                    matchButtonWidth
                    options={filterSelectOption}
                    appearance="button"
                    size="m"
                    onChange={value => handleSelectChange(value)}
                />
              </>;
      },
    },
    {
      key: 'Id',
      header: t('流ID'),
      render: (item) => {
        const handleInputChange = ({ type, index = 0, value = '' }) => {
          const cur = map(instanceList, (i) => {
            if (i.Id === item.Id) {
              if (type === 'ADD') {
                i.InstanceIds.push('');
              } else if (type === 'DELETE') {
                i.InstanceIds.splice(index, 1);
              } else if (type === 'CHANG') {
                i.InstanceIds[index] = value;
              }
            }
            return i;
          });
          setInstanceList(cur);
        };
        return <>
                <ExtensibleInput
                    initialList={item.InstanceIds}
                    onInputAdd={() => handleInputChange({ type: 'ADD' })}
                    onInputDelete={index => handleInputChange({ type: 'DELETE', index })}
                    onInputChange={(value, index) => handleInputChange({ type: 'CHANG', index, value })}
                />
            </>;
      },
    },
    {
      key: '',
      header: t('操作'),
      width: '10%',
      render: item => <Button
                type="link"
                onClick={() => setInstanceList(filter(instanceList, i => i.Id !== item.Id))}
            >{t('删除')}</Button>,
    },
  ];

  const tableOptionProps = {
    recordKey: 'Id',
    verticalTopL: true,
    compact: true,
    bordered: true,
    columns,
    records: instanceList,
  };

  const handleTableRowAdd = () => {
    setInstanceList(instanceList.concat({ Id: instanceList.length, AppId: 0, InstanceIds: [''] }));
  };

  return (
    <div>
      <p style={{ fontSize: '14px', fontWeight: 'bold', margin: 10, marginLeft: 0 }}>
        {t('请补充信息')}
      </p>
      <div style={{ position: 'relative', width: '80%' }}>
        {!editable && <div className='content-mask'></div>}
        {/* @ts-ignore */}
        <Table {...tableOptionProps} />
        {
            instanceList?.length < appIdOption?.length
            && <Button type="link" htmlType="button" onClick={handleTableRowAdd}>{t('添加')}</Button>
        }
      </div>
    </div>
  );
};
export default ResourceFlow;
