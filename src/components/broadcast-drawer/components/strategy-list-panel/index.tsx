/* eslint-disable react/display-name */
import React, { useState, useEffect, useMemo, useImperativeHandle } from 'react';
import './index.less';
import { useToggle } from '@src/hooks/common';
import {
  DescribeBroadcastStrategys,
  ModifyBroadcastUserStrategy,
  DescribeCombinedBroadcastConfigs,
  ModifyCombinedBroadcastConfigs,
  getDescribeProductConfigList,
  getDescribeProductConfigListForGuard,
} from '@src/service/api/broadcast-drawer';
import { useCommonSelector } from '@src/store/app-common';
import { find, isEmpty, forEach, map, clone, toPairs, isEqual, sortBy, filter, uniqBy } from 'lodash';
import originStore from '@src/origin-store/store';
import { EENDTYPE } from '@src/constants';
import { typeOption, subTypeOption, STATUS, SourceType } from '../../constants';
import { AttributeValue } from '@tencent/tea-component/src/tagsearchbox/AttributeSelect';
import {
  Button,
  InputAdornment,
  Input,
  message,
  Transfer,
  Table,
  Select,
  TagSearchBox,
  Status,
  InputNumber,
  Tag,
  Icon,
  Bubble,
  Collapse,
  Text,
  Switch,
  Row,
  Col,
  Justify,
  Card,
  Layout,
  Modal,
} from '@tencent/tea-component';
import { t, Trans, Slot } from '@tea/app/i18n';
import uuid from 'react-uuid';
const { TextArea } = Input;
const { Content } = Layout;
const { expandable, rowtooltip } = Table.addons;

interface IStrategyListPanelProps {
  products: Array<any>
  broadcastId: number
  editable: boolean
  operator: string
  selectedStrategy: Array<any>
  onInfoChange: Function
  reload: Function,
  reloadC?: Function,
  // 外部群号码
  GtsChatid?: string,
  // 是否启用常规播报
  Enable?: boolean
}
const { selectable, scrollable, autotip } = Table.addons;

// const compareSelectOption = ['>', '=', '<'];

const baseColumns: any = [
  {
    key: 'ProductName',
    header: t('云产品'),
  },
  {
    key: 'CNName',
    header: t('播报项'),
  },
  {
    key: 'SubType',
    header: t('类型'),
    render: (item) => {
      const subTypeTxt: any = find(subTypeOption, subType => subType.value === item.SubType) || {};
      return (<>{isEmpty(subTypeTxt) ? '-' : subTypeTxt.text}</>);
    },
  },
  {
    key: 'Type',
    header: t('维度'),
    render: (item) => {
      const typeTxt: any = find(typeOption, type => type.value === item.Type) || {};
      return (<>{isEmpty(typeTxt) ? '-' : typeTxt.text}</>);
    },
  },
];

function SourceTable({ dataSource, targetKeys, onChange, loading, sourceColumns, isPeriodLimit = false, type = '' }: any) {
  return (
		<Table
			records={dataSource}
			recordKey="StrategyId"
			columns={sourceColumns}
			rowDisabled={(data: any) => {
			  if (type === 'combined') {
			    return data.disabled === true;
			  }
			  if (isPeriodLimit && data?.StrategySource === SourceType.MONITOR_BROADCAST_CONFIG) return true;
			  return false;
			}}
			addons={[
			  scrollable({
			    maxHeight: 310,
			    minHeight: 310,
			    minWidth: 700,
			  }),
			  selectable({
			    value: targetKeys,
			    onChange,
			    rowSelect: true,
			  }),
			  autotip({
			    isLoading: loading,
			  }),
			  rowtooltip({
			    tooltip: record => (
			      isPeriodLimit && record?.StrategySource === SourceType.MONITOR_BROADCAST_CONFIG
			        ? t('该类型策略仅支持10分钟以上的播报周期')
			        : null
			    ),
			  }),
			]}
		/>
  );
}

function TargetTable({ dataSource, loading, targetColumns }: any) {
  return (
		<Table
			records={dataSource}
			recordKey="StrategyId"
			columns={targetColumns}
			addons={[
			  autotip({
			    isLoading: loading,
			  }),
			  scrollable({
			    maxHeight: 340,
			    minHeight: 340,
			    minWidth: 700,
			  }),
			]}
		/>
  );
}

const typeOptionNoAll = filter(typeOption, item => (item.value !== 'all'));

const StrategyListPanel = React.forwardRef((
  {
    products,
    broadcastId,
    selectedStrategy,
    operator,
    onInfoChange,
    reload,
    reloadC,
    Enable,
  }: IStrategyListPanelProps,
  ref
) => {
  const queryParams = new URLSearchParams(location.search);
  const appId = +queryParams.get('appid');
  const { isBroadcastPeriodLimit }  = useCommonSelector();

  const {
    graphApi,
  }  = originStore.getState().guard;
  const type = {
    CONSOLE: EENDTYPE.CUSTOMER,
    ISA: EENDTYPE.OPERATOR,
  }[graphApi.env];
  // 当前点击的层级
  const [currentRow, setCurrentRow] = useState<any>(null);
  const filterSelectedStrategy = isBroadcastPeriodLimit
    ? selectedStrategy?.filter(item => item.StrategySource === SourceType.BROADCAST_STRATEGY)
    : selectedStrategy;
  const formatSelectedStrategy = filterSelectedStrategy?.map(item => ({
    ...item,
    StrategyId: `${item.StrategyId}-${item.StrategySource}`,
  }));
  // 播报详情列
  const combinedDetailColumns = [
    {
      key: 'title',
      header: t('播报标题'),
      width: '30%',
      render: item => <Input
				size='full'
				placeholder={t('请输入播报标题')}
				value={item.Title}
				onChange={(value) => {
				  setCombinedDetails(valueChange(item.Id, value, 'Title'));
				}} />,
    },
    {
      key: 'type',
      header: t('播报维度'),
      width: '25%',
      render: item => (item.Children ? '' : <Select
				value={item.Type}
				onChange={(value) => {
				  setCombinedDetails(valueChange(item.Id, [], 'BroadcastConfig', valueChange(item.Id, value, 'Type')));
				}}
				placeholder={t('请选择播报维度')}
				options={typeOption.filter(item => item.value !== 'all')}
				appearance="button"
				size='full'
			/>),
    },
    {
      key: 'strategys',
      header: t('播报策略'),
      width: '30%',
      render: item => (item.Children ? '' : <div>
				{item.BroadcastConfig?.length ? <Icon type="success" /> : <Icon type="warning" />}
				<span style={{ margin: '0px 10px 0 4px' }}><Trans>已选择 <Text theme="primary"><Slot content={item.BroadcastConfig?.length || 0} /></Text> 条</Trans></span>
				<Button type="link" onClick={() => {
				  setCurrentRow(item);
				  setCombinedModal(true);
				}}>{t('选择')}</Button>
			</div>),
    },
    {
      key: 'operator',
      header: t('操作'),
      width: '15%',
      render: item => (item.Children
        ? <>
					<Button icon="plus" title={t('新增')} onClick={() => {
					  const newData = clone(item.Children);
					  // 新增一条子数据
					  const addData = { Id: uuid(), Title: '', Type: 'resource', BroadcastConfig: [] };
					  newData.push(addData);
					  setCombinedDetails(valueChange(item.Id, newData, 'Children'));
					}} />
					<Button icon="minus" title={t('删除')} onClick={async () => {
					  const yes = await Modal.confirm({
					    message: t('确认删除当前播报标题以及子标题和播报策略？'),
					    okText: t('确认'),
					    cancelText: t('取消'),
					  });
					  if (yes) {
					    const newData = clone(combinedDetails);
					    const index = newData.findIndex(i => i.Id === item.Id);
					    newData.splice(index, 1);
					    setCombinedDetails(newData);
					  }
					}} />
				</>
        : <Button icon="minus" title={t('删除')} onClick={async () => {
				  const yes = await Modal.confirm({
				    message: t('确认删除当前子标题和播报策略？'),
				    okText: t('确认'),
				    cancelText: t('取消'),
				  });
				  if (yes) {
				    setCombinedDetails(deleteDataById(item.Id));
				  }
        }} />),
    },
  ];

  // 常规播报开关
  const [commonEnable, setCommonEnable] = useState((Enable === undefined || Enable === null) ? true : Enable);
  // 组合播报开关
  const [combinedEnable, setCombinedEnable] = useState(false);
  // 播报头部
  const [combinedHeader, setCombinedHeader] = useState('');
  // 播报详情
  const [combinedDetails, setCombinedDetails] = useState([]);
  // 播报尾部
  const [combinedFooter, setCombinedFooter] = useState('');
  // 当前展开的产品
  const [expandedKeys, setExpandedKeys] = useState([]);
  // 全部播报策略
  const [initBroadcastStrategy, setInitBroadcastStrategy] = useState([]);
  // 组合播报策略选择弹框
  const [combinedModal, setCombinedModal] = useState(false);
  const initTargetKeys = map(formatSelectedStrategy, ({ StrategyId }) => `${StrategyId}`);

  const [isLoading, startLoad, endLoad] = useToggle(false);
  const [isLoadingC, startLoadC, endLoadC] = useToggle(false);

  const [selectedStrategyObj, setSelectedStrategyObj] = useState({});

  // 常规播报左侧要展示的策略Id，用来在 initBroadcastStrategy 中过滤
  const [showIdsCommon, setShowIdsCommon] = useState([]);

  // 组合播报左侧要展示的策略Id，用来在 initBroadcastStrategy 中过滤
  const [showIdsCombined, setShowIdsCombined] = useState([]);
  const [targetKeys, setTargetKeys] = useState(initTargetKeys);
  // 组合播报，已选择的策略
  const [targetKeysC, setTargetKeysC] = useState([]);

  // 搜索筛选项
  const [tagSelectBoxValue, setTagSelectBoxValue] = useState([]);
  const [tagSelectValue, setTagSelectValue] = useState({ ProductNameList: [], TypeList: [] });
  // 组合播报搜索
  const [combinedSelectBoxValue, setCombinedSelectBoxValue] = useState([]);
  const [combinedSelectValue, setCombinedSelectValue] = useState({ ProductNameList: [] });

  const [productOption, setProductOption] = useState({});

  // 组合播报属性修改
  function valueChange(id, newValue, name, data = clone(combinedDetails)) {
    return data.map((item) => {
      if (item.Id === id) {
        return {
          ...item,
          [name]: newValue,
        };
      }
      if (item.Children) {
        return {
          ...item,
          Children: valueChange(id, newValue, name, item.Children),
        };
      }
      return item;
    });
  }

  // 组合播报删除子内容
  const deleteDataById = (id, data = clone(combinedDetails)) => data.filter((item) => {
    if (item.Id === id) {
      return false; // 不包含指定 ID 的对象
    }
    if (item.Children) {
      item.Children = deleteDataById(id, item.Children); // 递归处理子级数据
    }
    return true; // 保留其他对象
  });

  // 常规播报搜索值变化，重新查询
  useEffect(() => {
    getBroadStrategyList();
  }, [tagSelectValue]);

  // 初始化组合播报数据
  useEffect(() => {
    if (broadcastId) {
      getCombinedData();
    }
  }, [broadcastId]);

  // 监听播报详情数据，立马展开全部数据
  useEffect(() => {
    setExpandedKeys(combinedDetails.map(i => i.Id));
  }, [combinedDetails]);

  // 下拉框获取当前可供选择的云产品，包括兜底产品
  const productsSelectValue = useMemo(() => {
    const temp = map(products, item => ({ key: item.Product, name: productOption[item.Product] }));
    return uniqBy(temp, 'key');
  }, [products, productOption]);

  useEffect(() => {
    onInfoChange(isEqual(sortBy(targetKeys, item => +item), sortBy(initTargetKeys, item => +item)));
  }, [targetKeys, initTargetKeys]);

  // 获取组合播报内容
  const getCombinedData = async () => {
    try {
      const res: any = await DescribeCombinedBroadcastConfigs({
        BroadcastId: broadcastId,
        ...(type === EENDTYPE.OPERATOR && { AppId: appId }),
      });
      if (res.Error) {
        message.error({ content: res.Error.Message });
      } else {
        // 设置组合播报内容
        const { Enable, Header, Footer, Details } = res || {};
        setCombinedEnable(Enable);
        setCombinedHeader(Header);
        setCombinedFooter(Footer);
        const formatDetails = Details?.map((item) => {
          const { Children } = item || {};
          return {
            ...item,
            Children: Children?.map((i) => {
              const { BroadcastConfig } = i || {};
              return {
                ...i,
                BroadcastConfig: BroadcastConfig?.map(j => ({
                  ...j,
                  StrategyId: `${j.StrategyId}-${j.StrategySource}`,
                })),
              };
            }),
          };
        });
        setCombinedDetails(formatDetails || []);
      }
      endLoad();
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
    }
  };

  // 常规播报策略列表搜索
  const getBroadStrategyList = async () => {
    startLoad();
    const { ProductNameList, TypeList } = tagSelectValue;
    const filters = [
      { Name: 'product', Values: ProductNameList.length ? ProductNameList : products.map(i => i.Product) },
      { Name: 'type', Values: TypeList },
      { Name: 'online', Values: [`${STATUS.SUCCESS}`] },
    ];
    const params = {
      Filters: filters,
      ...(type === EENDTYPE.OPERATOR && { AppId: appId }),
    };
    DescribeBroadcastStrategys(params)
      .then((res: any) => {
        if (res.Error) {
          message.error({ content: res.Error.Message });
        } else {
          const formatBroadcastStrategy = res?.BroadcastStrategy?.map(item => ({
            ...item,
            StrategyId: `${item.StrategyId}-${item.StrategySource}`,
          }));
          if (!initBroadcastStrategy.length) {
          // 保存一份完整的播报策略
            const broadcastStrategies = map(formatBroadcastStrategy, (item) => {
              const current = find(formatSelectedStrategy, i => i.StrategyId === item.StrategyId);
              if (!isEmpty(current)) {
                return { ...item, Threshold: current.Threshold };
              }
              return item;
            });
            setInitBroadcastStrategy(broadcastStrategies);
          }
          setShowIdsCommon(formatBroadcastStrategy.map(i => i.StrategyId));
        }
        endLoad();
      })
      .catch((err) => {
        const msg = err.msg || err.toString() || t('未知错误');
        message.error({ content: msg });
        endLoad();
      });
  };

  // 组合播报查询
  const getCombinedBroadStrategyList = () => {
    startLoadC();
    const { ProductNameList } = combinedSelectValue;
    const filters = [
      { Name: 'product', Values: ProductNameList.length ? ProductNameList : products.map(i => i.Product) },
      { Name: 'type', Values: [currentRow.Type] },
      { Name: 'online', Values: [`${STATUS.SUCCESS}`] },
    ];
    const params = {
      Filters: filters,
      ...(type === EENDTYPE.OPERATOR && { AppId: appId }),
    };
    DescribeBroadcastStrategys(params)
      .then((res: any) => {
        if (res.Error) {
          message.error({ content: res.Error.Message });
        } else {
          const formatBroadcastStrategy = res?.BroadcastStrategy?.map(item => ({
            ...item,
            StrategyId: `${item.StrategyId}-${item.StrategySource}`,
          }));
          setShowIdsCombined(formatBroadcastStrategy.map(i => i.StrategyId));
        }
        endLoadC();
      })
      .catch((err) => {
        const msg = err.msg || err.toString() || t('未知错误');
        message.error({ content: msg });
        endLoadC();
      });
  };

  const handleSearchTagChange = (tags) => {
    let ProductNameList = [];
    let TypeList = [];
    forEach(tags, (tag) => {
      const name = tag.attr ? tag.attr.key : '';
      const valueList = map(tag.values, item => (tag.attr ? item.key : item.name));
      switch (name) {
        case 'productName':
          ProductNameList = valueList;
          break;
        case 'type':
          TypeList = valueList;
          break;
        default:
          ProductNameList = ProductNameList.concat(valueList);
          break;
      }
    });
    setTagSelectValue({ ProductNameList, TypeList });
    setTagSelectBoxValue(tags);
  };

  // 组合播报搜索条件变化
  const combinedSearchTagChange = (tags) => {
    let ProductNameList = [];
    forEach(tags, (tag) => {
      const valueList = map(tag.values, item => (tag.attr ? item.key : item.name));
      ProductNameList = valueList;
    });
    setCombinedSelectValue({ ProductNameList });
    setCombinedSelectBoxValue(tags);
  };

  const sourceColumns = baseColumns.concat([
    {
      key: 'Desc',
      header: t('播报项说明'),
    },
    {
      key: 'Out',
      header: <><Trans>是否支持外部群播报<Bubble content={t('注意：如果选择播报策略支持外部群（群员包括客户），则播报消息会同步发送到外部群。')}>
				<Icon type="info" style={{ cursor: 'pointer' }} />
			</Bubble>
			</Trans></>,
      width: 150,
      render: item => (item.Out ? <Tag theme="warning">{t('是')}</Tag> : <Tag theme="primary">{t('否')}</Tag>),
      fixed: 'right',
    },
  ]);

  const targetColumns = baseColumns.concat([
    {
      key: 'threshold',
      header: t('阈值'),
      // @ts-ignore
      width: '30%',
      render: (item) => {
        if (isEmpty(item.Threshold)) return <span style={{ color: 'rgba(0, 0, 0, 0.4)' }}>{t('无')}</span>;
        const { Values, Factor, Unit = '' } = item.Threshold[0] || {};
        // const compareSelect = (
        // 	<Select
        // 		placeholder=''
        // 		options={compareSelectOption.map(value => ({ value }))}
        // 		defaultValue={Factor}
        // 		onChange={value => handleInputChange(value, item.StrategyId, 'Factor')}
        // 	/>
        // );
        return <InputAdornment before={Factor} after={Unit} style={{ display: 'flex' }}>
					<InputNumber
						style={{ width: 60 }}
						defaultValue={Values}
						hideButton={true}
						min={0}
						max={Unit === '%' ? 100 : null}
						onChange={value => handleInputChange(value, item.StrategyId, 'Value')}
					/>
				</InputAdornment>;
      },
    },
    {
      key: 'Out',
      header: t('是否支持外部群播报'),
      width: 130,
      render: item => (item.Out ? <Tag theme="warning">{t('是')}</Tag> : <Tag theme="primary">{t('否')}</Tag>),
      fixed: 'right',
    },
    {
      key: 'delete',
      header: '',
      width: 50,
      render: item => <Icon type="dismiss" size="s" style={{ cursor: 'pointer' }}
				onClick={() => {
				  setTargetKeys(targetKeys.filter(i => i != item.StrategyId));
				}} />,
      fixed: 'right',
    },
  ]);

  const targetColumnsC = baseColumns.concat([
    {
      key: 'threshold',
      header: t('阈值'),
      width: '30%',
      render: (item) => {
        if (isEmpty(item.Threshold)) return <></>;
        const { Values, Factor, Unit = '' } = item.Threshold[0] || {};
        return <InputAdornment before={Factor} after={Unit} style={{ display: 'flex' }}>
					<InputNumber
						style={{ width: 60 }}
						defaultValue={Values}
						hideButton={true}
						min={0}
						max={Unit === '%' ? 100 : null}
						onChange={value => handleInputChangeC(value, item.StrategyId, 'Value')}
					/>
				</InputAdornment>;
      },
    },
    {
      key: 'delete',
      header: '',
      width: 50,
      render: item => <Icon type="dismiss" size="s" style={{ cursor: 'pointer' }}
				onClick={() => {
				  setTargetKeysC(targetKeysC.filter(i => i != item.StrategyId));
				}} />,
      fixed: 'right',
    },
  ]);

  // const subTypeList = useMemo(() => {
  // 	return map(subTypeOption, ({ text, value }) => ({ key: value, name: text })).filter(({ key }) => key !== 'all')
  // }, [subTypeOption]);

  // const typeList = useMemo(() => {
  // 	return map(typeOption, ({ text, value }) => ({ key: value, name: text })).filter(({ key }) => key !== 'all')
  // }, [typeOption]);

  // 标签搜索框
  const attributes: Array<AttributeValue> = [
    {
      type: ['multiple', { searchable: true }],
      key: 'productName',
      name: t('产品名'),
      values: productsSelectValue,
    },
    {
      type: 'multiple',
      key: 'type',
      name: t('维度'),
      values: map(typeOptionNoAll, (item) => {
        if (item.value === 'all') {
          return;
        }
        return { key: item.value, name: item.text };
      }),
    },
  ];
  const tagSearchBoxProps = {
    minWidth: '100%',
    attributes,
    value: tagSelectBoxValue,
    hideHelp: true,
    tips: t('多个过滤标签用回车键分隔'),
    onChange: handleSearchTagChange,
    onSearchButtonClick: () => getBroadStrategyList(),
  };
  // 组合播报搜索框属性
  const combinedTagSearchBoxProps = {
    minWidth: '100%',
    attributes: [attributes[0]],
    value: combinedSelectBoxValue,
    hideHelp: true,
    tips: t('多个过滤标签用回车键分隔'),
    onChange: combinedSearchTagChange,
    onSearchButtonClick: () => getCombinedBroadStrategyList(),
  };

  // 运营端获取产品名称
  const getProductsGroupsInfo = () => {
    getDescribeProductConfigList({
      AppId: appId,
      TaskType: 'guardTaskType',
      Env: 'all',
    })
      .then((res: any) => {
        if (res.Error) {
          const msg = res.Error.Message;
          message.error({ content: msg });
          return;
        }
        setProductOption(res.ProductDict);
      })
      .catch((err) => {
        const msg = err.msg || err.toString() || t('未知错误');
        message.error({ content: msg });
      });
  };
  // 租户端获取产品名称
  const getBroadStrategyListForGuard = () => {
    getDescribeProductConfigListForGuard({
      TaskType: 'guardTaskType',
      Env: 'all',
    })
      .then((res: any) => {
        if (res.Error) {
          const msg = res.Error.Message;
          message.error({ content: msg });
          return;
        }
        const products = JSON.parse(res.ProductDict);
        setProductOption(products);
      })
      .catch((err) => {
        const msg = err.msg || err.toString() || t('未知错误');
        message.error({ content: msg });
      });
  };

  useEffect(() => {
    const temp = {};
    map(formatSelectedStrategy, ({ StrategyId, Threshold }) => {
      temp[StrategyId] = Threshold;
    });
    setSelectedStrategyObj(temp);
    if (type === EENDTYPE.OPERATOR) {
      getProductsGroupsInfo();
    } else {
      getBroadStrategyListForGuard();
    }
  }, []);

  useEffect(() => {
    const broadcastList = initBroadcastStrategy
      .filter(i => targetKeys.includes(`${i.StrategyId}`))
      .map(({ StrategyId, Threshold }) => ({ StrategyId, Threshold }));

    const temp = clone(selectedStrategyObj);
    map(broadcastList, ({ StrategyId, Threshold }) => {
      if (!temp[StrategyId]) {
        temp[StrategyId] = Threshold;
      }
    });
    map(toPairs(temp), ([key]) => {
      if (!targetKeys.includes(`${key}`)) {
        delete temp[key];
      }
    });
    setSelectedStrategyObj(temp);
  }, [targetKeys, initBroadcastStrategy]);

  // 监听组合播报策略勾选，更新数据
  useEffect(() => {
    if (currentRow) {
      const broadcastList = initBroadcastStrategy
        .filter(i => targetKeysC.includes(`${i.StrategyId}`))
        .map(({ StrategyId, Threshold }) => ({ StrategyId, Threshold }));
      setCombinedDetails(valueChange(currentRow.Id, broadcastList, 'BroadcastConfig'));
    }
  }, [targetKeysC]);

  const handleInputChange = (value, strategyId, type = 'Factor') => {
    const temp = clone(selectedStrategyObj);
    // 更新右侧视图显示
    const newStrategyList = map(initBroadcastStrategy, (strategy) => {
      if (strategy.StrategyId === strategyId) {
        const tempObj = clone(strategy);
        if (type === 'Value') {
          tempObj.Threshold[0].Values = +value;
        } else tempObj.Threshold[0].Factor = value;
        return tempObj;
      }
      return strategy;
    });
    setInitBroadcastStrategy(clone(newStrategyList));
    // 更新操作值
    const changeValue = type === 'Value'
      ? { ...temp[strategyId][0], Values: +value }
      : { ...temp[strategyId][0], Factor: value };
    temp[strategyId] = [changeValue];
    setSelectedStrategyObj(temp);
  };

  // 组合播报策略阈值修改
  const handleInputChangeC = (value, strategyId, type = 'Factor') => {
    const newStrategyList = map(initBroadcastStrategy, (strategy) => {
      if (strategy.StrategyId === strategyId) {
        const tempObj = clone(strategy);
        if (type === 'Value') {
          tempObj.Threshold[0].Values = +value;
        } else tempObj.Threshold[0].Factor = value;
        return tempObj;
      }
      return strategy;
    });
    setInitBroadcastStrategy(clone(newStrategyList));
  };

  // 当组合播报搜索条件以及当前行改变
  useEffect(() => {
    if (currentRow) {
      // 切换勾选的策略
      setTargetKeysC((currentRow.BroadcastConfig || []).map(i => `${i.StrategyId}`));
      getCombinedBroadStrategyList();
    }
  }, [combinedSelectValue, currentRow]);

  const handleStrategyListSave = async () => {
    try {
      const BroadcastConfig: any = map(
        toPairs(selectedStrategyObj),
        ([key, value]) => {
          const [strategyId, strategySource] = key.split('-');
          return {
            StrategyId: +strategyId,
            StrategySource: +strategySource,
            Threshold: value,
          };
        }
      );
      const res: any = await ModifyBroadcastUserStrategy({
        BroadcastId: broadcastId,
        BroadcastConfig,
        Enable: commonEnable,
        Updater: operator,
        ...(type === EENDTYPE.OPERATOR && { AppId: appId }),
      });
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        reload();
        return false;
      }
      reload();
      return true;
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
      return false;
    }
  };

  // 组合播报内容保存
  const combinedDataSave = async () => {
    const formatCombinedDetails = combinedDetails?.map((item) => {
      const { Children } = item || {};
      return {
        ...item,
        Children: Children?.map((i) => {
          const { BroadcastConfig } = i || {};
          return {
            ...i,
            BroadcastConfig: BroadcastConfig?.map((j) => {
              const [strategyId, strategySource] = j.StrategyId.split('-');
              return {
                ...j,
                StrategyId: +strategyId,
                StrategySource: +strategySource,
              };
            }),
          };
        }),
      };
    });
    try {
      const res: any = await ModifyCombinedBroadcastConfigs({
        BroadcastId: broadcastId,
        CombinedBroadcastConfig: {
          Enable: combinedEnable,
          Header: combinedHeader,
          Footer: combinedFooter,
          Details: formatCombinedDetails,
        },
        Updater: operator,
        ...(type === EENDTYPE.OPERATOR && { AppId: appId }),
      });
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        reloadC();
        return false;
      }
      reloadC();
      return true;
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
      return false;
    }
  };

  // 将保存方法暴露给父组件
  useImperativeHandle(ref, () => ({
    save: () => {
      if (combinedEnable) {
        // 是否存在至少一条数据的 BroadcastConfig 为空
        const hasEmptyBroadcastConfig = combinedDetails
          .some(item => item.Children?.some(child => child.BroadcastConfig?.length === 0));
        if (hasEmptyBroadcastConfig) {
          message.error({ content: t('组合播报中有播报策略未选择') });
          return false;
        }
      }
      return Promise.all([handleStrategyListSave(), combinedDataSave()]).then((res) => {
        const value = res.every(result => result);
        value && message.success({ content: t('保存成功') });
        return value;
      });
    },
  }));

  function extractStrategyIds(data) {
    let strategyIds = [];
    data.forEach((item) => {
      if (item.BroadcastConfig && item.BroadcastConfig.length > 0) {
        // 跳过对当前正在编辑的行的判断
        if (item.Id !== currentRow.Id) {
          item.BroadcastConfig.forEach((config) => {
            strategyIds.push(config.StrategyId);
          });
        }
      }
      if (item.Children && item.Children.length > 0) {
        const childStrategyIds = extractStrategyIds(item.Children);
        strategyIds = strategyIds.concat(childStrategyIds);
      }
    });
    return strategyIds;
  }

  function getShowList() {
    // 组合播报已经选择了的策略id
    const selectedStrategyIds = extractStrategyIds(combinedDetails);
    const tempList = initBroadcastStrategy.filter((i) => {
      if (showIdsCombined.includes(i.StrategyId)) {
        return i;
      }
    }).map((j) => {
      if (selectedStrategyIds.includes(j.StrategyId)) {
        j.disabled = true;
      } else {
        j.disabled = false;
      }
      return j;
    });
    return tempList;
  }

  const RenderBroadcastPicker = (
		<div className='intlc-broadcast__body'>
			<Transfer
				leftCell={
					<Transfer.Cell
						scrollable={false}
						title={t('选择播报策略')}
						header={<TagSearchBox {...tagSearchBoxProps} />}
					>
						<SourceTable
							dataSource={showIdsCommon.length
							  ? initBroadcastStrategy.filter(i => showIdsCommon.includes(i.StrategyId))
							  : []
							}
              isPeriodLimit={isBroadcastPeriodLimit}
							targetKeys={targetKeys}
							onChange={keys => setTargetKeys(keys)}
							loading={isLoading}
							sourceColumns={sourceColumns}
						/>
					</Transfer.Cell>
				}
				rightCell={
					<Transfer.Cell title={t('已选择 ({{attr0}})', { attr0: targetKeys.length })} className='targetTable'>
						<TargetTable
							dataSource={combinedModal ? [] : initBroadcastStrategy.filter(i => targetKeys.includes(`${i.StrategyId}`))}
							targetColumns={targetColumns}
							loading={isLoading}
						/>
					</Transfer.Cell>
				}
			/>
			{/* { */}
			{/* 	editable */}
			{/* 	&& <Form.Action style={{ display: 'flex', justifyContent: 'center' }}> */}
			{/* 		<Button type="primary" onClick={handleStrategyListSave}> */}
			{/* 			保存 */}
			{/* 		</Button> */}
			{/* 	</Form.Action> */}
			{/* } */}
		</div>
  );

  // 组装播报策略预览名称
  function getStrategyName(id) {
    let result = '';
    initBroadcastStrategy.map((i) => {
      if (i.StrategyId === id) {
        const subTypeTxt = subTypeOption.find((j: any) => j.value === i.SubType)?.text || '';
        result = `【${i.CNName}-${subTypeTxt}】`;
      }
    });
    return result;
  }

  // 按产品组装播报策略
  function strategyPreview(config) {
    // 产品-策略映射
    const previewMap = new Map();
    // 产品列表
    const productList = [];
    config.map((i) => {
      // 找到策略数据
      const item = initBroadcastStrategy.find(j => j.StrategyId === i.StrategyId) || {};
      // 初始化产品列表
      if (productList.findIndex(m => m.Product === item.Product) === -1) {
        productList.push({ Product: item.Product, ProductName: item.ProductName });
      }
      // 初始化映射
      if (previewMap.has(item.Product)) {
        const sortList = [...previewMap.get(item.Product), item];
        // 策略按照StrategyId升序排列
        sortList.sort((a, b) => a.StrategyId - b.StrategyId);
        previewMap.set(item.Product, sortList);
      } else {
        previewMap.set(item.Product, [item]);
      }
    });
    // 产品按字母升序排列
    productList.sort();
    return (
			<div>
				{productList.map(i => (
						<div key={JSON.stringify(i)}>
							<div className='strategyProductName'>【{i.ProductName}】</div>
							{
                previewMap.has(i.Product)
                && (previewMap.get(i.Product) || [])
                  .map(j => <div key={j.StrategyId}>{getStrategyName(j.StrategyId)}</div>)
              }
						</div>
				))}
			</div>
    );
  }

  return (
		<div className='intlc-broadcast-strategylist__inner'>
			<Collapse defaultActiveIds={['1', '2']} className='broadcastCollapse'>
				<Collapse.Panel id="1" title={
					<div className='intlc-broadcast__header'>
						<h3>{t('常规播报')}</h3>
						<Bubble
							arrowPointAtCenter
							content={t('根据已选择的播报策略，在群里按实例、总览维度分批播报')}
						>
							<Icon type="info" className='titleInfo' />
						</Bubble>
					</div>
				}>
					<div className='enableWrap'>
						<Text theme="label" className='enableLabel'>{t('是否播报')}</Text>
						<Switch value={commonEnable} onChange={(val) => {
						  setCommonEnable(val);
						}}></Switch>
					</div>
					{
						isEmpty(products)
						  ? <Status
								icon="blank"
								size="s"
								title={t('当前护航单没有产品或所有产品下没有相关策略 ')}
							/>
						  : RenderBroadcastPicker
					}
				</Collapse.Panel>
				<Collapse.Panel id="2" title={
					<div className='intlc-broadcast__header'>
						<h3>{t('组合播报')}</h3>
						<Bubble
							arrowPointAtCenter
							content={t('自定义播报头部、详情、尾部内容，在群里按预览格式进行组合播报。暂不支持在外部群播报。')}
						>
							<Icon type="info" className='titleInfo' />
						</Bubble>
					</div>
				}>
					<div className='enableWrap'>
						<Text theme="label" className='enableLabel'>{t('是否播报')}</Text>
						<Switch value={combinedEnable} onChange={(val) => {
						  setCombinedEnable(val);
						}}></Switch>
					</div>
					<div className='combinedWrap'>
						<Row gap={60}>
							<Col span={14}>
								<Row>
									<Col span={5}>
										<Text theme="label">{t('播报头部')}</Text>
									</Col>
									<Col span={19}>
										<TextArea
											disabled={!combinedEnable}
											rows={10}
											size='full'
											value={combinedHeader}
											onChange={(value) => {
											  setCombinedHeader(value);
											}}
											placeholder={t('输入播报头部内容')}
										/>
									</Col>
								</Row>
								<Row>
									<Col span={5}>
										<Text theme="label">{t('播报详情')}</Text>
									</Col>
									<Col span={19} className='detailsCol'>
										{!combinedEnable && <div className='disabledWrap'></div>}
										<Content>
											<Content.Body>
												<Table.ActionPanel>
													<Justify
														left={
															<>
																<Button type="primary" onClick={() => {
																  // 新增一条数据
																  const addData = { Id: uuid(), Title: '', Children: [] };
																  setCombinedDetails((oldValue) => {
																    const newData = clone(oldValue);
																    newData.push(addData);
																    return newData;
																  });
																}}>{t('新增标题')}</Button>
															</>
														}
													/>
												</Table.ActionPanel>
												<Card>
													<Table
														className='combinedTable'
														verticalTop
														records={combinedDetails}
														recordKey='Id'
														columns={combinedDetailColumns}
														// 配置插件
														addons={[
														  expandable({
														    // 已经展开的产品
														    expandedKeys,
														    // 产品展开为消息行
														    expand: i => i.Children || null,
														    // 发生展开行为时，回调更新展开键值
														    onExpandedKeysChange: key => setExpandedKeys(key),
														    // 只有产品行允许展开
														    shouldRecordExpandable: i => Boolean(i.Children),
														  }),
														]}
													/>
												</Card>
											</Content.Body>
										</Content>
									</Col>
								</Row>
								<Row>
									<Col span={5}>
										<Text theme="label">{t('播报尾部')}</Text>
									</Col>
									<Col span={19}>
										<TextArea
											disabled={!combinedEnable}
											rows={10}
											size='full'
											value={combinedFooter}
											onChange={(value) => {
											  setCombinedFooter(value);
											}}
											placeholder={t('输入播报尾部内容')}
										/>
									</Col>
								</Row>
							</Col>
							<Col span={10}>
								<div className='combinedPreview'>
									<div className='title'>
										<Text theme="label">{t('预览')}</Text>
										<Bubble
											arrowPointAtCenter
											content={t('预览中播报策略只展示名称。请自行调整播报时间观察群内完整播报。')}
										>
											<Icon type="info" className='titleInfo' />
										</Bubble>
									</div>
									<div className='priviewWrap'>
										{combinedHeader && <div className='preLineDiv'>{combinedHeader}</div>}
										{combinedDetails.map(i => (
												<div className='previewItem' key={i.Id}>
													{/* 播报标题 */}
													<div className='preLineDiv'>{i.Title}</div>
													{(i.Children || []).map(j => <div key={j}>
															{/* 子标题 */}
															<div className="preLineDiv childrenTitle">{j.Title}</div>
															{strategyPreview(j.BroadcastConfig)}
														</div>)}
												</div>
										))}
										{combinedFooter && <div className='preLineDiv'>{combinedFooter}</div>}
									</div>
								</div>
							</Col>
						</Row>
					</div>
				</Collapse.Panel>
			</Collapse>

			{/* 组合播报，播报策略配置弹框 */}
			<Modal size='80%' className='combinedModal' visible={combinedModal} caption={t('组合播报策略选择')} onClose={() => {
			  setCombinedModal(false);
			}}>
				<Modal.Body>
					<Transfer
						leftCell={
							<Transfer.Cell
								scrollable={false}
								title={t('选择播报策略')}
								header={<TagSearchBox {...combinedTagSearchBoxProps} />}
							>
								<SourceTable
									dataSource={showIdsCombined.length ? getShowList() : []}
									targetKeys={targetKeysC}
                  isPeriodLimit={isBroadcastPeriodLimit}
									onChange={keys => setTargetKeysC(keys)}
									loading={isLoadingC}
									sourceColumns={sourceColumns.filter(i => i.key !== 'Out' && i.key !== 'Type' && i.key !== 'Desc')}
									type='combined'
								/>
							</Transfer.Cell>
						}
						rightCell={
							<Transfer.Cell title={t('已选择 ({{attr0}})', { attr0: targetKeysC.length })} className='targetTable'>
								<TargetTable
									dataSource={initBroadcastStrategy.filter(i => targetKeysC.includes(`${i.StrategyId}`))}
									targetColumns={targetColumnsC.filter(i => i.key !== 'Out' && i.key !== 'Type' && i.key !== 'Desc')}
									loading={isLoadingC}
								/>
							</Transfer.Cell>
						}
					/>
				</Modal.Body>

			</Modal>
		</div>

  );
});

export default StrategyListPanel;
