.intlc-broadcast-strategylist__inner{
  position: relative;
  text-align: left;
  white-space: normal;
  vertical-align: middle;
  min-width: 420px;
  width: 100%;
  margin: 30px auto;
  background-color: #fff;
  box-shadow: 2px 2px 4px 0 rgb(54 58 80 / 32%);
  border-radius: 0;
  padding: 25px;
  box-sizing: border-box;
  padding-bottom: 70px;

  .content-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    cursor: not-allowed;
  }

  .intlc-broadcast__header{
    font-size: 14px;
    color: #000;
    position: relative;
    display: flex;
    align-items: center;
  }

  .intlc-broadcast__body{
    color: rgba(0, 0, 0, 0.9);
    word-break: break-word;
    word-wrap: break-word;

    .tea-transfer__separator {
      display: none;
    }
    .tea-transfer__cell {
      display: block;
    }
  }
  .targetTable{
    .tea-transfer__inner{
      overflow-y: hidden;
    }
  }

  .broadcastCollapse{
    .tea-accordion__body{
      padding: 14px;
    }
    .tea-icon-arrowup{
      transform: rotate(180deg);
    }
    .titleInfo{
      margin-left: 4px;
    }
    .enableWrap{
      margin: 10px 0;
      display: flex;
      align-items: center;
      .enableLabel{
        margin-right: 20px;
      }
    }
    .combinedWrap{
      .detailsCol{
        .tea-grid__box{
          position: relative;
          .disabledWrap{
            position: absolute;
            width: 100%;
            height: 100%;
            background-color:rgba(224, 226, 230,.6);
            z-index: 1;
          }
        }
      }
    }

    .combinedTable{
      td{
        vertical-align: middle;
      }
    }

    .combinedPreview{
      height: 100%;
      .title{
        display: flex;
        align-items: center;
      }
      .priviewWrap{
        height: calc(100% - 28px);
        margin-top: 10px;
        border: 1px solid #b2bcc9;
        box-sizing: border-box;
        .preLineDiv{
          white-space: pre-wrap;
        }
        .priviewBroadcast{
          display: flex;
          flex-wrap: wrap;
        }
        .previewItem{
          margin-bottom: 20px;
          .childrenTitle{
            margin-top: 10px;
          }
          .strategyProductName{
            margin: 10px 0 2px 0;
          }
        }
      }
    }
    
  }
}

.combinedModal{
  .targetTable{
    .tea-transfer__inner{
      overflow-y: hidden;
    }
  }
}