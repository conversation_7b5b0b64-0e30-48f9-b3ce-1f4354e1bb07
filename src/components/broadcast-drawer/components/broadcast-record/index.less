.statusReportWrap{
  margin-bottom: 20px;

    .t-collapse-panel__content {
      padding: 5px !important;
    }
    .tea-table + .tea-pagination {
      padding-bottom: 0;
    }

    .t-collapse-panel__wrapper .t-collapse-panel__header{
      padding: 6px;
    }
  
  .title{
    font-size: 14px;
    font-weight: 800;
    margin-bottom: 12px;
  }

  .broadcast-result-filter {
    margin-bottom: 10px;
  }
  .report-title {
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 400;
  }
  .statusText{
    margin:0 10px;
  }
  .tea-pagination__turnbtn{
    padding: 1px 4px !important;
  }
  .noBroadcastWrap{
    color: #000000E6;
    border: 1px solid #d8d8d8;
    padding: 6px;
    font-size: 12px;
  }
}

.codeWrap,.abnormalWrap{
  color: rgb(212, 212, 212);
  font-size: 13px;
  text-shadow: none;
  font-family: Menlo, Monaco, Consolas, "Andale Mono", "Ubuntu Mono", "Courier New", monospace;
  direction: ltr;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  line-height: 1.5;
  tab-size: 4;
  hyphens: none;
  padding: 1em;
  margin: 0.5em 0px;
  overflow: auto;
}

.codeWrap{
  background: rgb(30, 30, 30);
  margin-bottom: 10px;
}

.abnormalWrap{
  border: 1px solid #c9cfd9;
}

.lineWrap{
  display: flex;
  align-items: center;
  color: rgb(212, 212, 212);
  .lineIndex{
    display: inline-block;
    min-width: 2.25em;
    padding-right: 1em;
    text-align: right;
    user-select: none;
    color: rgb(106, 153, 85);
  }
  .lineMessage{
    margin-right: 30px;
    .value{
      color:#b5cea8 ;
    }
  }
  .line-index{
    margin-right: 30px;
  }
}

.abnormalWrap{
  .lineWrap,.lineIndex,.value{
    color: #e54545 !important;
  }
}

.productListBox{
  width: 800px;
  .tea-list--option{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    li{
      width: 32%;
    }
  }
}