import React, { useEffect, useState } from 'react';
import { t } from '@tea/app/i18n';
import { Collapse } from 'tdesign-react';
import './index.less';
import { Text, Table, StatusTip, Icon, Button, Modal, message, Form, SelectMultiple, Select } from '@tencent/tea-component';
import { DescribeBroadcastResults, DescribeArchNodeBroadcastStatusShow } from '@src/service/api/baseInfo';
import { getDescribeProductConfigListForGuard } from '@src/service/api/broadcast-drawer';
import CodeEditor from '@src/components/broadcast-drawer/components/code-editor';
import { map, find, isEmpty } from 'lodash';
import store from '@src/origin-store/store';
import {
  sourceOption,
  SOURCE_STATUS,
} from '@src/constants';
const { Panel } = Collapse;
const { pageable, scrollable } = Table.addons;;

interface Iprops {
  guardId: string;
  isFold?: boolean;
}
/** Root
 * @param props
 * @returns React.ReactElement
 */
export default function BroadcastRecord({ guardId, isFold = true }: Iprops): React.ReactElement {
  const { graphApi } = store.getState().guard;
  const {
    env,
  } = graphApi;
  const isCONSOLE = env === 'CONSOLE';
  const queryParams = new URLSearchParams(location.search);
  const appId = +queryParams.get('appid');
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalPage, setTotalPage] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [productOption, setProductOption] = useState([]);

  // 播报列表
  const [broadcastList, setBroadcastList] = useState([]);
  // 列表查询接口常规参数
  const [loading, setLoading] = useState<boolean>(false);
  // 播报状态信息
  const [statusInfo, setStatusInfo] = useState<any>({});

  const [resultContent, setResultContent] = useState('');
  const [resultDialogVisible, setResultDialogVisible] = useState(false);
  const [product, setProduct] = useState([]);
  const [resourceStatus, setResourceStatus] = useState('all');

  useEffect(() => {
    getBroadcastResults();
  }, [pageNumber, pageSize, product, resourceStatus]);

  useEffect(() => {
    getProductsGroupsInfo();
  }, []);

  useEffect(() => {
    getBroadcastStatus();
  }, [guardId]);

  // 获取产品和维度信息
  const getProductsGroupsInfo = async () => {
    try {
      const res: any = await getDescribeProductConfigListForGuard({
        TaskType: 'guardTaskType',
        Env: 'all',
      });
      const productDict = JSON.parse(res.ProductDict);
      const productNameList = [];
      // eslint-disable-next-line no-restricted-syntax
      for (const key in productDict) {
        productNameList.push({
          text: productDict[key],
          value: key,
        });
      }
      setProductOption(productNameList);
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
    }
  };

  // 播报列
  const columns = [
    {
      key: 'Product',
      header: t('云产品'),
      render: (item) => {
        const nameTxt = find(productOption, product => product.value === item.Product);
        return (<>{isEmpty(nameTxt) ? '-' : nameTxt.text}</>);
      },
    },
    {
      key: 'CNName',
      header: t('播报项'),
    },
    {
      key: 'IsNormal',
      header: t('资源状态'),
      render: (item) => {
        const status: any = find(sourceOption, status => status.value === `${item.IsNormal}`) || {};
        // eslint-disable-next-line no-nested-ternary
        const theme = +status.value === SOURCE_STATUS.SUCCESS ? 'success' : +status.value === SOURCE_STATUS.ERROR ? 'danger' : 'warning';
        return (<div style={{ display: 'flex', alignItems: 'center' }}>
              <Icon type={theme === 'danger' ? 'error' : theme} />
              <span style={{ marginLeft: '3px' }}>
                  <Text theme={theme}>{status.text}</Text>
              </span>
          </div>);
      },
    },
    {
      key: 'SendTime',
      header: t('播报时间'),
    },
    {
      key: 'Result',
      header: t('详情'),
      render: item => <Button type="link" onClick={() => handleViewResult(item)}>{t('查看详情')}</Button>,
    },
  ];

  const handleViewResult = (item) => {
    setResultContent(item.Result);
    setResultDialogVisible(true);
  };

  // 查询播报状态
  function getBroadcastStatus() {
    const params = {
      GuardId: guardId,
      ArchId: '',
      NodeUuid: '',
      Product: '',
      ...(!isCONSOLE && { AppId: Number(appId) }),
    };
    DescribeArchNodeBroadcastStatusShow(params).then((res) => {
      setStatusInfo(res || {});
    })
      .catch((err) => {
        console.log(err);
      });
  }

  // 查询播报列表
  const getBroadcastResults = async () => {
    setLoading(true);
    try {
      const params = {
        Filters: [
          { Name: 'guard_id', Values: [`${guardId}`] },
          { Name: 'product', Values: product },
          { Name: 'is_normal', Values: resourceStatus === 'all' ? [] : [resourceStatus] },
        ],
        Offset: (pageNumber - 1) * pageSize,
        Limit: pageSize,
        ...(!isCONSOLE && { AppId: Number(appId) }),
      };
      const res: any = await DescribeBroadcastResults(params);
      const broadcastResults = map(res.BroadcastResults, (item, index) => ({ ...item, Id: index }));
      setBroadcastList(broadcastResults);
      setTotalPage(res.TotalCount);
      setLoading(false);
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
      setLoading(false);
    }
  };

  const handlePageChange = ({ pageIndex, pageSize }) => {
    setPageSize(pageSize);
    setPageNumber(pageIndex);
  };

  return (
    <div className='statusReportWrap'>
      <div className='title'>
        {t('整体播报记录')}
      </div>
      <div className='broadcast-result-filter'>
        <Form layout="inline">
          <Form.Item label={t('云产品')}>
            <SelectMultiple
              listHeight={400}
              appearance="button"
              searchable
              size="m"
              boxClassName='productListBox'
              options={productOption}
              value={product}
              allOption={{ value: 'all', text: t('全部') }}
              onChange={(value) => {
                setPageNumber(1);
                setProduct(value);
              }}
              placeholder={t('请选择')}
            />
          </Form.Item>
          <Form.Item label={t('资源状态')}>
            <Select
              appearance="button"
              matchButtonWidth
              size="m"
              options={sourceOption}
              value={resourceStatus}
              onChange={(value) => {
                setPageNumber(1);
                setResourceStatus(value);
              }}
            />
          </Form.Item>
        </Form>
      </div>

      <Collapse expandIconPlacement='right' defaultValue={isFold ? [] : [1]}>
        <Panel header={
          <div className='report-title'>
            {t('当前播报周期')}
            <Text
              className='statusText'
              theme={statusInfo?.TotalException ? 'danger' : 'success'}
            >
              {statusInfo?.TotalException ? t('{{attr0}}项异常', { attr0: statusInfo?.TotalException }) : t('全部正常')}
            </Text>
            {statusInfo?.BroadcastTime && (statusInfo?.BroadcastTime)}
          </div>
        }>
          <Table
            className='instanceTableWrap'
            bordered={true}
            records={broadcastList}
            recordKey="Code"
            columns={columns}
            topTip={
              (loading || broadcastList.length === 0) && (
                <StatusTip status={loading ? 'loading' : 'empty'} />
              )
            }
            addons={[
              pageable({
                pageIndex: pageNumber,
                recordCount: totalPage,
                onPagingChange: handlePageChange,
              }),
              scrollable({ maxHeight: 500 }),
            ]}
          />
        </Panel>
      </Collapse>
      <Modal
        maskClosable
        size="xl"
        visible={resultDialogVisible}
        caption={t('详情')}
        onClose={() => setResultDialogVisible(false)}
      >
        <Modal.Body>
          <CodeEditor codeText={resultContent} language="json" />
        </Modal.Body>
      </Modal>
    </div>);
}
