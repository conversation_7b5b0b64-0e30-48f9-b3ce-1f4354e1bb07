/* eslint-disable max-len */
/* eslint-disable react/display-name */
import React, { useState, useEffect, useMemo, useImperativeHandle } from 'react';
import { DatePicker, TimePicker, Form, message, Select, Switch, Button, Modal, Radio, Bubble, Input } from '@tencent/tea-component';
import './index.less';
import moment from 'moment';
import { PushGuardChat, ModifyBroadcastBaseInfo, ModifyBroadcastUserStrategy } from '@src/service/api/broadcast-drawer';
import PlusIconSvg from '@src/assets/svg/plus.svg';
import NotIconSvg from '@src/assets/svg/not.svg';
import NotIconSvgDisable from '@src/assets/svg/not-disable.svg';
import originStore from '@src/origin-store/store';
import { EENDTYPE } from '@src/constants';
import BroadcastRecord from '@src/components/broadcast-drawer/components/broadcast-record';
import uuid from 'react-uuid';
import { changeCommonData } from '@src/store/app-common';
import { useDispatch } from 'react-redux';
import { SourceType } from '../../constants';
import { pick, isEqual, isEmpty, clone, filter, map, find } from 'lodash';
import { getProcessEnv } from '../../utils';
import { t, Trans, Slot } from '@tea/app/i18n';

interface IBroadcastBasePanelProps {
  guardInfo: any;
  broadcastInfo: any
  editable: boolean
  operator: string
  onInfoChange: Function
  enable?: boolean
  reload: Function
}

const compareSelectOption = [
  { text: t('5分钟'), value: '5' },
  { text: t('10分钟'), value: '10' },
  { text: t('30分钟'), value: '30' },
  { text: t('60分钟'), value: '60' },
  { text: t('2小时'), value: '120' },
  { text: t('3小时'), value: '180' },
];

enum OPERATOR_TYPE  {
  DATE = 'DATE',
  TIME = 'TIME',
  PERIOD= 'PERIOD'
}
const FORMAT_DATE = 'YYYY-MM-DD HH:mm:ss';

const TIME_FORMAT_DATE = 'HH:mm';

const DATE_FORMAT =  'YYYY-MM-DD';

const { RangePicker } = DatePicker;
const { RangePicker: TimeRangePicker } = TimePicker;

function generateUniqueDate(dateArray, startDate, endDate) {
  // 创建一个包含所有可能日期的数组
  const allDates = [];
  const currentDate = moment(startDate);

  while (currentDate.isSameOrBefore(endDate)) {
    allDates.push(currentDate.format('YYYY-MM-DD'));
    currentDate.add(1, 'days');
  }

  // 删除已经存在的日期
  const availableDates = allDates.filter(date => !dateArray.includes(date));
  if (isEmpty(availableDates)) {
    return moment(startDate).format('YYYY-MM-DD');
  }
  // 从剩余的日期中随机选择一个
  const uniqueDate = availableDates[Math.floor(Math.random() * availableDates.length)];

  return uniqueDate;
}

enum ESpecifyGtsChatOff {
  TRUE = '1',
  FALSE = '0',
}

const BroadcastBasePanel = React.forwardRef((
  {
    guardInfo,
    broadcastInfo,
    editable,
    operator,
    onInfoChange,
    enable,
    reload,
  }: IBroadcastBasePanelProps,
  ref
) => {
  // 当前环境
  const env = getProcessEnv();
  const queryParams = new URLSearchParams(location.search);
  const appId = +queryParams.get('appid');
  const { graphApi }  = originStore.getState().guard;
  const type = {
    CONSOLE: EENDTYPE.CUSTOMER,
    ISA: EENDTYPE.OPERATOR,
  }[graphApi.env];

  const {
    GuardId,
    MainAppId,
    StartTime: GuardStartTime,
    EndTime: GuardEndTime,
  } = guardInfo;
  const {
    BroadcastId,
    GroupIds,
    StartTime,
    EndTime,
    StartClock,
    EndClock,
    Period,
    OnlyWorkdays,
    GtsChatid,
    AntoolTaskId,
    Abnormal,
    BroadCastStandTimes,
    ProcessInstanceId,
    StandEnable,
  } = broadcastInfo;
  const dispatch = useDispatch();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [wcGroupIds, setWcGroupIds] = useState(GroupIds);

  const [rangeDateTime, setRangeDateTime] = useState([moment(StartTime).format(FORMAT_DATE),
    moment(EndTime).format(FORMAT_DATE)]);

  const [timeRange, setTimeRange] = useState([StartClock, EndClock]);

  const [tipPeriod, setTipPeriod] = useState(`${Period / 60}` || '30');

  const [isOnlyWorkdays, setIsOnlyWorkdays] = useState(OnlyWorkdays);

  const [abnormal, setAbnormal] = useState(Abnormal);

  const [isShowTip, setIsShowTip] = useState(false);

  const [isStandBroadcast, setIsStandBroadcast] = useState(StandEnable);
  const initBroadCastStandTimes = map(BroadCastStandTimes, item => ({ ...item, StandPeriod: `${item?.StandPeriod / 60}` }));
  const [broadCastStandTimes, setBroadCastStandTimes] = useState(initBroadCastStandTimes || []);
  const start = moment(GuardStartTime, FORMAT_DATE);
  const end = moment(GuardEndTime, FORMAT_DATE);
  const isGuarding = guardInfo.Status > 1 && moment().isBetween(start, end);
  const [specifyGtsChatOff, setSpecifyGtsChatOff] = useState(broadcastInfo?.SpecifyGtsChatOff ? ESpecifyGtsChatOff.TRUE : ESpecifyGtsChatOff.FALSE);
  const [specifyGtsChatId, setSpecifyGtsChatId] = useState(broadcastInfo?.SpecifyGtsChatid || '');
  const initFromInfo = pick(broadcastInfo, ['GroupIds', 'StartTime', 'EndTime', 'Period', 'OnlyWorkdays', 'StartClock', 'EndClock']);
  // 外部群弹框提示
  const [showTip, setShowTip] = useState(false);
  const URL = `https://${env === 'production' ? '' : 'test-'}antool.woa.com/fe-base/antool-page/visit/info?taskId=${AntoolTaskId}&processInstanceId=${ProcessInstanceId}`;

  const ruleDetail = useMemo(() => ({
    // GroupIds: wcGroupIds,
    StartTime: moment(rangeDateTime[0]).format(FORMAT_DATE),
    EndTime: moment(rangeDateTime[1]).format(FORMAT_DATE),
    StartClock: timeRange[0],
    EndClock: timeRange[1],
    Period: +tipPeriod * 60,
    OnlyWorkdays: isOnlyWorkdays,
    Abnormal: abnormal,
    StandBroadcast: isStandBroadcast,
    SpecifyGtsChatOff: specifyGtsChatOff === ESpecifyGtsChatOff.TRUE,
    SpecifyGtsChatId: specifyGtsChatId,
    BroadCastStandTimes: map(broadCastStandTimes, item => ({
      ...item,
      StandPeriod: +item?.StandPeriod * 60,
      ...(typeof item?.StandTimeConfigId === 'string' && { StandTimeConfigId: 0 }),
    })),
  }), [wcGroupIds, rangeDateTime, timeRange, tipPeriod,
    isOnlyWorkdays, isStandBroadcast, abnormal, broadCastStandTimes, specifyGtsChatOff, specifyGtsChatId]);

  useEffect(() => {
    onInfoChange(isEqual(initFromInfo, ruleDetail));
  }, [initFromInfo, ruleDetail]);

  useEffect(() => {
    if (isEmpty(broadCastStandTimes)) {
      setBroadCastStandTimes([{
        StandTimeConfigId: uuid(),
        StandPeriod: '5',
        StandStartTime: moment(rangeDateTime[0]).add(1, 'minutes')
          .format(TIME_FORMAT_DATE),
        StandEndTime: moment(rangeDateTime[0]).add(2, 'minutes')
          .format(TIME_FORMAT_DATE),
        StandDate: generateUniqueDate(
          [],
          moment(rangeDateTime[0]).format(DATE_FORMAT),
          moment(rangeDateTime[1]).format(DATE_FORMAT)
        ),
      }]);
    }
  }, [broadCastStandTimes]);

  const handleRangePickerChange = (value) => {
    setRangeDateTime([
      value[0].format(FORMAT_DATE),
      value[1].format(FORMAT_DATE),
    ]);
  };

  const handelBroadcastTimesChange = (value, timeId, type) => {
    const broadCastTimes = clone(broadCastStandTimes);
    const broadCastStandTimesNew = broadCastTimes?.map((item) => {
      if (item?.StandTimeConfigId === timeId) {
        let ret = {};
        if (type === OPERATOR_TYPE.DATE) {
          ret = {
            ...item,
            StandDate: value.format(DATE_FORMAT),
          };
        } else if (type === OPERATOR_TYPE.TIME) {
          ret = {
            ...item,
            StandStartTime: value[0].format(TIME_FORMAT_DATE),
            StandEndTime: value[1].format(TIME_FORMAT_DATE),
          };
        } else if (type === OPERATOR_TYPE.PERIOD) {
          ret = {
            ...item,
            StandPeriod: value,
          };
        }
        return ret;
      }
      return item;
    });
    setBroadCastStandTimes(broadCastStandTimesNew);
  };

  const handleBroadcastAdd = (id) => {
    const broadCastTime = clone(broadCastStandTimes);
    const index = broadCastTime.findIndex(obj => obj.StandTimeConfigId === id);
    const StandDateArray = map(broadCastTime, i => i?.StandDate);
    if (index !== -1) {
      broadCastTime.splice(index + 1, 0, {
        StandTimeConfigId: uuid(),
        StandPeriod: '5',
        StandStartTime: moment(rangeDateTime[0]).add(1, 'minutes')
          .format(TIME_FORMAT_DATE),
        StandEndTime: moment(rangeDateTime[0]).add(2, 'minutes')
          .format(TIME_FORMAT_DATE),
        StandDate: generateUniqueDate(
          StandDateArray,
          moment(rangeDateTime[0]).format(DATE_FORMAT),
          moment(rangeDateTime[1]).format(DATE_FORMAT)
        ),
      });
    }
    setBroadCastStandTimes(broadCastTime);
  };

  const handleBroadcastDelete = (id) => {
    const broadCastTimeTemp = clone(broadCastStandTimes);
    const broadCastTimes = filter(broadCastTimeTemp, item => item?.StandTimeConfigId !== id);
    setBroadCastStandTimes(broadCastTimes);
  };


  const isBetweenDate = (item) => {
    const { StandDate, StandStartTime, StandEndTime } = item;
    const startTime = moment(`${StandDate} ${StandStartTime}`).format(FORMAT_DATE);
    const endTime = moment(`${StandDate} ${StandEndTime}`).format(FORMAT_DATE);
    return moment(startTime).isBetween(moment(rangeDateTime[0]), moment(rangeDateTime[1]), null, '[]')
    && moment(endTime).isBetween(moment(rangeDateTime[0]), moment(rangeDateTime[1]), null, '[]');
  };

  const isOverLaping = (tagetItem) => {
    let ret = false;
    const repeatDate = find(broadCastStandTimes, i => (
      i?.StandDate === tagetItem?.StandDate && i?.StandTimeConfigId !== tagetItem?.StandTimeConfigId
    )) || {};
    if (!isEmpty(repeatDate)) {
      const isNoOverlapping = ((repeatDate?.StandEndTime < tagetItem?.StandStartTime)
      ||  (tagetItem?.StandEndTime <  repeatDate?.StandStartTime));
      if (!isNoOverlapping) {
        ret = true;
      }
    }
    return ret;
  };
  const handleBaseInfoSave = async () => {
    if (specifyGtsChatOff === ESpecifyGtsChatOff.TRUE) {
      if (isEmpty(specifyGtsChatId)) {
        message.error({ content: t('请输入指定播报群ID') });
        return;
      }
    }
    if (isStandBroadcast) {
      setIsShowTip(false);
      let flag = false;
      map(broadCastStandTimes, (item) => {
        if (!isBetweenDate(item)) {
          setIsShowTip(true);
          flag = true;
          return;
        }
        if (isOverLaping(item)) {
          setIsShowTip(true);
          flag = true;
          return;
        }
      });
      if (flag) return;
    }
    try {
      const res: any = await ModifyBroadcastBaseInfo({
        ...ruleDetail,
        BroadcastId,
        Updater: operator,
        ...(type === EENDTYPE.OPERATOR && { AppId: appId }),
      });
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        reload();
        return false;
      }
      let isLimitBroadcast = false;
      if (isStandBroadcast) {
        map(ruleDetail?.BroadCastStandTimes, (item) => {
          // 5分钟限制监控指标策略
          if (item?.StandPeriod === 300) {
            isLimitBroadcast = true;
          }
        });
      } else {
        if (ruleDetail?.Period === 300) {
          isLimitBroadcast = true;
        }
      }
      dispatch(changeCommonData({
        isBroadcastPeriodLimit: isLimitBroadcast,
      }));
      const broadcastFilter = filter(broadcastInfo?.BroadcastConfig, item => item?.StrategySource === SourceType.MONITOR_BROADCAST_CONFIG);
      if (isLimitBroadcast && !isEmpty(broadcastFilter)) {
        const broadcastConfig = filter(broadcastInfo?.BroadcastConfig, item => item?.StrategySource !== SourceType.MONITOR_BROADCAST_CONFIG);
        const rs: any = await ModifyBroadcastUserStrategy({
          BroadcastId,
          BroadcastConfig: broadcastConfig,
          Enable: (enable === undefined || enable === null) ? true : enable,
          Updater: operator,
          ...(type === EENDTYPE.OPERATOR && { AppId: appId }),
        });
        if (rs.Error) {
          const msg = res.Error.Message;
          message.error({ content: msg });
          reload();
          return false;
        }
        message.success({ content: t('已自动保存') });
        reload();
        return true;
      }
      message.success({ content: t('已自动保存') });
      reload();
      return true;
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
      return false;
    }
  };

  // 将保存方法暴露给父组件
  useImperativeHandle(ref, () => ({
    save: handleBaseInfoSave,
  }));

  const handleJoinChat = () => {
    PushGuardChat({
      GuardId,
      AppId: MainAppId,
      Type: 'broadcast',
      User: [operator],
      ...(type === EENDTYPE.OPERATOR && { AppId: appId  }),
    })
      .then((res: any) => {
        if (res.Error) {
          message.error({ content: res.Error.Message });
          return;
        }
        message.success({ content: res.Message });
      })
      .catch((err) => {
        const msg = err.msg || err.toString() || t('未知错误');
        message.error({ content: msg });
      });
  };

  return (
		<div className='intlc-broadcast-base-info__inner' >
			{!editable && <div className='content-mask'></div>}
      <div className='broadcast-info-status'>
        {
          isGuarding
          && <BroadcastRecord guardId={GuardId}/>
        }
      </div>
			<div className='intlc-broadcast__inner'>
				<div className='intlc-broadcast__header'>
					<h3>{t('修改推送规则')}</h3>
				</div>
				<div className='intlc-broadcast__body'>
					<Form>
						<Form.Item label={t('护航播报群（内部）')} tips={t('仅用于护航播报，默认只有护航提单人和护航负责人在群内。')}>
							<Button type="text" className='intlc-broadcast-chat'>{GroupIds[0]}</Button>
							<Button type="link" onClick={handleJoinChat}>
								{t('一键入群')}
							</Button>
						</Form.Item>
            <Radio.Group style={{ width: 148, height: 86 }} value={specifyGtsChatOff} onChange={(value) => {
              if (value === ESpecifyGtsChatOff.FALSE) {
                setSpecifyGtsChatId('');
              }
              setSpecifyGtsChatOff(value as ESpecifyGtsChatOff);
            }}>
              <Form.Item className='intlc-broadcast-chat-radio' label={t('护航播报群（外部）')} tips={t('通过 Antool 建立，只支持总览类播报策略。')}>
                <Radio name={ESpecifyGtsChatOff.FALSE}>
                  <Button type="link" style={{ color: '#000000E6' }} className='intlc-broadcast-chat'>
                    {
                      GtsChatid
                      || <span style={{ color: '#0006' }}>
                          {
                            AntoolTaskId
                              ? <>
                                  <span>{t('未建立，到antool任务单拉群')}</span>
                                  <span>
                                    (<a href={URL} target="_blank" rel="noreferrer" className='link'>{AntoolTaskId}</a>)
                                  </span>
                              </>
                              : t('暂无')
                          }
                        </span>
                    }
                  </Button>
                  {
                    AntoolTaskId
                    && <Button type="link" onClick={() => {
                      setShowTip(true);
                    }}>
                      { GtsChatid ? t('入群指引') : t('建群指引')}
                    </Button>
                  }
                </Radio>
              </Form.Item>
              <Form.Item style={{ position: 'relative', top: 6 }} className='intlc-broadcast-chat-radio' label={t('指定播报群（外部）')} tips={t('可指定播报外部群，群 ID 格式如：wrRiX7DwAAKvSzntUCebjDPkph1ZmmtR')}>
                <Radio name={ESpecifyGtsChatOff.TRUE}>
                  <Input value={specifyGtsChatId} onChange={value => setSpecifyGtsChatId(value.trim())} placeholder={t('请填入安灯外部群 ID')} disabled={specifyGtsChatOff === ESpecifyGtsChatOff.FALSE} style={{ transform: 'translateY(-5px)', width: 280 }} />
                </Radio>
              </Form.Item>
            </Radio.Group>
						<Form.Item required label={t('开始/结束时间')}>
							<RangePicker
								defaultValue={[moment(StartTime), moment(EndTime)]}
                showTime
								range={[moment(GuardStartTime), moment(GuardEndTime)]}
								onChange={value => handleRangePickerChange(value)}
							/>
						</Form.Item>
            <Form.Item
              required
              label={
                <Radio
                  value={!isStandBroadcast}
                  onChange={val => setIsStandBroadcast(!val)}
                >
                  {t('统一播报时段')}
                </Radio>
              }
              className='stand-broadcast-form'
            >
              <TimeRangePicker
                defaultValue={StartClock
                  ? [moment(StartClock, TIME_FORMAT_DATE), moment(EndClock, TIME_FORMAT_DATE)]
                  : undefined
                }
                disabled={isStandBroadcast}
                format={TIME_FORMAT_DATE}
                onChange={value => setTimeRange([
                  value[0].format(TIME_FORMAT_DATE),
                  value[1].format(TIME_FORMAT_DATE),
                ])
                }
              />
              <div className='broadcast-stand-period'>
                <p className='period-label'>{t('频率')}</p>
                <p>*</p>
                <Select
                  disabled={isStandBroadcast}
                  value={tipPeriod}
                  matchButtonWidth
                  options={compareSelectOption}
                  appearance="button"
                  size='s'
                  onChange={value => setTipPeriod(value)}
                />
              </div>
              <div className='only-workdays'>
                <p className='only-workdays-label'>{t('仅工作日提醒')}</p>
                <Switch
                  value={isOnlyWorkdays}
                  onChange={value => setIsOnlyWorkdays(value)}
                  disabled={isStandBroadcast}
                ></Switch>
              </div>
            </Form.Item>
            <Form.Item
              required
              label={
                <Radio
                  value={isStandBroadcast}
                  onChange={val => setIsStandBroadcast(val)}
                >
                  {t('独立播报时段')}
                </Radio>
              }
            >
              {
                broadCastStandTimes?.map((item, index) => (
                  <Bubble
                    error
                    visible={isStandBroadcast && isShowTip && (!isBetweenDate(item) || isOverLaping(item)) }
                    content={t('播报时段重叠或超出播报开始结束时间')}
                    arrowPointAtCenter
                    placement='top'
                    key={item?.StandTimeConfigId}
                  >
                    <Form.Item
                      className='alone-broadcast-box'
                      status={isStandBroadcast && isShowTip && (!isBetweenDate(item) || isOverLaping(item)) ? 'error' : 'success'}
                    >
                      <div className='alone-broadcast-form' >
                        <DatePicker
                          disabled={!isStandBroadcast}
                          value={moment(item?.StandDate)}
                          range={[moment(rangeDateTime[0]), moment(rangeDateTime[1])]}
                          onChange={val => handelBroadcastTimesChange(val, item?.StandTimeConfigId, OPERATOR_TYPE.DATE)}
                          style={{ marginRight: 20 }}
                        />
                        <TimeRangePicker
                          disabled={!isStandBroadcast}
                          value={[
                            moment(item?.StandStartTime, TIME_FORMAT_DATE),
                            moment(item?.StandEndTime, TIME_FORMAT_DATE),
                          ]}
                          format={TIME_FORMAT_DATE}
                          onChange={value => handelBroadcastTimesChange(
                            value,
                            item?.StandTimeConfigId,
                            OPERATOR_TYPE.TIME
                          )}
                        />
                        <div className='broadcast-stand-period'>
                          <p className='period-label'>{t('频率')}</p>
                          <p>*</p>
                          <Select
                            disabled={!isStandBroadcast}
                            value={item?.StandPeriod}
                            matchButtonWidth
                            options={compareSelectOption}
                            appearance="button"
                            size='s'
                            onChange={value => handelBroadcastTimesChange(
                              value,
                              item?.StandTimeConfigId,
                              OPERATOR_TYPE.PERIOD
                            )}
                          />
                        </div>
                        <div>
                          <img
                            src={PlusIconSvg}
                            alt={t('添加')}
                            className='plus-broadcast'
                            style={!isStandBroadcast ? { cursor: 'no-drop' } : { cursor: 'pointer' }}
                            onClick={() => {
                              if (!isStandBroadcast) return;
                              handleBroadcastAdd(item?.StandTimeConfigId);
                            }}
                          />
                          <img
                            src={(broadCastStandTimes?.length === 1 && index === 0) ? NotIconSvgDisable : NotIconSvg}
                            alt={t('删除')}
                            style={(!isStandBroadcast || index === 0) ? { cursor: 'no-drop' } : { cursor: 'pointer' }}
                            className='cut-broadcast'
                            onClick={() => {
                              if ((broadCastStandTimes?.length === 1 && index === 0) || !isStandBroadcast) return;
                              handleBroadcastDelete(item?.StandTimeConfigId);
                            }}
                          />
                        </div>
                      </div>
                    </Form.Item>
                  </Bubble>
                ))
              }
            </Form.Item>
            <Form.Item label={t('播报异常汇总')} tips={t('针对阈值告警类播报指标，仅播报超过阈值的异常情况')}>
							<Switch value={abnormal} onChange={value => setAbnormal(value)}></Switch>
						</Form.Item>
					</Form>
				</div>
			</div>
			<Modal visible={showTip} caption={t('当前子任务单ID：{{AntoolTaskId}}', { AntoolTaskId })} onClose={() => {
			  setShowTip(false);
			}} disableCloseIcon>
        <Modal.Body style={{ lineHeight: '24px' }}>
					<div><Trans>1. 【确认子任务单】<a href={URL} target="_blank" rel="noreferrer"><Slot content={URL} /></a></Trans></div>
					{ GtsChatid
					  ? <div>{t('2. 【入群】在子任务单页面右下角，点击“一键拉群”图标，进入已有外部群；')}</div>
					  :						<>
							<div>{t('2. 【建群】在子任务单页面右下角，点击“一键拉群”图标，建立外部群；')}</div>
							<div>{t('3. 【配置播报】建立外部群成功后，刷新本页面，继续配置播报订阅；')}</div>
						</>
					}
					<div style={{ marginTop: 20 }}>{t('注意，如 Antool 页面存在权限问题，请按需申请操作；如过程有疑惑，请联系 lucasxye。')}</div>
				</Modal.Body>
        <Modal.Footer>
          <Button type="primary" onClick={() => {
            setShowTip(false);
          }}>
            {t('确定')}
          </Button>
        </Modal.Footer>
      </Modal>
		</div>
  );
});

export default BroadcastBasePanel;
