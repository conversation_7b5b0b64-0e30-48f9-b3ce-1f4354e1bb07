.intlc-broadcast-base-info__inner{
  position: relative;
  padding-bottom: 72px;

  .broadcast-info-status {
    margin-top: 20px;
  }
  .intlc-broadcast-chat-radio {
    .tea-form__controls {
      position: absolute;
    }
  }
  .intlc-broadcast__inner {
    width: 100%;
    text-align: left;
    white-space: normal;
    vertical-align: middle;
    min-width: 420px;
    margin: 15px auto;
    background-color: #fff;
    box-shadow: 2px 4px 4px 0 rgb(54 58 80 / 32%);
    border-radius: 0;
    padding: 25px;
    box-sizing: border-box;
    .intlc-broadcast__header{
      font-size: 14px;
      min-height: 22px;
      color: #000;
      position: relative;
      margin-bottom: 10px;
    }

    .intlc-broadcast__body{
      color: rgba(0, 0, 0, 0.9);
      word-break: break-word;
      word-wrap: break-word;

      .intlc-broadcast__operate {
        display: flex;
        justify-content: center;
      }
    }
  }

  .intlc-broadcast-chat {
    padding: 0 15px 0 0;
  }
}

.broadcast-stand-period {
  display: flex;
  align-items: center;
  margin-left: 24px;
  .period-label {
    width: 30px;
    color: #888;
    font-size: 12px;
  }
  p {
    color: red;
    font-size: 12px;
    margin-right: 10px;
  }
}

.only-workdays {
  display: flex;
  align-items: center;
  margin-left: 24px;
  .only-workdays-label {
    width: 90px;
    color: #888;
    font-size: 12px;
  }
}

.stand-broadcast-form  {
  .tea-form__controls--text {
    display: flex;
  }
}

.alone-broadcast-box {
  .tea-form__controls.tea-form__controls--text > .tea-icon-valid {
    display: none;
  }
  .tea-form__label {
    padding-right: 0;
  }
  .tea-form__controls {
    padding-bottom: 0;
  }
}
.alone-broadcast-form {
  display: flex;
  margin-bottom: 15px;
  align-items: center;

  .plus-broadcast {
    margin-left: 12px;
    cursor: pointer;
  }

  .cut-broadcast {
    margin-left: 8px;
    cursor: pointer;
  }

  .cut-broadcast-disable {
    cursor: not-allowed;

    path {
      fill: #888;
    }
  }
}
.content-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  cursor: not-allowed;
}