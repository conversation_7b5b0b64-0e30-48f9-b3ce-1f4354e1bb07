/* eslint-disable react/prop-types */
import React from 'react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface IProps {
  codeText: string;
  language: string;
}

class Code extends React.Component {
  render() {
    return <SyntaxHighlighter
      showLineNumbers
      // @ts-ignore
      language={this.props.lang}
      wrapLines
      style={vscDarkPlus}
    >
      {/* @ts-ignore */}
      {this.props.children.replace(/^\s+|\s+$/g, '')}
    </SyntaxHighlighter>;
  }
}


export default function CodeHighLighter({ codeText, language }: IProps) {
  return (
    // @ts-ignore
    <Code lang={language}>{codeText}</Code>
  );
}
