import React from 'react';
import { t } from '@tea/app/i18n';
import './index.less';
import { Button  } from '@tencent/tea-component';
import { setDrawerVisibleMapAction } from '@src/origin-store/guardAction';
import { guardInfo } from '@src/utils/caching';
import BroadcastRecord from '@src/components/broadcast-drawer/components/broadcast-record';
import store from '@src/origin-store/store';
import moment from 'moment';

const FORMAT_DATE = 'YYYY-MM-DD HH:mm:ss';
/** Root
 * @param props
 * @returns React.ReactElement
 */
export default function CustomerBroadcast(): React.ReactElement {
  const {
    StartTime: GuardStartTime,
    EndTime: GuardEndTime,
  } = guardInfo;
  const start = moment(GuardStartTime, FORMAT_DATE);
  const end = moment(GuardEndTime, FORMAT_DATE);
  const isGuarding = guardInfo.Status > 1 && moment().isBetween(start, end);

  return (
    <div className='statusReportWrap'>
      <div className='broadcast-info-status'>
        {
          isGuarding
          && <BroadcastRecord
            guardId={guardInfo.GuardId}
            isFold={false}
          />
        }
      </div>
      <div className='broadcast-opt'>
        <div className='broadcast-opt-box'>
          <Button type="primary" onClick={() => store.dispatch(setDrawerVisibleMapAction({ broadcastVisible: false }))}>{t('关闭')}</Button>
        </div>
      </div>
    </div>
  );
}
