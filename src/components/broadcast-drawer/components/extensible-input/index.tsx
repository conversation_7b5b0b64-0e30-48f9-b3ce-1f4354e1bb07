

import React from 'react';
import './index.less';
import { Button, Input } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
interface IExtensibleInputProps {
  initialList: Array<string>
  disableIndex?: number
  limit?: number
  onInputAdd: Function
  onInputChange: Function
  onInputDelete: Function
}

const ExtensibleInput = ({
  initialList,
  disableIndex = -1,
  limit = 50,
  onInputAdd,
  onInputChange,
  onInputDelete,
}: IExtensibleInputProps) => (
  <>
      <div className='intlc-broadcast__unit'>
          <ul className='intlc-broadcast__list'>
              {
                  initialList?.map((item, index) => (
                      <div key={index} className='intlc-broadcast__item'>
                          <div className='intlc-broadcast-number'>{index + 1}</div>
                          <div>
                              <Input autoComplete="off" disabled={disableIndex === index} value={item} onChange={value => onInputChange(value, index)} />
                              {
                                  initialList?.length > 1
                                  && disableIndex !== index
                                  && <Button icon="dismiss" htmlType="button" style={{ marginLeft: 8 }} title={t('删除')} onClick={() => onInputDelete(index)} />
                              }
                          </div>
                      </div>))
              }
          </ul>
          {
              initialList?.length < limit
              && <Button type="link" htmlType="button" onClick={() => onInputAdd()}>{t('添加')}</Button>
          }
      </div>
  </>
);
export default ExtensibleInput;
