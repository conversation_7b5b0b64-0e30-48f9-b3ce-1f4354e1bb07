import React, { useState, useEffect, useMemo, useRef } from 'react';
import { message, But<PERSON>, Stepper, Status } from '@tencent/tea-component';
import { Empty } from 'tdesign-react';
import { map, isEmpty, merge } from 'lodash';
import BroadcastBasePanel from '../broadcast-base-panel';
import StrategyListPanel from '../strategy-list-panel';
import ResourceSupplePanel from '../resource-supple-panel';
import SubmitPanel from '../submit-panel';
import originStore from '@src/origin-store/store';
import { EENDTYPE } from '@src/constants';
import { app } from '@tea/app';
import {
  getBroadcastSheet,
  DescribeCombinedBroadcastConfigs,
  ModifyBroadcastOnline,
  DescribeGuardSheet,
} from '@src/service/api/broadcast-drawer';
import { setDrawerVisibleMapAction } from '@src/origin-store/guardAction';
import { GuardParams } from '@src/types/broadcast';
import { SOURCE_STATUS, STATUS } from '../../constants';
import { t } from '@tea/app/i18n';
import { guardInfo } from '@src/utils/caching';
import './index.less';

const steps = [
  { id: 'baseInfo', label: t('基础信息') },
  { id: 'strategyList', label: t('选择播报策略') },
  { id: 'resourceSupple', label: t('选择播报资源') },
  { id: 'submit', label: t('启用') },
];

const defaultInfoChangeStatus = {
  ruleInfoStatus: false,
  broadStrategyStatus: false,
  pendingStatus: false,
};

export function BroadcastEditor() {
  const queryParams = new URLSearchParams(location.search);
  const appId = +queryParams.get('appid');
  const {
    graphApi,
  }  = originStore.getState().guard;
  const type = {
    CONSOLE: EENDTYPE.CUSTOMER,
    ISA: EENDTYPE.OPERATOR,
  }[graphApi.env];

  // 护航单信息
  const [currentGuardInfo, setCurrentGuardInfo] = useState<GuardParams>({});
  // 护航单ID
  const [broadcastId, setBroadcastId] = useState<number>();
  const guardId = guardInfo.GuardId;
  // 播报订阅信息
  const [broadcastInfo, setBroadcastInfo] = useState<any>({});
  // 组合播报信息
  const [combinedBroadcastConfig, setCombinedBroadcastConfig] = useState<any>({});

  // 步骤信息改变状态
  const [changeStatus, setChangeStatus] = useState(defaultInfoChangeStatus);

  // 启用状态
  const [switchValue, setSwitchValue] = useState(false);

  // 控制步骤
  const [current, setCurrent] = useState('baseInfo');
  const currentIndex = current ? steps.findIndex(x => x.id === current) : -1;
  const next = current && steps[currentIndex + 1];
  const prev = current ? steps[currentIndex - 1] : steps[steps.length - 1];

  const isBroadcastEditabale = true;
  const operator = localStorage.getItem('engName');
  const [custOperator, setCustOperator] = useState('');

  // 调用子组件方法
  const saveRef = useRef(null);
  // 点击下一步时进入 loading 等待保存完成
  const [loadingNext, setLoadingNext] = useState(false);
  const [loadingLast, setLoadingLast] = useState(false);
  const [disabledNext, setDisabledNext] = useState(false);
  const [disabledLast, setDisabledLast] = useState(false);

  // 获取护航信息
  const getGuardSheet = () => {
    const filters = [{ Name: 'guard_id', Values: [`${guardId}`] }];
    DescribeGuardSheet({
      Filters: filters.filter((i) => {
        if (i.Values.length) return i;
      }),
      Offset: 0,
      Limit: 10,
      ...(type === EENDTYPE.OPERATOR && { AppId: appId }),
    })
      .then((res: any) => {
        if (res.Error) {
          const msg = res.Error.Message;
          message.error({ content: msg });
          return;
        }
        const item = res.Guard[0] || {};
        setCurrentGuardInfo(item);
      })
      .catch((err) => {
        const msg = err.msg || err.toString() || t('未知错误');
        message.error({ content: msg });
      });
  };

  // 获取播报订阅信息
  const getBroadcastSheetInfo = async () => {
    getBroadcastSheet({
      GuardId: guardId,
      ...(type === EENDTYPE.OPERATOR && { AppId: appId, BroadcastId: 0 }),
    })
      .then((res: any) => {
        if (res.Error) {
          const msg = res.Error.Message;
          message.error({ content: msg });
          return;
        }
        setBroadcastId(res.BroadcastSheet.BroadcastId);
        setBroadcastInfo(res.BroadcastSheet);
      })
      .catch((err) => {
        const msg = err.msg || err.toString() || t('未知错误');
        message.error({ content: msg });
      });
  };

  // 获取组合播报内容
  const getCombinedData = async () => {
    try {
      const res: any = await DescribeCombinedBroadcastConfigs({
        BroadcastId: broadcastId,
        ...(type === EENDTYPE.OPERATOR && { AppId: appId }),
      });
      if (res.Error) {
        message.error({ content: res.Error.Message });
      } else {
        setCombinedBroadcastConfig(res || {});
      }
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
    }
  };

  useEffect(() => {
    getBroadcastSheetInfo();
  }, []);
  useEffect(() => {
    if (type === EENDTYPE.CUSTOMER) {
      app.user.current()
        .then((res) => {
          setCustOperator(res.displayName);
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, []);

  useEffect(() => {
    if (!guardId) return;
    getGuardSheet();
  }, [guardId]);

  const isShowBaseInfoPanel = useMemo(() => !isEmpty(broadcastInfo), [broadcastInfo]);

  // 启用开关是否可用
  const statusDisable = useMemo(() => {
    const { Products, BroadcastConfig, Enable } = broadcastInfo;
    if (broadcastInfo?.Online === SOURCE_STATUS.SUCCESS) {
      return true;
    }
    // 如果其中有一个打开，就得判断产品是否选择实例
    if (Enable || combinedBroadcastConfig.Enable) {
      let result = true;
      // 如果打开常规播报，就要校验有没有选择策略
      if (Enable) {
        result = result && !isEmpty(BroadcastConfig);
      }
      if (!isEmpty(Products)) {
        const pickFinished = Products
          .filter(item => item.Product !== 'stream')
          .every(item => item.ResourceIds.length > 0);
        result = result && pickFinished;
      }
      return result;
    }
    // 如果两个都未打开，不允许启用
    return false;
  }, [broadcastInfo, combinedBroadcastConfig]);

  // 待补充信息预处理
  const resourcePending = useMemo(() => map(broadcastInfo?.Products, ({ Product, ResourceIds, Name, IsPending }) => ({
    Product,
    Name,
    IsPending,
    ResourceIds: map(ResourceIds, item => ({ InstanceId: item })),
  })), [broadcastInfo.Products]);

  // 点击下一步时自动保存
  const handleStepBtnClick = async (type) => {
    if (current === 'submit') {
      setCurrent(type === 'NEXT' ? next.id : prev.id);
      return;
    }
    if (type === 'NEXT') {
      setLoadingNext(true);
      setDisabledLast(true);
    } else {
      setLoadingLast(true);
      setDisabledNext(true);
    }
    // 子组件保存会返回结果 true / false
    const res = await saveRef.current.save();

    if (type === 'NEXT') {
      setLoadingNext(false);
      setDisabledLast(false);
    } else {
      setLoadingLast(false);
      setDisabledNext(false);
    }

    if (res) {
      setCurrent(type === 'NEXT' ? next.id : prev.id);
    }
  };

  // 记录信息是否改变
  const handleDetailChange = (status, type) => {
    const temp = merge(changeStatus, { [type]: status });
    setChangeStatus(temp);
  };

  // 初始化启用状态
  useEffect(() => {
    setSwitchValue(broadcastInfo.Online === STATUS.SUCCESS);
  }, [broadcastInfo.Online]);

  // 保存状态
  const handleStatusInfoSave = () => {
    ModifyBroadcastOnline({
      BroadcastId: broadcastInfo.BroadcastId,
      Online: switchValue ? 1 : 0,
      Updater: type === EENDTYPE.OPERATOR ? operator : custOperator,
      ...(type === EENDTYPE.OPERATOR && { AppId: appId }),
    })
      .then((res: any) => {
        if (res.Error) {
          const msg = res.Error.Message;
          message.error({ content: msg });
          return;
        }
        message.success({ content: t('提交保存成功') });
        originStore.dispatch(setDrawerVisibleMapAction({ broadcastVisible: false }));
      })
      .catch((err) => {
        const msg = err.msg || err.toString() || t('未知错误');
        message.error({ content: msg });
      });
  };

  return (
		<>
			<Stepper steps={steps} current={current} />
      {
        !!broadcastId
          ? <section className='broadcast-box'>
          <div style={{ position: 'relative' }}>
            {(() => {
              switch (steps[currentIndex]?.id) {
                case 'baseInfo':
                  return <>
                    {
                      isShowBaseInfoPanel
                        ? <BroadcastBasePanel
                            ref={saveRef}
                            guardInfo={currentGuardInfo}
                            broadcastInfo={broadcastInfo}
                            editable={isBroadcastEditabale}
                            operator={type === EENDTYPE.OPERATOR ? operator : custOperator}
                            onInfoChange={status => handleDetailChange(!status, 'ruleInfoStatus')}
                            enable={broadcastInfo.Enable}
                            reload={getBroadcastSheetInfo}
                          />
                        : <div style={{ marginTop: 200 }}>
                            <Empty />
                          </div>
                    }
                  </>;
                case 'strategyList':
                  return 	<StrategyListPanel
                          ref={saveRef}
                          products={currentGuardInfo.ProductDesc}
                          broadcastId={broadcastInfo.BroadcastId}
                          editable={isBroadcastEditabale}
                          operator={type === EENDTYPE.OPERATOR ? operator : custOperator}
                          selectedStrategy={broadcastInfo.BroadcastConfig}
                          onInfoChange={status => handleDetailChange(!status, 'broadStrategyStatus')}
                          reload={getBroadcastSheetInfo}
                          reloadC={getCombinedData}
                          GtsChatid={broadcastInfo.GtsChatid}
                          Enable={broadcastInfo.Enable}
                        />;
                case 'resourceSupple':
                  return 	<ResourceSupplePanel
                            ref={saveRef}
                            guardId={guardId}
                            broadcastId={broadcastInfo.BroadcastId}
                            resourcePending={resourcePending}
                            editable={isBroadcastEditabale}
                            operator={type === EENDTYPE.OPERATOR ? operator : custOperator}
                            onInfoChange={status => handleDetailChange(!status, 'pendingStatus')}
                            reload={getBroadcastSheetInfo}
                            online={broadcastInfo.Online}
                          />;
                case 'submit':
                  return <SubmitPanel
                          statusDisable={statusDisable}
                          strategyStatus={broadcastInfo.Online}
                          onSwitchChange={value => setSwitchValue(value)}
                        />;
                default: return <></>;
              }
            })()}
          </div>
          <div className='broadcast-opt'>
            <div className='broadcast-opt-box'>
              <Button disabled={!prev || disabledLast} loading={loadingLast} onClick={() => handleStepBtnClick('PREV')}>{t('上一步')}</Button>
              {
                next
                && <Button
                    type="primary"
                    disabled={!next || !current || disabledNext}
                    onClick={() => handleStepBtnClick('NEXT')}
                    style={{ marginLeft: 10 }}
                    loading={loadingNext}
                  >
                    {t('下一步')}
                </Button>
              }
              {
                isBroadcastEditabale && current === 'submit'
                && <>
                    <Button
                      type="primary"
                      onClick={handleStatusInfoSave}
                      style={{ marginLeft: 6 }}
                    >
                      {t('提交')}
                    </Button>
                </>
              }
            </div>
          </div>
        </section>
          : <div style={{ marginTop: 200 }}>
            <Status
              icon="loading"
              title={t('请稍候，需等待播报群创建成功')}
            />
        </div>
      }
		</>
  );
}
