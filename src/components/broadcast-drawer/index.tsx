/* eslint-disable no-nested-ternary */
import React from 'react';
import { t } from '@tea/app/i18n';
import {
  Drawer,
} from '@tencent/tea-component';
import store from '@src/origin-store/store';
import { setDrawerVisibleMapAction } from '@src/origin-store/guardAction';
import CustomerBroadcast from './components/customer-broadcast';
import { BroadcastEditor } from './components/broadcast-editor';
import './index.less';

/** Root
 * @param props
 * @returns React.ReactElement
 */
export default function BroadcastDrawer(): React.ReactElement {
  const { graphApi } = store.getState().guard;
  const isCONSOLE = graphApi.env === 'CONSOLE';

  return (
    <>
      <Drawer
        style={{ width: 1100 }}
        visible
        title={ isCONSOLE ? t('护航播报') : t('播报策略')}
        destroyOnClose
        outerClickClosable={false}
        className='monitor-drawer'
        onClose={() => {
          store.dispatch(setDrawerVisibleMapAction({ broadcastVisible: false }));
        }}
      >
        {
          isCONSOLE
            ? <CustomerBroadcast />
            : <BroadcastEditor />
        }
      </Drawer>
    </>
  );
}
