import { t } from '@tea/app/i18n';
export const STATUS = {
  SUCCESS: 1,
  FAIL: 0,
  PEEDDING: -1,
  TOADD: 2,
};

export const SOURCE_STATUS = {
  ERROR: 0,
  SUCCESS: 1,
  EMPTY: 2,
};

export const broadcastBaseLabel = [t('播报ID | 名称'), t('场景'), t('关联护航单'), t('APPID | 客户名称')];

export const typeOption = [
  {
    text: t('全部'),
    value: 'all',
  },
  {
    text: t('资源'),
    value: 'resource',
  },
  {
    text: t('护航资源总览'),
    value: 'overview',
  },
  {
    text: t('客户资源总览'),
    value: 'appid',
  },
];

export const subTypeOption = [
  {
    text: t('全部'),
    value: 'all',
  },
  {
    text: t('事件告警类型'),
    value: 'event',
  },
  {
    text: t('水位告警类型'),
    value: 'usage',
  },
];

export enum SourceType {
  BROADCAST_STRATEGY = 1,
  MONITOR_BROADCAST_CONFIG = 2,
}
