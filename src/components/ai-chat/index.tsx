/**
 * @description AIChat页面
 */
/* eslint-disable */
import React, { useEffect, useState, useRef } from 'react';
import {
  Bubble, TextArea,
} from '@tencent/tea-component';
import { ContentType, MessageType, MessageTypeEnum } from '@src/types/ai-chat';
import {
  BOTTOM_ITEM_ID, DEFAULT_MESSAGE, INPUT_MAX_LENGTH, MESSAGE_CONTAINER_ID, SQL_SIGN,
} from './contants';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { sleep } from '@src/utils/common';
import { useCommonSelector } from '@src/store/app-common';
import originStore from '@src/origin-store/store';
import { v4 as uuidv4 } from 'uuid';
import { isEmpty, find } from 'lodash';
import StopIconSvg from '@src/assets/svg/stop.svg';
import PlusIconSvg from '@src/assets/svg/plus-circle.svg';
import PlayIconSvg from '@src/assets/svg/chat-play.svg';
import MessageItem from './components/message-item';
import { scrollIntoView } from './utils';
import { t } from '@tea/app/i18n';
import './index.less';

const AIChat = () => {
  const { graphApi: apis }  = originStore.getState().guard;
  const { appId }  = useCommonSelector();
  const [inputValue, setInputValue] = useState('');
  const [messages, setMessages] = useState<MessageType[]>(DEFAULT_MESSAGE);
  const [loading, setLoading] = useState(false);
  const inputRef = useRef(null);
  const cancelRef = useRef(null);
  const currQuestionRef = useRef(-1);
  const [currQUestion, setCurrQuestion] = useState<string>('');
  const [historyQuestions, setHistoryQuestions] = useState<string[]>([]);
  const [needScroll, setNeedScroll] = useState<boolean>(true);
  const [sessionId, setSessionId] = useState<string>(uuidv4());

  const scrollRef = useRef(0);
  const chatIdRef = useRef('');
  
  // 把最新的消息展示出来
  useEffect(() => {
    if (!loading) {
      sleep(50).then(() => {
        bottomScroll();
      });
    } else {
      bottomScroll();
    }
  }, [messages, needScroll, loading]);

  // 允许大模型吐消息的时候滚动查看上面的消息
  useEffect(() => {
    // 监听id为MESSAGE_CONTAINER_ID的容器滚动事件
    const handleScroll = () => {
      const container = document.getElementById(MESSAGE_CONTAINER_ID);
      if (container) {
        const { scrollTop, scrollHeight, clientHeight } = container;
        if (scrollTop < scrollRef.current) {
          setNeedScroll(false);
        }
        if ((scrollTop + clientHeight + 0.5) >= scrollHeight) {
          setNeedScroll(true);
        }
        scrollRef.current = scrollTop;
      }
    };
    sleep(100).then(() => {
      const container = document.getElementById(MESSAGE_CONTAINER_ID);
      if (container) {
        container.addEventListener('scroll', handleScroll);
      }
    });

    return () => {
      const container = document.getElementById(MESSAGE_CONTAINER_ID);

      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  const bottomScroll = () => {
    const bottomItem = document.getElementById(BOTTOM_ITEM_ID);
    if (bottomItem && needScroll) {
      bottomItem?.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // 添加全局键盘事件监听，可以上下键翻动历史问题
  useEffect(() => {
    const handleGlobalKeyDown = (e) => {
      // 只有当输入框未聚焦时才处理上下箭头键
      if (inputRef.current?.matches(':focus')) {
        if (e.key === 'ArrowUp') {
          e.preventDefault();
          if (currQuestionRef.current > -1) {
            currQuestionRef.current = Math.max(currQuestionRef.current - 1, 0);
          }
          setInputValue(historyQuestions[currQuestionRef.current]);
        }
        if (e.key === 'ArrowDown') {
          e.preventDefault();
          if (currQuestionRef.current < historyQuestions.length) {
            currQuestionRef.current = Math.min(currQuestionRef.current + 1, historyQuestions.length - 1);
          }
          setInputValue(historyQuestions[currQuestionRef.current]);
        }
      }
    };

    window.addEventListener('keydown', handleGlobalKeyDown);

    return () => {
      window.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [historyQuestions]);

  // 处理sse异常
  const handleError = (str?: string, ctrl?: any) => {
    setLoading(false);
    setMessages((prev) => {
      const lastContent = prev[prev.length - 1].content;
      return [...(prev.slice(0, prev.length - 1)), {
        type: MessageTypeEnum.assistant,
        content: `${lastContent}
${(str || t('查询失败,请稍后再试'))}
`,
        contentType: ContentType.text,
        id: chatIdRef.current,
      }];
    });
    ctrl?.abort?.();
  };

  // 发送消息前对messages预处理
  const handleInitSendMessage = (v: string, options?: {
    addLast?: boolean; // 是否追加，处理查询sql异常场景
    error?: string; // 报错信息
  }) => {
    const { addLast = false } = options || {};
    // 正常消息
    if (!addLast) {
      setHistoryQuestions(prev => [...prev, v]);
      currQuestionRef.current = historyQuestions.length + 1;
      setMessages(prev => [...prev, {
        type: MessageTypeEnum.user,
        content: v,
        contentType: ContentType.text,
        id: uuidv4(),
      }, {
        id: uuidv4(),
        type: MessageTypeEnum.assistant,
        content: t('正在为您查询，请稍等...'),
        contentType: ContentType.text,
      }]);
    // 处理报错重试
    } else {
      setMessages((prev) => {
        // 获取处理过的最后一条数据
        const currLastMessage = prev[prev.length - 1].content.replaceAll(SQL_SIGN, `
        \`\`\`
        `);
        // 整理最后一条消息
        const showLastMessage = `${currLastMessage}`;
        return [...(prev.slice(0, prev.length - 1)), {
          type: MessageTypeEnum.assistant,
          content: showLastMessage,
          contentType: ContentType.text,
          id: prev[prev.length - 1].id,
        }];
      });
    }
  };

  const handleEventStep = (data, ctrl) => {
    try {
      const resData = JSON.parse(data);
      const {
        step,
        result,
      } = resData;

      setMessages((prev) => {
        const stepItem = find(prev[prev.length - 1]?.steps, (item) => item.stepName === step) || {}
        if (isEmpty(stepItem)) {
          return [...(prev.slice(0, prev.length - 1)), {
            type: MessageTypeEnum.assistant,
            content: prev[prev.length - 1].content,
            contentType: ContentType.text,
            id: chatIdRef.current,
            steps: (prev[prev.length - 1]?.steps ?? []).concat({stepName: step, stepContent: JSON.stringify(result),}),
          }];
        }
        const newSteps = prev[prev.length - 1]?.steps?.map((item) => {
          if (item.stepName === step) {
            return {
              stepName: step,
              stepContent: item?.stepContent + (JSON.stringify(result) ?? ''),
            };
          }
          return item;
        });
        return [...(prev.slice(0, prev.length - 1)), {
          type: MessageTypeEnum.assistant,
          content: prev[prev.length - 1].content,
          contentType: ContentType.text,
          id: chatIdRef.current,
          steps: newSteps,
        }];
      });
    } catch (e) {
      handleError(e.message, ctrl);
      console.error('json error', e);
    }
  };

  // 处理接收到的消息
  const handleReciveMessage = (params: {
    rsp: {data: string; event?: string};
    ctrl: any;
    addLast?: boolean;
  }) => {
    const { rsp, ctrl, addLast } = params;
    if (!rsp?.data) {
      console.log('收到心跳:', rsp);
      return;
    }

    // 运营端的event消息特殊处理
    // const isAdminChatId = rsp?.data.startsWith('event:') && !addLast;
    // if (isAdminChatId) {
    //   chatIdRef.current = rsp?.data.replace('event:', '');
    //   return;
    // }
    // 数据总是以data:开头，所以要去掉前五位
    try {
      const {data: rsData, event: rsEvent} = JSON.parse(rsp?.data);
      switch (rsEvent) {
        case 'step':
          handleEventStep(rsData, ctrl);
          break;
        case 'message':
          setMessages(prev => [...(prev.slice(0, prev.length - 1)), {
            type: MessageTypeEnum.assistant,
            content: prev[prev.length - 1].content + (rsData ?? ''),
            contentType: ContentType.text,
            id: chatIdRef.current,
            steps: prev[prev.length - 1]?.steps,
          }]);
          break;
        case 'end':
          setLoading(false);
          ctrl.abort();
          break;
        default:
          console.warn('Unknown event type:');
      }
      // const {
      //   content,
      //   is_final: isFInal, // 结束标识
      //   recommends, // 推荐的问题
      //   reasoning_content: reasoningContent, // 推理内容
      //   request_id: requestId, // 请求id
      //   Response: response,
      // } = data;

      // setMessages(prev => [...(prev.slice(0, prev.length - 1)), {
      //   type: MessageTypeEnum.assistant,
      //   content: prev[prev.length - 1].content + (content ?? ''),
      //   contentType: ContentType.text,
      //   id: chatIdRef.current,
      //   reasoningContent: (prev[prev.length - 1].reasoningContent ?? '') + (reasoningContent ?? ''),
      //   requestId,
      // }]);

      // if (isFInal) {
      //   setLoading(false);
      //   ctrl.abort();
      //   setMessages((prev) => {
      //     const lastMessage = prev[prev.length - 1];
      //     return [...(prev.slice(0, prev.length - 1)), {
      //       type: MessageTypeEnum.assistant,
      //       content: lastMessage,
      //       contentType: ContentType.text,
      //       id: chatIdRef.current,
      //       questions: recommends,
      //       reasoningContent: prev[prev.length - 1].reasoningContent,
      //       requestId,
      //     }];
      //   });
      // }
    } catch (e) {
      handleError(e.message, ctrl);
      console.error('json error', e);
    }
  };

  const handlKeyDown = (e) => {
    if (e.key === 'Enter') {
      // Shift+回车：允许换行
      if (e.shiftKey) return;

      // 普通回车：阻止默认行为并触发发送
      e.preventDefault();
      handleSendMessage?.(inputValue);
    }
  }

  // 处理发送消核心逻辑
  const handleSendMessage = (v: string, options?: {
    addLast?: boolean; // 是否追加，处理查询sql异常场景
    error?: string; // 报错信息
  }) => {
    if (loading || !v) return;
    setCurrQuestion(v);
    setLoading(true);
    const { addLast = false } = options || {};
    setInputValue('');
    handleInitSendMessage(v, options);
    scrollIntoView();
    setNeedScroll(true);
    // 控制取消请求
    const ctrl = new AbortController();
    cancelRef.current = ctrl;
    //     const question = error ? `${v}？
    // 这个问题的sql查询数据失败，错误信息是：
    // ${error}
    // 分析异常重新查询一次` : v;
    const question = v;
    const params = {
      AppId: appId,
      User: apis?.userName,
      SessionId: sessionId,
      // OutputType: 'append',
      Message: question,
      // Uin: apis?.uin,
      // Action: 'SseAPI',
    };
    const url = 'http://11.141.10.186/template/stream';
    const method = 'post';
    const headers = {
      'Content-Type': 'application/json',
    };
    const body = JSON.stringify(params);
    inputRef.current?.focus();

    fetchEventSource(url, {
      method,
      headers,
      body,
      credentials: 'include',
      openWhenHidden: true,
      signal: ctrl.signal,
      // 必须设置，否则出现异常无法终止
      onopen(res): any {
        if (res.status !== 200) {
          handleError(t('网络异常'), ctrl);
        } else if (!addLast) {
          setMessages(prev => [...(prev.slice(0, prev.length - 1)), {
            type: MessageTypeEnum.assistant,
            content: '',
            contentType: ContentType.text,
            id: prev[prev.length - 1].id,
          }]);
        }
      },
      onmessage(rsp) {
        handleReciveMessage({
          rsp,
          ctrl,
          addLast,
        });
      },
      onerror(e) {
        handleError(e.message, ctrl);
        console.error('sse error', e, ctrl);
      },
    });
  };

  const stopMessage = () => {
    if (!loading) return;
    cancelRef.current?.abort();
    setLoading(false);
    sleep(50).then(() => {
      scrollIntoView();
    });
  };

  const handleNewSessionCreate = () => {
    setSessionId(uuidv4());
    setMessages(DEFAULT_MESSAGE);
  }
  return <div className="chat-container">
    
    <div className='chat-messages' id={MESSAGE_CONTAINER_ID}>
      {
        messages?.map((item, ind) => <MessageItem
          loading={loading}
          item={item}
          key={item?.id}
          handleSendMessage={handleSendMessage}
          isLatest={ind === messages.length - 1}
          isFirst={ind === 0}
          question={currQUestion}
          apis={apis}
        />)
      }
      <div id={BOTTOM_ITEM_ID} />
    </div>
    <div className='chat-bottom'>
      <div className='chat-input'>
        <TextArea
          maxLength={INPUT_MAX_LENGTH}
          ref={inputRef}
          disabled={loading}
          value={inputValue}
          onChange={(v) => {
            setInputValue(v);
          }}
          className='chat-input-area'
          placeholder={t('请输入问题，回车键发送，shift+enter换行')}
          autoFocus
          onKeyDown={e => handlKeyDown(e)}
          // onPressEnter={v => handleSendMessage(v)}
        />
        <div className='chat-tool'>
          <Bubble content={t('新建会话')} tooltip dark>
            <div className='chat-tool-left' onClick={handleNewSessionCreate}>
              <img src={PlusIconSvg} alt="add" />
            </div>
          </Bubble>
          {
            loading
              ? < Bubble content={t('终止当前会话')} tooltip dark>
              <div className='chat-tool-right' onClick={stopMessage}>
                <img src={StopIconSvg} alt="stop" />
              </div>
            </Bubble>
              : < Bubble content={t('发送')} tooltip dark>
                <div className='chat-tool-right' onClick={() => handleSendMessage(inputValue)}>
                  <img src={PlayIconSvg} alt="play" />
                </div>
              </Bubble>
          }
        </div>
      </div>
    </div>
  </div>;
};
export default AIChat;
