/* eslint-disable */
import { MessageTypeEnum, ContentType } from '@src/types/ai-chat';;
export const BOTTOM_ITEM_ID = 'bottom-item';

export const SQL_SIGN = '__sql__';
export const TITLE_SIGN = '__title__';
export const CHART_TYPE_SIGN = '__chartType__';

export const DEFAULT_QUESTION = '了解我能做什么';
export const DEFAULT_MESSAGE = [{
  content: `您好！我是您的​​智能护航播报助手​​，专注于基于现有数据快速生成定制化分析数据。

​​我能为您做什么？​​
* 灵活定制​​：基于自然语言，我将自动生成您所需的数据形态与可视化配置。
* 精准适配​​：依托基础指标库与架构图，可细化需求描述，确保结果贴合业务场景。
* 高效交付​​：从数据整合到展示，全程自动化，助您迅速获取决策支持信息。

用专业与效率，为您的数据需求护航！`,
  type: MessageTypeEnum.assistant,
  contentType: ContentType.text,
}];

export const INPUT_MAX_LENGTH = 512;

export const MESSAGE_CONTAINER_ID = 'advisor-message-container';

export const NO_DATA_TIP = '当前条件查询暂无数据，建议修改查询条件重新查询';

export const TEMPLATE_MOCK = '模板内容生成'