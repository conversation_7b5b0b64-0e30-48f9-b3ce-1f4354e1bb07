/* eslint-disable  */
import React, { useState, useMemo } from 'react';
import './index.less';
import {
  LightbulbIcon,
} from 'tdesign-icons-react';
import { isEmpty } from 'lodash';
import { Collapse, Timeline } from 'tdesign-react';
import { Icon } from '@tencent/tea-component';

interface IProps {
  messageItem: any; 
}
const { Panel } = Collapse;
const CollapseComponent = ({messageItem}: IProps) => {
  if (isEmpty(messageItem?.steps)) return <></>
  return <div className='chat-bot-item'>
      <Collapse
        borderless
        defaultExpandAll
        defaultValue={['default']}
        expandIcon
        expandIconPlacement="right"
        expandOnRowClick
      >
        <Panel 
          header={<>
            <LightbulbIcon />
            <span>深度思索</span>
          </>}>
          <Timeline mode="same">
            {
              messageItem?.steps?.map((step: any, index: number) => {
                return <Timeline.Item key={index} className='time-line-collapse'>
                  <Collapse
                    borderless
                    defaultValue={['0']}
                    expandIcon
                    defaultExpandAll
                    expandIconPlacement="right"
                    expandOnRowClick
                  >
                    <Panel header={step?.stepName}>
                      {step?.stepContent}
                    </Panel>
                  </Collapse>
              </Timeline.Item>
              })
            }
          </Timeline>
        </Panel>
      </Collapse>
    </div>;
};

export default CollapseComponent;
