/* eslint-disable no-param-reassign */
/* eslint-disable no-nested-ternary */
/* eslint-disable */
import {
  Icon, TabPanel, Card, Tabs, Bubble,
} from '@tencent/tea-component';
import React, { useEffect, useMemo, useState } from 'react';
import { find, isEmpty } from 'lodash';
import { Tag, Button } from 'tdesign-react';
import { TextboxIcon, ChartLineData1Icon, CheckIcon } from 'tdesign-icons-react';
import { TEMPLATE_MOCK } from '../../contants';
import './index.less';
import { sleep } from '@src/utils/common';


const ChartRender = (props) => {
  const {
    messageItem, width, height, addon,
  } = props;
  const [active, setActive] = useState('perview');
  const [isSubmit, setIsSubmit] = useState(false);
  const [isloading, setIsloading] = useState(false);

  const template = find(messageItem?.steps, item => item.stepName === TEMPLATE_MOCK) || {};
  
  // 核心转换函数
  const parseSpecialTags = (input) => {
    // 同时匹配两种标签格式：$[...] 和 $(...)
    const parts = input.split(/(\$\[.*?\]|\$\(.*?\))/g);
  
    return parts.map((part, index) => {
      // 匹配方括号标签 $[...]
      const bracketMatch = part.match(/^\$\[(.*)\]$/);
      // 匹配圆括号标签 $(...)
      const parenMatch = part.match(/^\$\((.*)\)$/);
  
      if (bracketMatch) {
        return (
          <Tag theme="success" variant="outline" key={`bracket-${index}`} >
            {bracketMatch[1]}
          </Tag>
        );
      }
      if (parenMatch) {
        return (
          <Tag theme="danger" variant="light" key={`paren-${index}`} className="dollar-paren-tag">
            {parenMatch[1]}
          </Tag>
        );
      }
      return <React.Fragment key={`text-${index}`}>{part}</React.Fragment>;
    });
  };

  const tabs = [
    { id: 'perview', label: <TextboxIcon size="large" /> },
    { id: 'chart', label: <ChartLineData1Icon size="large"/> },
  ];

  const perviewContent = useMemo(() => {
    if (isEmpty(template)) return '';
    try {
      const result = JSON.parse(template.stepContent);
      return result?.index_mock_result
    } catch (e) {
      console.error('json error', e);
    }
  }, [template])
  
  const handleSubmitClick = () => {
    setIsloading(true);
    sleep(500).then(() => {
      setIsloading(false);
      setIsSubmit(true);
    });
  }
  return !isEmpty(template)
    ? (
      <Card
        className="charts-card"
        style={{ marginTop: 10, width: width ?? '', height: height ?? '' }}
      >
        <Tabs
          tabs={tabs}
          destroyInactiveTabPanel={false}
          // defaultActiveId={dimensionsLength > 1 ? 'line' : 'table'}
          addon={addon}
          activeId={active}
          onActive={tab => setActive(tab.id)}
        >
          {
              tabs.map((tab) => {
                const { id } = tab;
                if (id === 'chart') {
                  return (
                    <TabPanel id={id} key={id}>
                      <Card.Body>
                        {'XXXX'}
                      </Card.Body>
                    </TabPanel>);
                }

                return (
                  <TabPanel id={id} key={id}>
                    <Card.Body>
                      <div className='charts-card-perview'>
                        {parseSpecialTags(perviewContent)}
                      </div>
                      <div className='charts-card-perview-oprate'>
                        <Button
                          size='small'
                          icon={isSubmit ? <CheckIcon /> : <></>}
                          variant="base"
                          onClick={handleSubmitClick}
                          loading={isloading}
                          disabled={isSubmit}
                        >
                          {
                            isSubmit
                              ? '已确认'
                              : '确认'
                          }
                        </Button>
                      </div>
                      
                    </Card.Body>
                  </TabPanel>
                );
              })
            }
        </Tabs>
      </Card>)
    : <></>;
};

export default ChartRender;
