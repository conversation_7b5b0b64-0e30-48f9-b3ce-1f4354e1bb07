/* eslint-disable */
import { ContentType, MessageType, MessageTypeEnum } from '@src/types/ai-chat';
import React, {
  useRef, useState,
} from 'react';
import CollapseRender from '@src/components/ai-chat/components/collapse-render';
import ContentRender from '@src/components/ai-chat/components/content-render';
import ChartRender from '@src/components/ai-chat/components/chart-render';
import {
  CHART_TYPE_SIGN,
  SQL_SIGN, TITLE_SIGN,
} from '@src/components/ai-chat/contants';
// import {
//   extractSqlTags,
// } from '../../utils';
// import CollapseRender from '../collapse-render';
// import ChartRender from '../chart-render';
// import OperationsRender from '../operations-render';
import { t } from '@tea/app/i18n';
import './index.less';

interface MessageItemProps {
  item: MessageType;
  handleSendMessage: (message: string, options?: {
    addLast?: boolean;
    error?: string;
  }) => void;
  loading?: boolean;
  isLatest?: boolean;
  isFirst?: boolean;
  sessionId?: string;
  question?: string;
  selectedNode?: string;
  apis: AppPluginAPI.PluginAPI;
  model?: string;
}

// 单个消息组件
const MessageItem = (props: MessageItemProps) => {
  const {
    item, loading, isLatest, isFirst, apis,
  } = props;
  const isUser = item.type === MessageTypeEnum.user;
  // const isIframe = item.contentType === ContentType.iframeUrl;
  // const isChart = item.contentType === ContentType.chart;
  // const sqlRef = useRef('');
  // const titleRef = useRef('');
  // const chartTypeRef = useRef('line');
  // const { questions } = item;

  const [showDetail, setShowDetail] = useState(true); // 是否折叠，展示回答
  const [showProcessDetail, setShowProcessDetail] = useState(true); // 是否折叠，展示回答

  // useEffect(() => {
  //   if (!isUser && !sqlLoading && questions?.length > 0) {
  //     sleep(50).then(() => {
  //       scrollIntoView();
  //     });
  //   }
  // }, [sqlLoading]);

  // 推理完成后获取图表数据
  // useEffect(() => {
  //   if (!loading && isLatest && !isFirst) {
  //     setShowProcessDetail(false);
  //     const currSql = sqlRef.current;
  //     setSql(currSql);
  //     sqlRef.current = '';
  //     if (currSql) {
  //       setSqlLoading(true);
  //       getEchartData({
  //         isConsole,
  //         apiParams: {
  //           Action: 'GetChartDataSet',
  //           DataSQL: btoa(unescape(encodeURIComponent(currSql))),
  //           SessionId: String(sessionId),
  //           AppId: apis?.appId,
  //           ArchId: apis?.archInfo?.archId,
  //           NodeUUID: node,
  //           ChatId: item.requestId,
  //         },
  //       }).then((d) => {
  //         // 处理异常
  //         if ((d as any)?.Error || !d) {
  //           setFinish(false);
  //           retryRef.current += 1;
  //           if (retryRef.current > 2) {
  //             setChartData({});
  //             setShowDetail(true);
  //             setFinish(true);
  //             return;
  //           }
  //           setChartData({});
  //           setShowDetail(true);
  //           handleSendMessage(question, {
  //             addLast: true,
  //             error: (d as any)?.Error?.Message,
  //           });
  //         } else {
  //           try {
  //             if (d) {
  //               const data = JSON.parse(d?.DataSet);
  //               setChartData(data);
  //               const options = getOptions(chartTypeRef.current, data, titleRef.current);
  //               setOptions(options);
  //               setShowDetail(false);
  //               if (d?.Warning) {
  //                 setDataLimitTip(d?.Warning);
  //               }
  //             }
  //           } catch (e) {
  //             console.error(e);
  //             setChartData({});
  //             setShowDetail(true);
  //           }
  //           setFinish(true);
  //         }
  //       })
  //         .catch((e) => {
  //           console.error(e);
  //           setChartData({});
  //         })
  //         .finally(() => {
  //           setSqlLoading(false);
  //         });
  //     } else {
  //       setFinish(true);
  //       setShowDetail(true);
  //       sqlRef.current = '';
  //     }
  //   }
  // }, [loading, isLatest]);

  // 预处理文本，主要做标签内容提取与文本替换
  //   const handleContentText = (content: string) => {
  //     // 获取sql
  //     const { extracted } = extractSqlTags(item.content, SQL_SIGN);
  //     if (extracted.length > 0) {
  //       sqlRef.current = extracted[extracted.length - 1];
  //     }

  //     // 获取标题后删除标签内容
  //     const { extracted: title, remaining } = extractSqlTags(content, TITLE_SIGN);
  //     titleRef.current = currNodeInfo.name ? `${currNodeInfo.name}：${title[0]}` : title[0];

  //     // 获取chartType后删除标签内容
  //     const { extracted: chartType, remaining: cr } = extractSqlTags(remaining, CHART_TYPE_SIGN);
  //     chartTypeRef.current = chartType[0] ?? 'line';

  //     const reasoningContent = item?.reasoningContent;
  //     const show = cr?.replaceAll(SQL_SIGN, `
  // \`\`\`
  // `);
  //     return {
  //       show,
  //       reasoningContent,
  //     };
  //   };

  const renderContent = (item: MessageType) => {
    switch (item.contentType) {
      case ContentType.text: {
        if (isUser) {
          return <div className='chat-item-user-message'>{item.content}</div>;
        }

        // const { show, reasoningContent } = handleContentText(item.content);
        return (
          <div>
            <CollapseRender messageItem={item} />
            <ContentRender messageItem={item} />
            <ChartRender messageItem={item} />
            {/* {
              showChart && <ChartRender
                chartData={chartData}
                sql={sql}
                changeLoading={changeLoading}
                options={options}
              />
            } */}
            {/* {
              sqlLoading && <Icon type="loading" style={{ marginLeft: 10, marginTop: 10 }} />
            }
            {
              chartData && !Object.keys(chartData).length && finish && <div className={s['no-data']}>{NO_DATA_TIP}</div>
            }
            {
              dataLimitTip && <div className={s['no-data']}>{dataLimitTip}</div>
            } */}
            {/* {
              !isFirst && finish && <OperationsRender
                currNodeInfo={currNodeInfo}
                apis={apis}
                showChart={showChart}
                handleChangeCurrChart={handleChangeCurrChart}
                sql={sql}
                options={options}
                handleRefresh={handleRefresh}
                chatId={item.id}
                requestId={item.requestId}
              />
            } */}
            {/* {
              showChangeInput && showChart && (
                <div className={s['change-input']}>
                  <Input
                    maxLength={INPUT_MAX_LENGTH}
                    placeholder={CHANGE_INPUT_TIP}
                    className={s.input}
                    autoFocus
                    value={changeInputValue}
                    onChange={setChangeInputValue}
                    onPressEnter={handleSendChangeMessage}
                    disabled={changeLoading}
                  />
                  <Icon type="close" className={s.icon} onClick={() => setShowChangeInput(false)} />
                </div>
              )
            } */}
          </div>);
      }
      // case ContentType.iframeUrl:
      //   return <iframe src={item.content} title="title" width="100%" height="100%" />;
      // case ContentType.chart:
      //   return <ReactEcharts
      //     option={item.content}
      //     style={{ height: '100%', width: '100%' }}
      //   />;
      case ContentType.default:
        return <div>
          {item?.content?.message ?? t('正在处理中...')}
               </div>;
      default:
        return null;
    }
  };

  return <div className='chat-item'>
    <div
      className={`chat-item-content ${!isUser ? 'chat-bot' : 'chat-user'}`}
    >
      {renderContent(item)}
    </div>
  </div>;
};

export default MessageItem;
