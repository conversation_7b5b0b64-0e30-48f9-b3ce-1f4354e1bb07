.chat-item {
  position: relative;
  width: 100%;

  .no-data {
    margin-top: 5px;
    color: #ff7200;
    font-weight: 500;
  }

  .recommend-title {
    margin-top: 10px;
    font-size: 14px;
    font-weight: 600;
  }


  .chat-item-content {
    width: fit-content;
    max-width: 90%;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0 4Px 24Px 0 rgba(41,91,156,.05);
    // white-space: pre-wrap;
    // word-break: break-all;
  }

  .questions {
    margin-top: 10px;
    font-weight: 600;

    .question {
      width: fit-content;
      color: #006ef9;
      cursor: pointer;
      line-height: 20px;
    }
  }

  .analysis {
    padding: 10px;
    border: 1px solid #f1eaea;
    border-radius: 5px;
    background-color: #fff;
    color: #000;
  }

  .collapse {
    display: flex;
    justify-content: space-between;
    cursor: pointer;
    font-weight: 600;

    .icon {
      cursor: pointer;
    }

    .title {
      color: black;
      font-size: 14px;
    }
  }

  .change-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 6px;
    gap: 10px;

    .input {
      flex: 1;
      border-radius: 10px;
    }

    .icon {
      flex-basis: 15px;
      cursor: pointer;
    }
  }
}

.chat-bot {
  background-color: #fff;
  border-radius: 8px;
  width: 100% !important;

  // &::after {
  //   position: absolute;
  //   top: 15px;
  //   left: -14px;
  //   width: 0;
  //   height: 0;
  //   border: 5px solid rgb(151, 242, 160);
  //   border-radius: 50%;
  //   content: '';
  // }
}

.chat-user {
  background-color: #006ef9;
  color: #fff;
  float: right;
}

.chat-item-user-message {
  white-space: pre-wrap;
}

:global {
  /* 使用 fixed 定位动画中移动的元素 */
  .animated-button {
    position: fixed;
    z-index: 1000;
    z-index: 10000;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #e74c3c;
    pointer-events: none;
  }
}