
.chat-container {
  position: relative;
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  align-content: space-between;
  margin: auto;
  gap: 10px;
  user-select: text;

  .choice {
    width: 100%;
  }

  .chat-messages {
    position: relative;
    display: flex;
    overflow: auto;
    width: 100%;
    min-height: 600px;
    box-sizing: border-box;
    flex: 1;
    flex-direction: column;
    padding: 10px;
    padding-left: 20px;
    border-radius: 10px;
    background-color: #fff;
    gap: 10px;
    border: 1px solid rgba(0, 0, 0, .08);
    box-shadow: 0 6Px 6Px 0 rgba(0,0,0,.04),0 8Px 24Px 0 rgba(0,0,0,.02);
    background-color: #f2f8ff;
  }

  .chat-bottom {
    display: flex;
    width: 100%;
    flex-direction: column;

    .chat-input {
      position: relative;
      display: flex;
      height: fit-content;
      // flex: 1;
      // align-items: center;
      justify-content: center;
      border: 1px solid rgba(0, 0, 0, .08);
      border-radius: 10px;
      background-color: #fff;
      box-shadow: 0 6Px 6Px 0 rgba(0,0,0,.04),0 8Px 24Px 0 rgba(0,0,0,.02);

      .chat-input-area {
        width: 100%;
        height: fit-content;
        min-height: 100px;
        border: 0;
        border-radius: 10px;
      }

      .chat-tool {
        position: absolute;
        right: 10px;
        bottom: 5px;
        display: flex;

        .chat-tool-left,
        .chat-tool-right {
          cursor: pointer;

          &:hover {
            background-color: #eae7e7;
          }
        }

        .chat-tool-left {
          margin-right: 10px;
        }
        
      }
    }
  }
}
