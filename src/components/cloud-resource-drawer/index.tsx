/* eslint-disable no-nested-ternary */
import React, { useState } from 'react';
import { t, Trans } from '@tea/app/i18n';
import { Space } from 'tdesign-react';
import {
  message,
  Modal,
  Drawer,
  Button,
  Bubble,
} from '@tencent/tea-component';
import store from '@src/origin-store/store';
import { getStatu } from '@src/utils/common';
import { EENDTYPE } from '@src/constants';
// import EscortTool from '@src/pages/escortTool';
import { guardInfo, setGuardInfo } from '@src/utils/caching';
import { setDrawerVisibleMapAction, setGuardUpdateFlagAction } from '@src/origin-store/guardAction';
import {
  ModifyGuardAfterSaleConfirmStatus,
  DescribeOtherPlatformGuardSheet,
} from '@src/service/api/baseInfo';
import GuardStatusStep from './components/guard-status-step';
// import { nanoid } from 'nanoid';
// import { useDispatch } from 'react-redux';
import { useCommonSelector } from '@src/store/app-common';
import './index.less';

/** Root
 * @param props
 * @returns React.ReactElement
 */
export default function CloudResourceDrawer(): React.ReactElement {
  const { archProductInfo } = useCommonSelector();
  const { graphApi, drawerUpdateFlag } = store.getState().guard;
  const [modalVisible, setModalVisible] = useState(false);
  const [isloading, setIsloading] = useState(false);
  const operator = localStorage.getItem('engName');
  const {
    env, archInfo,
  } = graphApi;
  const { Supporter: supporter } = guardInfo?.Approvals?.AfterSalesStatus;
  const queryParams = new URLSearchParams(location.search);
  const appId = +queryParams.get('appid');

  const isDisabled = supporter !== operator;
  const type = {
    CONSOLE: EENDTYPE.CUSTOMER,
    ISA: EENDTYPE.OPERATOR,
  }[env];

  const handleMapResourceSubmit = () => {
    if (archProductInfo.HasAddedInstance) {
      setModalVisible(true);
      return;
    }
    confirmCloudMapResource();
  };

  const updateGuardInfo = () => {
    DescribeOtherPlatformGuardSheet({ Filters: [{ Name: 'platform_unique_id', Values: [archInfo?.archId] }] })
      .then((res: any) => {
        // 最后一条护航单
        const guard = res.Guard?.length ? res.Guard[0] : {};
        // 如果最后一条已经护航完成，则不缓存
        setGuardInfo(getStatu(guard) === 'finish' ? {} : guard);
        store.dispatch(setGuardUpdateFlagAction(!drawerUpdateFlag));
        // graphApi.setSlotComponent(<EscortTool uuid={nanoid()} />);
      });
  };

  const confirmCloudMapResource = async () => {
    setModalVisible(false);
    setIsloading(true);
    try {
      const res: any = await ModifyGuardAfterSaleConfirmStatus({
        GuardId: guardInfo.GuardId,
        OperateType: 1, // 确认
        Operator: operator,
        ...(type === EENDTYPE.OPERATOR && { AppId: appId }),
      });
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        return false;
      }
      message.success({ content: t('确认成功') });
      updateGuardInfo();
      setIsloading(false);
      store.dispatch(setDrawerVisibleMapAction({ cloudResourceVisible: false }));
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
      setIsloading(false);
    }
  };

  return (
    <>
      <Drawer
        style={{ width: 920 }}
        visible
        title={t('售后确认架构图-资源新增')}
        destroyOnClose
        outerClickClosable={false}
        className='cloud-resource-drawer'
        footer={
          <div className='cloud-resource-drawer-footer'>
            <Bubble
              arrowPointAtCenter
              placement="auto"
              trigger="hover"
              dark
              content={isDisabled ? t('只支持护航负责人可操作') : ''}
            >
              <Button
                type="primary"
                onClick={handleMapResourceSubmit}
                loading={isloading}
                disabled={isDisabled}
              >
                {t('下一步')}
              </Button>
            </Bubble>
          </div>
        }
        onClose={() => {
          store.dispatch(setDrawerVisibleMapAction({ cloudResourceVisible: false }));
        }}
      >
        <GuardStatusStep />
      </Drawer>
      <Modal
        visible={modalVisible}
        size="s"
        caption={t('是否确认提交？')}
        onClose={() => setModalVisible(false)}
      >
        <Modal.Body>
          <p className='cloud-resource-warning-title'><Trans>
            部分资源未绑定到架构图，将
            <span>无法支持：</span>
          </Trans></p>
          <ul className='cloud-resource-warning-list'>
            <li>{t('架构图节点监控')}</li>
            <li>{t('异常播报汇总及展示到架构图节点')}</li>
            <li>{t('业务健康告警展示到架构图节点')}</li>
            <li>{t('护航架构巡检')}</li>
          </ul>
          <p className='cloud-resource-warning-bd'>{t('*客户将无法在租户端架构图看到这部分资源')}</p>
        </Modal.Body>
        <Modal.Footer>
          <div className='resource-warning-footer'>
            <Space>
              <Button type='primary' onClick={confirmCloudMapResource}>{t('确认提交')}</Button>
              <Button onClick={() => setModalVisible(false)}>{t('取消')}</Button>
            </Space>
          </div>
        </Modal.Footer>
      </Modal>
    </>
  );
}
