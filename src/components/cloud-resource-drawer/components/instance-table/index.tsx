import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Card, Table,  message, StatusTip, Row, Col, Text } from '@tencent/tea-component';
import { DescribeGuardAddedInstance } from '@src/service/api/baseInfo';

import { t } from '@tea/app/i18n';
const { scrollable, pageable, autotip } = Table.addons;

interface Props {
  product: string,
  appid?: number,
  guardId?: number,
  instancePolicy: any,
}

// 根据路径去找值
const findValueByPath = (obj, path) => {
  try {
    const value = path.split('.').reduce((acc, key) => acc?.[key], obj);
    // 如果值是数组，返回逗号拼接的字符串.如果值是字符串，返回本身
    return Array.isArray(value) ? value.join(',') : value;
  } catch (error) {
    return '--'; // 如果发生错误，返回--
  }
};

function InstanceTable({ appid, product, guardId, instancePolicy }: Props, ref) {
  const [total, setTotal] = useState(0);
  const [pageIndex, setPageIndex] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [records, setRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  // 实例列
  const [columnData, setColumnData] = useState<any>();

  useEffect(() => {
    getProductInstances({ pageIndex: 1, pageSize });
  }, []);

  // 获取选择的产品实例
  async function getProductInstances({ pageIndex = 1, pageSize = 10 } = {}) {
    try {
      setLoading(true);
      const params = {
        AppId: appid,
        GuardId: guardId,
        Limit: pageSize,
        Offset: (pageIndex - 1) * pageSize,
        Products: [product],
      };
      // 记录当前页
      setPageIndex(pageIndex);
      setPageSize(pageSize);
      const res: any = await DescribeGuardAddedInstance(params);
      if (res.Error) {
        setRecords([]);
        setLoading(false);
        const msg = res.Error.Message;
        message.error({ content: msg });
        return;
      }
      setRecords([...res.Instance]);
      setTotal(res.TotalCount);
      setLoading(false);
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
    }
  }

  // 获取实例展示列
  const getColumns = async () => {
    const columnData = (instancePolicy || []).filter(i => i.Product === product);
    const columns: any = columnData.length ? columnData[0].FilterInfo.filter(i => i.Uses === 'Right') : [];
    // 最终的展示列
    const result: any = columns.map((i, index) => ({
      key: index,
      header: i.FilterName,
      width: i.FilterCorrespondName === 'InstanceId' ? 150 : 'auto',
      render: (ins) => {
        let value: any = '';
        if (i.DataFrom === 'Info') {
          if (i.FilterCorrespondName === 'InstanceId') {
            value = <Text copyable overflow tooltip>{ins.InstanceId}</Text>;
          } else {
            value = ins[i.FilterCorrespondName];
          }
        } else if (i.DataFrom === 'Extra') {
          const extraData = ins.Extra ? JSON.parse(ins.Extra) : {};
          value = findValueByPath(extraData, i.Location) || '';
        } else if (i.DataFrom === 'Policy') {
          const policy = ins.Policy.filter(j => j.MetricName === i.Location)[0];
          value = policy ? (policy.Type === 'Other' ? (policy.OtherValue || t('未填写')) : policy.Value) : '';
        }
        // 根据映射展示对应的值
        if (value !== '' && i.ExtraShowName?.length) {
          const list = String(value).split(';');
          const result = [];
          list.map((val) => {
            const colValueItem = i.ExtraShowName.filter(i => i.Key == val);
            const tempValue = colValueItem.length ? colValueItem[0].Value : val;
            result.push(tempValue);
          });
          value = result.join(';');
        }
        // 是否有大时间跨度或大聚合查询展示优化
        if (i.Location === 'AggregateQuery') {
          value = value === 0 ? t('是') : t('否');
        }
        return <div style={{ overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }} title={value} >
					{value}
				</div>;
      },
    }));
    // 增加 ‘已加入架构图’
    result.push({
      key: 'joinMap',
      header: t('已加入架构图'),
      width: 120,
      render: ins => <div>
				{ins.NodeUuid ?  <Text theme="success">{t('已加入')}</Text> : <Text theme="danger">{t('未加入')}</Text>}
			</div>,
    });

    setColumnData(result);
  };

  useEffect(() => {
    getColumns();
  }, [product, records, instancePolicy]);

  // 暴露回调函数给父组件
  useImperativeHandle(ref, () => ({
    updateData: () => {
      getProductInstances({ pageIndex: 1, pageSize });
    },
  }));


  return (
		<div key={product}>
			<section>
				{
					<Row style={{ marginTop: 10 }}>
						<Col>
							<Card>
								<Table
									verticalTop
									records={records}
									recordKey="InstanceId"
									columns={columnData || []}
									addons={[
									  pageable({
									    recordCount: total,
									    pageIndex,
									    onPagingChange: query => getProductInstances(query),
									  }),
									  autotip({
									    isLoading: loading,
									  }),
									  scrollable({ maxHeight: 480 }),
									]}
									topTip={
										records?.length === 0 && <StatusTip status={'empty'} />
									}
								/>
							</Card>
						</Col>
					</Row>
				}
			</section>
		</div>
  );
}

export default forwardRef(InstanceTable);
