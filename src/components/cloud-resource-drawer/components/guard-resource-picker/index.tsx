/* eslint-disable no-nested-ternary */
import React, { useRef, useState, useEffect } from 'react';
import { t } from '@tea/app/i18n';
import { message } from 'tdesign-react';
import {
  Button,
} from '@tencent/tea-component';
import store from '@src/origin-store/store';
import uuid from 'react-uuid';
import ProductCollapse from '../product-collapse';
import { DescribeArchGuardAddedInstance, DescribeArchGuardInstanceSync, DescribeGuardApprovalProgress } from '@src/service/api/baseInfo';
import { guardInfo } from '@src/utils/caching';
import { useDispatch } from 'react-redux';
import { useCommonSelector, changeCommonData } from '@src/store/app-common';
import { setGuardUpdateFlagAction } from '@src/origin-store/guardAction';
import './index.less';
// import EscortTool from '@src/pages/escortTool';
// import { nanoid } from 'nanoid';

/** Root
 * @param props
 * @returns React.ReactElement
 */
export default function GuardResourcePicker(): React.ReactElement {
  const { archProductInfo } = useCommonSelector();
  const { graphApi, drawerUpdateFlag } = store.getState().guard;
  const [isloading, setIsloading] = useState(false);
  const [approvalId, setApprovalId] = useState(0);
  const ProductCollapseRef = useRef(null);
  const queryParams = new URLSearchParams(location.search);
  const appId = +queryParams.get('appid');

  	// 当前护航单数据
  const {
    archInfo,
  } = graphApi;
  const dispatch = useDispatch();

  useEffect(() => {
    flashCloudResource();
    getApprovalInfo();
  }, []);

  	// 查询护航单产品情况
  async function getArchProductInfo() {
    try {
      const res: any = await DescribeArchGuardAddedInstance({
        GuardId: guardInfo.GuardId,
        AppId: appId,
      });
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        return;
      }
      // 更新角标
      // graphApi.setSlotComponent(<EscortTool uuid={nanoid()} />);
      store.dispatch(setGuardUpdateFlagAction(!drawerUpdateFlag));
      dispatch(changeCommonData({
        archProductInfo: res || {},
      }));
      ProductCollapseRef?.current?.updateData();
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
    }
  }

  const flashCloudResource = () => {
    setIsloading(true);
    DescribeArchGuardInstanceSync({
      AppId: appId,
      MapId: archInfo?.archId,
      TemplateId: uuid(),
    })
      .then(() => {
        getArchProductInfo();
        setIsloading(false);
      })
      .catch((err) => {
        const msg = err.msg || err.toString() || t('未知错误');
        message.error({ content: msg });
        setIsloading(false);
      });
  };

  // 查询售后确认架构图审批单
  async function getApprovalInfo() {
    try {
      const params = {
        Filters: [
          { Name: 'approval_type', Values: ['7'] },
          { Name: 'status', Values: ['0'] },
          { Name: 'guard_id', Values: [guardInfo.GuardId.toString()] },
        ],
        Offset: 0,
        Limit: 10,
        AppId: guardInfo.MainAppId,
      };
      const res: any = await DescribeGuardApprovalProgress(params);
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        return;
      }
      // 记录售后确认架构图审批单Id
      setApprovalId(res?.Progress?.[0].Id || 0);
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
    }
  }

  return (
    <>
      <div className='resource-box-opt'>
        <Button type="primary" onClick={() => graphApi.switchEdit('filterMySrc', { filterId: String(guardInfo.GuardId) })}>
          {t('绘图模式')}
        </Button>
        <Button
          type="weak"
          style={{ marginLeft: 10 }}
          loading={isloading}
          onClick={() => {
            flashCloudResource();
          }}
        >
          {t('刷新')}
        </Button>
        <Button
          type="weak"
          style={{ marginLeft: 10 }}
          onClick={() => {
            if (approvalId) {
              location.href = `/advisor/approval/sales-confirm/${guardInfo.GuardId}/${approvalId}?reSelect=true`;
            }
          }}
        >
          {t('重选架构图')}
        </Button>
      </div>
      <div className='resource-box-list'>
        <ProductCollapse
          ref={ProductCollapseRef}
          currentGuard={guardInfo}
          productAddedInstance={archProductInfo?.ProductAddedInstanceCount || {}}
        />
      </div>
    </>
  );
}
