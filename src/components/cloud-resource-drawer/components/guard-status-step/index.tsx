
/* eslint-disable no-nested-ternary */
import React, { useState } from 'react';
import { t, Trans, Slot } from '@tea/app/i18n';
import {
  Bubble,
  Text,
  Icon,
  Stepper,
} from '@tencent/tea-component';
import store from '@src/origin-store/store';
import { guardInfo } from '@src/utils/caching';
import ChevronDownIcon from '@src/assets/svg/chevron-down.svg';
import ChevronUpIcon from '@src/assets/svg/chevron-up.svg';
import GuardStatu from '@src/components/GuardStatu';
import GuardResourcePicker from '../guard-resource-picker';
import { useCommonSelector } from '@src/store/app-common';
import './index.less';

/** Root
 * @param props
 * @returns React.ReactElement
 */
export default function GuardStatusStep(): React.ReactElement {
  const { graphApi } = store.getState().guard;
  const { archProductInfo } = useCommonSelector();
  // 提示文案
  const addInstanceTip = archProductInfo?.HasAddedInstance
    ? t('护航架构图需添加护航资源，请在“绘图模式”完成资源绑定后推进流程。以下资源需要添加至架构图：')
    : t('请确认护航架构图信息，若需调整可进入“绘图模式”，若需更换架构图请选择“重选架构图”');

  const {
    env,
  } = graphApi;
  const isISA = env === 'ISA';
  const [isFold, setIsFold] = useState(true);
  const { Handler, IsConfirm: afterSaleIsConfirm } = guardInfo.Approvals?.AfterSaleConfirmStatus || {};

  const antoolPlatformInfo = guardInfo?.CloudGuardBaseInfoOtherPlatform?.find(i => i.Platform === 'Antool');
  const URL = `https://${env === 'production' ? '' : 'test-'}antool.woa.com/fe-base/antool-page/visit/info?taskId=${antoolPlatformInfo?.SubTaskId}&processInstanceId=${antoolPlatformInfo?.PlatformUniqueId}`;
  const afterSaleConfirmCheck = [
    Handler,
    afterSaleIsConfirm ? t(' - 已审批') : t(' - 审批中'),
  ];

  const approvalStep = [
    {
      id: 'step1',
      label: <div className="guard-step-status" onClick={() => setIsFold(!isFold)}>
        <div className='guard-step-one'>
          <span>{t('护航项目审批信息')}</span>
          {
            isFold
            && <div className='guard-status-loading'>
              <Icon type="loading" />
              <span>{t('售后确认架构图')}</span>
              <Bubble
                arrowPointAtCenter
                placement="auto"
                trigger="hover"
                content={afterSaleConfirmCheck}
              >
                <Icon type="info" />
              </Bubble>
            </div>
          }
        </div>
        <div>
          <img src={isFold ? ChevronDownIcon : ChevronUpIcon} alt="down" />
        </div>
      </div>,
      detail: <>
        {
          isFold
            ? <></>
            : <>
            {
              isISA
              && antoolPlatformInfo?.SubTaskId
              && <Text className='antoolTask'  theme="text">
                <Trans>
                  已生成antool任务单
                  (<a href={URL} target="_blank" rel="noreferrer" className='link'><Slot content={antoolPlatformInfo?.SubTaskId} /></a>)
                </Trans>
                </Text>
              }
              <GuardStatu item={guardInfo} />
            </>
        }
      </>,
    },
    {
      id: 'step2',
      label: addInstanceTip,
      detail: <GuardResourcePicker />,
    },
    {
      id: 'step3',
      label: '',
    },
  ];

  return (
    <Stepper nowrap className='vertical-steps' type="process-vertical-dot" steps={approvalStep} />
  );
}
