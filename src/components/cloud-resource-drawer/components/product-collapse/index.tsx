import React, { useState, useEffect, useImperativeHandle, forwardRef, useRef } from 'react';
import { message, Collapse, Text } from '@tencent/tea-component';
import { DescribeProductConfigList } from '@src/service/api/baseInfo';
import InstanceTable from '../instance-table';
import { t } from '@tea/app/i18n';
import './index.less';
interface Props {
  currentGuard?: any;
  productAddedInstance?: any
}

function ProductCollapse({ currentGuard, productAddedInstance = {} }: Props, ref) {
  const InstanceTableRefList = useRef([]);
  // 资源信息收集
  const [productDict, setProductDict] = useState({});                                 // 产品中文名称
  const [activeIds, setActiveIds] = useState<Array<string>>([]);                      // 展开的云产品
  // 已经展开过的产品
  const [requestedProducts, setRequestedProducts] = useState([]);
  // 实例展示列
  const [filterPolicy, setFilterPolicy] = useState([]);
  const queryParams = new URLSearchParams(location.search);
  const appId = +queryParams.get('appid');

  useEffect(() => {
    // 默认展开还有实例未加入的产品
    const product = [];
    Object.entries(productAddedInstance).map(([key, value]) => {
      if (value) {
        product.push(key);
      }
    });
    setActiveIds(product);
  }, [productAddedInstance]);

  // 获取云产品清单
  const getProductsGroupsInfo = async () => {
    try {
      const res: any = await DescribeProductConfigList({
        AppId: appId,
        Env: 'all',
        TaskType: 'guardTaskType',
      });
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        return;
      }
      const productDictOption = JSON.parse(res.ProductDict);
      setProductDict(productDictOption);
      setFilterPolicy(res.FilterPolicy || []);
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
    }
  };

  useEffect(() => {
    setRequestedProducts(Array.from(new Set([...requestedProducts, ...activeIds])));
  }, [activeIds]);


  // init
  useEffect(() => {
    getProductsGroupsInfo();
  }, []);

  // 暴露回调函数给父组件
  useImperativeHandle(ref, () => ({
    updateData: () => {
      requestedProducts.forEach((product, index) => {
        setTimeout(() => {
          InstanceTableRefList?.current?.[product]?.updateData();
        }, 60 * index);
      });
    },
  }));

  return (
		<div style={{ marginTop: 10 }}>
			<Collapse activeIds={activeIds} onActive={(v) => {
			  setActiveIds(v);
			}} destroyInactivePanel={false}>
				{
					Object.entries(productAddedInstance).map(([key, value]) => {
					  const tipStr = value
					    ? <Text key={key} style={{ marginLeft: 5 }} theme="danger">{ t('以下部分资源未加入架构图，请直接提交“下一步”，资源仍在护航范围。') }</Text>
					    : <Text key={key} style={{ marginLeft: 5 }} theme="success">{t('所有护航资源已加入架构图')}</Text>;
					  return <div key={key}>
							<Collapse.Panel
								style={{ marginTop: 10 }}
								key={key}
								id={key}
								title={[productDict[key], tipStr]}
							>
								{/* 场景一：授权账号、接入云顾问的产品 */}
								{requestedProducts.includes(key)
									&& <InstanceTable
										ref={ref => InstanceTableRefList.current[key] = ref}
										product={key}
										instancePolicy={filterPolicy.filter(i => i.Product === key)}
										key={key}
										appid={appId}
										guardId={currentGuard.GuardId}
									/>
								}
							</Collapse.Panel>
						</div>;
					})
				}
			</Collapse>
		</div>
  );
}

export default forwardRef(ProductCollapse);

