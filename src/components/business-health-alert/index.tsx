import React, { useEffect, useState } from 'react';
import { t } from '@tea/app/i18n';
import { Collapse } from 'tdesign-react';
import './index.less';
import { Text, Table, StatusTip } from '@tencent/tea-component';
import { DescribeArchGuardNodeHealthAlarms } from '@src/service/api/baseInfo';
import store from '@src/origin-store/store';
import moment from 'moment';
const { Panel } = Collapse;
const { pageable, scrollable, expandable } = Table.addons;;

/** Root
 * @param props
 * @returns React.ReactElement
 */

interface ChangeProps {
  timeRange: Array<string>;
  instanceList: Array<string>
}

export default function BusinessHealthAlert({ timeRange = [], instanceList = [] }: ChangeProps): React.ReactElement {
  const { graphApi, currNode } = store.getState().guard;

  // 播报列
  const columns = [
    {
      key: 'AlarmDesc',
      header: t('异常描述'),
      render: item => <Text theme="primary" overflow tooltip>{item.AlarmDesc}</Text>,
    },
    {
      key: 'InstanceId',
      header: t('资源实例'),
    },
    {
      key: 'AlarmTime',
      header: t('异常时间'),
    },
  ];

  // 播报列表
  const [businessHealthList, setBusinessHealthList] = useState([]);
  // 列表查询接口常规参数
  const [offset, setOffset] = useState<number>(0);
  const [limit, setLimit] = useState<number>(20);
  const [loading, setLoading] = useState<boolean>(false);
  const [total, setTotal] = useState<number>(0);
  const [expandedKeys, setExpandedKeys] = useState([]);

  useEffect(() => {
    if (currNode.key && timeRange.length && instanceList.length) {
      getBroadcastList(offset, limit);
    }
  }, [currNode.key, timeRange, instanceList]);

  // 查询播报列表
  function getBroadcastList(Offset?, Limit?) {
    setLoading(true);
    const params = {
      ArchId: graphApi?.archInfo?.archId,
      NodeUuid: currNode.key,
      InstanceIdList: instanceList,
      StartTime: moment(timeRange[0]).format('YYYY-MM-DD HH:mm:ss'),
      EndTime: moment(timeRange[1]).format('YYYY-MM-DD HH:mm:ss'),
      Offset,
      Limit,
      IsAllInsAlarms: false,
    };
    DescribeArchGuardNodeHealthAlarms(params)
      .then((res: any) => {
        setTotal(res?.TotalCount || 0);
        setBusinessHealthList(res?.InstanceAlarmInfoList || []);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  }

  return (total ? <div className='businessHealthAlertWrap'>
    <Collapse expandIconPlacement='right' defaultExpandAll>
         {<Panel header={
            <div className='report-title'>
              {t('当前时间周期')}
              <Text className='statusText' theme='danger'>{t('{{total}} 项业务健康告警', { total })}</Text>
            </div>
          }>
            <Table
              bordered={true}
              records={businessHealthList}
              recordKey="InstanceId"
              columns={columns}
              topTip={
                (loading || businessHealthList.length === 0) && (
                  <StatusTip status={loading ? 'loading' : 'empty'} />
                )
              }
              addons={[
                pageable({
                  recordCount: total,
                  pageSize: limit,
                  pageIndex: offset / limit + 1,
                  onPagingChange: (query) => {
                    if (loading) {
                      return;
                    }
                    setOffset((query.pageIndex - 1) * query.pageSize);
                    setLimit(query.pageSize);
                    getBroadcastList((query.pageIndex - 1) * query.pageSize, query.pageSize);
                  },
                }),
                scrollable({ maxHeight: 530 }),
                expandable({
                  rowExpand: true,
                  expandedKeys,
                  onExpandedKeysChange: (keys, { event }) => {
                    event.stopPropagation();
                    setExpandedKeys(keys);
                  },
                  render(record) {
                    return <>{record?.AlarmDetail?.split('\n').map((item, i) => <div key={i}>{item}</div>)}</>;
                  },
                }),
              ]}
            />
          </Panel>}
    </Collapse>
    </div> : <></>);
}
