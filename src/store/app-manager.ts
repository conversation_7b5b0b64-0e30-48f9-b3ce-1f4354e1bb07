import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { useSelector } from 'react-redux';

interface IAppManagerData {
  archId?: string | undefined; // 当前架构图 id
}

const initialState: () => IAppManagerData = () => ({
  archId: undefined,
});

export const appSlice = createSlice({
  name: 'appManager',
  initialState: initialState(),
  reducers: {
    changeArchData: (state, action: PayloadAction<IAppManagerData>) => {
      // 可以在createSlice 和 createReducer 中编写“突变”（mutation）逻辑，
      // 因为它们在内部使用了 Immer！如果在没有 Immer 的 reducer 中编写“突变”逻辑，将会引发错误！
      Object.assign(state, action.payload);
    },
    resetArchData: (state) => {
      Object.assign(state, initialState());
    },
  },
});

export const { changeArchData, resetArchData } = appSlice.actions;

// 暴露出 appGuard 切片数据的 selector
export const useArchSelector:
() => IAppManagerData = () => useSelector((state: any) => state.appManager);

export default appSlice.reducer;
