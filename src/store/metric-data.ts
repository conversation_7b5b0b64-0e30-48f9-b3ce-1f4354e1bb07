import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { useSelector } from 'react-redux';

interface MetricItem {
  id: string;
  productName: string;
  metricInfo: {
    MetricName: string;
    [key: string]: any;
  };
}

interface ContentTab {
  id: string;
  label: string;
}

interface ISubscribeItem {
  TemplateId: string;
  TemplateName: string;
  Status: number;
  UpdateTime: string;
}

interface IMetricData {
  contentTabs?: ContentTab[];
  tabsDataMap?: Record<string, Array<{
    MetricName: string;
    [key: string]: any;
  }>>;
  list?: MetricItem[];
  mySubscribeList?: ISubscribeItem[];
  monitorTabs?: ContentTab[];
  monitorTabsDataMap?: Record<string, Array<{
    MetricName: string;
    [key: string]: any;
  }>>;
  monitorList?: MetricItem[];
}

const initialState: () => IMetricData = () => ({
  contentTabs: [],
  tabsDataMap: {},
  list: [],
  mySubscribeList: [],
  // 监控产品列表
  monitorTabs: [],
  // 监控指标对象
  monitorTabsDataMap: {},
  monitorList: [],
});

export const metricSlice = createSlice({
  name: 'metricData',
  initialState: initialState(),
  reducers: {
    changeMetricData: (state, action: PayloadAction<IMetricData>) => {
      Object.assign(state, action.payload);
    },
    resetMetricData: (state) => {
      Object.assign(state, initialState());
    },
  },
});

export const { changeMetricData, resetMetricData } = metricSlice.actions;

// Selector hook
export const useMetricSelector: () => IMetricData = () => useSelector((state: any) => state.metricData);

export default metricSlice.reducer;
