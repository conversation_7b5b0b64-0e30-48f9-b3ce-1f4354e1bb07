import { isPlainObject } from 'lodash';
import useSWR from 'swr';

/**
 * @description 判断data是否是一个不完整的对象
 * @param data object
 * @returns boolean
 */
const unCompleteInspection = data =>
// 判断data是否是一个纯对象
  // eslint-disable-next-line implicit-arrow-linebreak
  isPlainObject(data)
	// 判断data中的key的数量和value中不为空的元素的数量是否相等
	&& Object.keys(data).length !== Object.values(data).filter(v => v !== undefined && v !== null && v !== '').length;

/**
 * @description fetchData - 一个使用 SWR hook 的 fetcher，具有根据params的变化重新 fetch 的功能
 * @param {Object} obj
 * @param {string} obj.name - api 的名称
 * @param {Object} [obj.params={}] - api 的参数
 * @param {Function} obj.fetch - api 的 fetcher
 * @param {boolean} [obj.cancel=false] - 是否取消当前的 fetch
 * @param {Object} [obj.config={}] - SWR 的配置
 * @returns {Object} - 一个对象，包含 result, error, isLoading, reload 四个字段
 * @example
 * const { result, error, isLoading, reload } = fetchData({
 *   name: 'user',
 *   params: { id: 1 },
 *   fetch: (params) => request.get('/api/user', { params }),
 * });
 */
const fetchData = ({
  name,
  params = {},
  fetch,
  cancel = false,
  config = {},
  key,
}: {
  name: string;
  params?: Object;
  fetch: Function;
  cancel?: boolean;
  config?: Object;
  key?: string;
}) => {
  const remove = unCompleteInspection(params) || cancel;

  const { data, error, mutate, isLoading, isValidating } = useSWR(
    remove ? null : key ?? [name, params], // `/api/${name}&params=${JSON.stringify(params || {})}`
    () => (params ? fetch(params) : fetch()),
    { revalidateOnFocus: false, dedupingInterval: 60000/* 缓存 1 分钟 */, ...config }
  );
  return {
    result: data,
    error,
    isLoading: isLoading || isValidating,
    reload: mutate,
  };
  // mutate(); // 重新验证当前缓存键的数据
  // mutate(newData); // 更新缓存数据，并重新验证
  // mutate(newData, false); // 仅更新缓存数据，不重新验证
};
export default fetchData;
