import axiosInstance from './axios';
import { archInfo } from '@src/utils/caching';
import { getLanguageParam, getSearchParam } from '@src/utils/common';
import { app } from '@tea/app';

const COMMON_NO_AUTH_CODES = [
  'AuthFailure.UnauthorizedOperation',
  'UnauthorizedOperation',
];

// 运营端-租户端接口映射，租户端和运营端接口名不一致需要在此处配置
const actionMap = {
  GetAccountInfoByFields: 'GetAccountInfoByFieldsForGuard',
  DescribeTags: 'DescribeTagsForArchGuard',
};

// 运营端需要携带AppId、Uin的接口名称
const appIdUinActionList = [
  'DescribeGuardSubscribeChannel',
  'DescribeGuideQuestion',
  'DescribeGuardAISubscribeMetricInfo',
  'SaveArchDiagram',
  'SaveBroadcastTemplate',
  'DescribeBroadcastMessageHistory',
  'DisableBroadcast',
  'DescribeSubscribeHistoryTemplateInfo',
  'SaveGraphProductAndMetric',
];


// 兼容运营端和租户端接口
const request = (action, data, url = 'interface', options = {}) => {
  // 从url中获取appId
  const AppId = getSearchParam('appid', location);
  const extraParams = {
    AppId,
    Uin: archInfo?.uin,
  };
  // 运营端
  if (archInfo.env === 'ISA') {
    return axiosInstance({
      method: 'post',
      url: `/${url}`,
      data: { ...data, Action: action, ...(appIdUinActionList.includes(action) && extraParams) },
    });
  }
  return new Promise((resolve, reject) => {
    // 兼容租户端和运营端接口名不一致
    const capiAction = actionMap[action] || action;
    app.capi.requestV3(
      {
        serviceType: 'advisor',
        cmd: capiAction,
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
        ...options,
      }
    )
      .then((data) => {
        resolve(data.data.Response);
      })
      .catch(async (err) => {
        if (COMMON_NO_AUTH_CODES.includes(err?.code)) {
          const cam = await app.sdk.use('cam-sdk');
          cam.showBreakModal({ message: err?.data?.message });
        } else {
          app.tips.error(err?.msg);
          reject(err);
        }
      });
  });
};

export default request;
