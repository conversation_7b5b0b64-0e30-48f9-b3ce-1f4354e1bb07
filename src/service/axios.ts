import axios from 'axios';
import { message, Modal } from '@tencent/tea-component';
import { get } from 'lodash';
import { getUrlParamFromLocation } from '@src/utils/common';
import { getStorage, setStorage } from '@src/utils/storage';
import { app } from '@tea/app';
import { t } from '@tea/app/i18n';

// @ts-ignore
const { source, signature, timestamp } = getUrlParamFromLocation(['source', 'signature', 'timestamp', 'env'], location);

const baseURL = `${window.location.origin}/1`;

const { domain } = document;
if (domain.startsWith('isa-intl')) {
  setStorage('site', 'sinapore');
} else {
  setStorage('site', 'china');
}

console.log(t('环境'), process.env.NODE_ENV);
console.log(t('站点'), getStorage('site') || 'china');

const header = {
  'X-Request-Signature': signature,
  'X-Request-Timestamp': timestamp,
  'X-Request-Source': source,
};
// 使用独立的axios实例，防止实例污染
const axiosInstance = axios.create({
  baseURL, // api 的 base_url
  timeout: 30 * 1000, // request timeout  设置请求超时时间
  withCredentials: true, // 是否允许带cookie这些
  headers: source === '' ? header : {},
});

// 请求拦截器

axiosInstance.interceptors.request.use(
  (config) => {
    const appId = location.search?.split('appid')?.[1]?.split('&')?.[0]?.split('=')?.[1];
    config.data.Language = domain.startsWith('isa-intl') ? 'en-US' : 'zh-CN';
    config.data.AppId = Number(appId);
    config.data.OnlyData = true;
    config.data.ShowError = !config?.data?.hideError;
    return config;
  },
  (error) => {
    // 对请求错误做些什么，自己定义
    message.error({ content: t('接口请求拦截出错！') });
    return Promise.reject(error);
  }
);

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response) => {
    // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据
    // 否则的话抛出错误
    let data: any = {};
    const { method } = response.config;
    if (method === 'post') {
      data = JSON.parse(response.config.data);
    } else {
      data = JSON.parse(response.config.params);
    }
    if (response.status === 200) {
      if (data.OnlyData) {
        if (data.isAll) {
          return Promise.resolve(response);
        }
        if (response.data.Response) {
          if (response.data.Response.Error) {
            if (data.ShowError) {
              app.tips.error(response.data.Response.Error.Message);
            }
            return Promise.resolve({});
          }
          return Promise.resolve(response.data.Response);
        }
      } else {
        return Promise.resolve(response);
      }
    } else {
      return Promise.reject(response);
    }
  },
  // 服务器状态码不是2开头的的情况
  // 这里可以跟你们的后台开发人员协商好统一的错误状态码
  // 然后根据返回的状态码进行一些操作，例如登录过期提示，错误提示等等
  // 下面列举几个常见的操作，其他需求可自行扩展
  (error) => {
    const { status } = error.response;
    if (status) {
      switch (status) {
        // 401: 无访问权限
        case 401:
          Modal.error({
            message: t('没有授权访问！'),
            description: t('请到 [权限管理] 申请相关权限。'),
            onClose: () => {
              window.open(`${window.location.origin}/advisor/privilege/apply-editor`);
            },
          });
          break;
          // 404请求不存在
        case 404:
          app.tips.error(t('404 网络请求不存在'));
          break;
        case 500:
          app.tips.error(t('500 服务器错误'));
          break;
          // 其他错误，直接抛出错误提示
        default:
          app.tips.error(get(error, 'response.data.Response.Error.Message'));
      }
      return Promise.reject(error);
    }
  }
);

export default axiosInstance;
