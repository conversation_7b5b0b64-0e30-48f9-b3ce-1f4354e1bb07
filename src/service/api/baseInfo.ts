import request from '../request';

// 查询护航单的售后确认架构图审批任务
export const DescribeGuardApprovalProgress = (data = {}) => request('DescribeGuardApprovalProgress', data);

// 异步同步资源
export const DescribeArchGuardSync = (data = {}) => request('DescribeArchGuardSync', data);

// 根据appId查询用户信息
export const GetAccountInfoByFields = (data = {}) => request('GetAccountInfoByFields', data);

// 根据架构图id查询护航单
export const DescribeOtherPlatformGuardSheet = (data = {}) => request('DescribeOtherPlatformGuardSheet', data);

// 根据架构图id查询资源
export const DescribeArch = (data = {}) => request('DescribeArch', data);

// 查询护航项目
export const DescribeGuardProjects = (data = {}) => request('DescribeGuardProjects', data);

// 获取护航服务
export const DescribeGuardServiceDetails = (data = {}) => request('DescribeGuardServiceDetails', data);

// 查询节点下的实例
export const DescribeArchGuardProductInstances = (data = {}) => request('DescribeArchGuardProductInstances', data);

// 创建护航单
export const CreateGuardSheet = (data = {}) => request('CreateArchGuardSheet', data);

// 编辑护航单
export const UpdateGuardSheet = (data = {}) => request('UpdateGuardSheet', data);

// 获取护航角标
export const DescribeArchNodeGuardInfo = data => request('DescribeArchNodeGuardInfo', data);

// 查询产品标签
export const DescribeTags = (data = {}) => request('DescribeTags', data);

// 获取产品、维度配置信息
export const DescribeProductConfigList = (data = {}) => request('DescribeProductConfigListForGuard', data);

// 查询policy
export const DescribeGuardProductPolicy = (data = {}) => request('DescribeGuardProductPolicy', data);

// 单独保存实例
export const BindArchGuardInstance = (data = {}) => request('BindArchGuardInstance', data);

// 查询节点已加入护航的实例
export const DescribeArchGuardInstance = (data = {}) => request('DescribeArchGuardInstance', data);

// 更改图的协作状态
export const ModifyShareStatus = (data = {}) => request('ModifyShareStatus', data);

// 查看节点监控面板
export const DescribeArchGuardNodeMonitorPanel = (data = {}) => request('DescribeArchGuardNodeMonitorPanel', data);

// 查询护航可修改的信息
export const DescribeGuardBaseConfig = (data = {}) => request('DescribeGuardBaseConfig', data);

// 修改护航实例
export const ModifyGuardInstances = (data = {}) => request('ModifyGuardInstances', data);

// 复制复航单
export const CopyOneArchGuard = (data = {}) => request('CopyOneArchGuard', data);

// 查询驻场护航配置
export const DescribeOnsiteGuardConfig = (data = {}) => request('DescribeOnsiteGuardConfig', data);

// 保存总监
export const UpdateOnsiteGuardApprovalDirector = (data = {}) => request('UpdateOnsiteGuardApprovalDirector', data);

// 查询播报状态
export const DescribeArchNodeBroadcastStatusShow = (data = {}) => request('DescribeArchNodeBroadcastStatusShow', data, 'broadcast');

// 查询业务健康告警
export const DescribeArchGuardNodeHealthAlarms = (data = {}) => request('DescribeArchGuardNodeHealthAlarms', data, 'guard');

// 查询播报列表
export const DescribeArchNodeStatusBroadCastList = (data = {}) => request('DescribeArchNodeStatusBroadCastList', data, 'broadcast');

// 播报记录详情
export const DescribeArchNodeSingleBroadcastRecordDetail = (data = {}) => request('DescribeArchNodeSingleBroadcastRecordDetail', data, 'broadcast');

// 创建antool任务
export const CreateAntoolTask = (data = {}) => request('CreateAntoolTask', data);

// 事件上报
export const ReportGuardEditCost = (data = {}) => request('ReportGuardEditCost', data);

export const DescribeBroadcastResults = (data = {}) => request('DescribeBroadcastResults', data, 'broadcast');

export const DescribeProductList = (data = {}) => request('DescribeProductList', data);

export const DescribeArchGuardAddedInstance = (data = {}) => request('DescribeArchGuardAddedInstance', data);

export const ModifyGuardAfterSaleConfirmStatus = (data = {}) => request('ModifyGuardAfterSaleConfirmStatus', data);

export const DescribeGuardAddedInstance = (data = {}) => request('DescribeGuardAddedInstance', data);

export const DescribeArchGuardInstanceSync = (data = {}) => request('DescribeArchGuardInstanceSync', data);

export const DescribeArchNodeTask = (data = {}) => request('DescribeArchNodeTask', data);

export const DescribeArchNodeTaskResult = (data = {}) => request('DescribeArchNodeTaskResult', data);
export const DescribeGuardApplyAuth = (data = {}) => request('DescribeGuardApplyAuth', data);

// 获取架构图ChatSchema列表
export const DescribeArchChatSchemaList = (data = {}) => request('DescribeArchChatSchemaList', data);

// 保存当前架构图的产品及chatbi指标
export const SaveGraphProductAndMetric = (data = {}) => request('SaveGraphProductAndMetric', data);

