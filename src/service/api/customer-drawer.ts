import request from '../request';

/**
 * @description 护航插件-查询产品监控指标配置
 * @link https://tcloud4api.woa.com/document/product/1343/796028?!preview
 * @param data
 * @property {string} Product - 产品
 * @returns Promise<unknown>
 */
export const DescribeArchGuardProductMetricConfig = (data: {
  Product: string;
}) => request('DescribeArchGuardProductMetricConfig', data);


/**
 * @description 护航插件-查询某个节点下的实例
 * <AUTHOR>
 * @link https://tcloud4api.woa.com/document/product/1343/796031?!preview
 * @param data
 * @property {string} ArchId - 架构图id
 * @property {string} NodeUuid - 节点uuid
 * @property {number} Limit - 一页数量
 * @property {number} Offset - 偏移量
 * @returns Promise<unknown>
 */
export const DescribeArchGuardNodeInstances = (data: {
  ArchId: string;
  NodeUuid: string;
  Limit: number;
  Offset: number;
}) => request('DescribeArchGuardNodeInstances', data);


/**
 * @description 护航插件-查询节点下的监控数据
 * <AUTHOR>
 * @link https://tcloud4api.woa.com/document/product/1343/796033?!preview
 * @param data
 * @property {string} ArchId - 架构图id
 * @property {string} NodeUuid - 节点uuid
 * @property {number} Limit - 一页数量
 * @property {number} Offset - 偏移量
 * @returns Promise<unknown>
 */
export const DescribeArchGuardNodeMetricInfos = (data: {
  ArchId: string;
  NodeUuid: string;
  Metric: string;
  StartTime: string;
  EndTime: string;
  InstanceIdList?: string[];
  IsAllInsMetric?: boolean;
}) => request('DescribeArchGuardNodeMetricInfos', data);
