import request from '../request';
import {
  BroadcastContentParams,
  CombinedData,
  BroadcastOnlineParams,
  GuardSheetParams,
  BaseInfoParams,
  StrategysListParams,
  BroadcastUserParams,
  CombinedDataM,
  BroadcastResourcesParams,
  GuardInstanceParams,
} from '@src/types/broadcast';
/**
 * @description 护航播报-查询播报订阅内容
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const getBroadcastSheet = (data: BroadcastContentParams) => request('DescribeBroadcastSheet', data, 'broadcast');

/**
 * @description 护航播报-查询组合播报内容
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const DescribeCombinedBroadcastConfigs = (data: CombinedData) => request('DescribeCombinedBroadcastConfigs', data, 'broadcast');

/**
 * @description 护航播报-修改播报订阅-状态内容
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const ModifyBroadcastOnline = (data: BroadcastOnlineParams) => request('ModifyBroadcastOnline', data, 'broadcast');

/**
 * @description 护航播报-查询护航单
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const DescribeGuardSheet = (data: GuardSheetParams) => request('DescribeGuardSheet', data);

/**
 * @description 护航播报-一键入群
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const PushGuardChat = (data: {
  GuardId: number,
  AppId: number,
  User: Array<string>,
  Type?: string
}) => request('PushGuardChat', data, 'guard');

/**
 * @description 护航播报-修改播报订阅-规则内容
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const ModifyBroadcastBaseInfo = (data: BaseInfoParams) => request('ModifyBroadcastBaseInfo', data, 'broadcast');

/**
 * @description 护航播报-获取产品信息新接口
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const getDescribeProductList = data => request('DescribeProductList', data);

/**
 * @description 护航播报-获取产品信息新接口
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const getDescribeProductConfigList = data => request('DescribeProductConfigList', data, 'qcloud');

/**
 * @description 护航播报-获取产品信息新接口
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const getDescribeProductConfigListForGuard = data => request('DescribeProductConfigListForGuard', data);

/**
 * @description 护航播报-查询播报策略列表
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const DescribeBroadcastStrategys = (data: StrategysListParams) => request('DescribeBroadcastStrategys', data, 'broadcast');

/**
 * @description 护航播报-修改播报订阅-策略列表内容
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const ModifyBroadcastUserStrategy = (data: BroadcastUserParams) => request('ModifyBroadcastUserStrategy', data, 'broadcast');

/**
 * @description 护航播报-修改播报订阅-策略列表内容
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const ModifyCombinedBroadcastConfigs = (data: CombinedDataM) => request('ModifyCombinedBroadcastConfigs', data, 'broadcast');

/**
 * @description 护航播报-修改播报订阅-资源补充内容
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const ModifyBroadcastResources = (data: BroadcastResourcesParams) => request('ModifyBroadcastResources', data, 'broadcast');

/**
 * @description 护航播报-修改播报订阅-资源补充内容
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const describeBroadcastLimits = (data: {Products: string[]}) => request('DescribeBroadcastLimits', data, 'broadcast');

/**
 * @description 护航播报-修改播报订阅-资源补充内容
 * <AUTHOR>
 * @link
 * @param data
 * @returns Promise<unknown>
 */
export const getGuardProductInstance = (data: GuardInstanceParams) => request('DescribeGuardProductInstance', data, 'broadcast');

