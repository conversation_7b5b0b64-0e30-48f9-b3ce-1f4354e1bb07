export interface Error {
  Message: string;
  Code: number;
}

export interface Threshold {
  Values: number
  Factor: string
  Unit: string
}

export interface GuardParams {
  AppIdOwner?: string,
  SubmittedBy?: string,
  SubmitTime?: string,
  GuardId?: number,
  GuardName?: string,
  Standard?: number,
  CustomerAuthStatus?: number,
  Project?: number,
  StartTime?: string,
  EndTime?: string,
  StartClock?: string,
  EndClock?: string,
  Responser?: string, // TAM负责人
  CreatedBy?: string, // 创建人
  UpdatedBy?: string, // 更新人
  MainAppId?: number,
  CustomerName?: string, // 客户名称
  CustomerContacts?: string, // 客户接口人名字
  CustomerPhone?: string,
  Products?: Array<string>,
  RejectedProducts?: Array<string>,
  Origin?: number,
  Status?: number,
  RelatedAppId?: Array<number>,
  RelatedCustomerNames?: Array<string>,
  ExpectedEnlargeTimes?: number,
  ExpectedEnlargeDays?: number,
  PressureTestPlan?: string,
  LimitStrategy?: string,
  BusinessEmergencyPlan?: string,
  InstanceTemplate?: Array<InstanceItem>,
  ProductTemplate?: Array<ProductTemplateItem>,
  TemplateId?: string,
  OnsiteTime?: Array<string>,
  OnsiteProducts?: Array<string>,
  GrafanaDashboardUrl?: string,
  ProductDesc?: Array<ProductDescItem>,
  Approvals?: ApprovalInfo,
  AppId?: number,
  RiskData?: GuardRiskData,
  ClosedNetworkDemand?: string,
  StatementOfNeeds?: string,
  InstanceTemplateCount?: string
  InstanceImportantTemplateCount?: string,
  GuardServiceStatus?: Array<any>,
  GuardServiceIds?: Array<any>
  GuardService?: Array<any>,
  GuardInfoSupportUpdate?: boolean,
  CronType?: number
  // 对接其他平台
  Platform?: string,
  PlatformUniqueId?: string,
  GuardDashboardExist?: boolean,
  StandardDesc?: any,
  ImportantGuardTime?: Array<string>
  CloudGuardBaseInfoOtherPlatform?: any
  ActualDays?: number;
}


export interface InstanceItem {
  Product: string,
  AppId: number,
  Region: string,
  Zone: string,
  InstanceId: string,
  InstanceName: string,
  InstanceTag: string,
  Policy: Array<policyItem>,
  Extra?: any
}


export interface ProductTemplateItem {
  AppId: number,
  Product: string,
  Regions: Array<string>,
  Policy: Array<ProductPolicyItem>,
}


// 护航任务产品描述
export interface ProductDescItem {
  AppId: number,
  IsAuthorized?: boolean,
  Product: string,
  IsProductSupported?: boolean,
  InstanceIds?: string,
  Comment?: string,
}

// 护航审批信息
export interface ApprovalInfo {
  AfterSalesStatus?: AfterSalesStatus,
  ExpertStatus?: Array<ExpertStatus>,
  ScanResultStatus?: Array<ScanResultStatus>,
}

// 护航风险数量
export interface GuardRiskData {
  HighRiskStrategyCount: number,
  MediumRiskStrategyCount: number,
}

// 容量策略
export interface policyItem {
  MetricName: string,
  CNName: string,
  ENName: string,
  Days: number,
  Value: number | string,
  Unit: string,
  IsRequired: boolean,
  Type: string,
  Desc: string,
  OtherValue?: any
}

// 产品维度护航指标
export interface ProductPolicyItem {
  MetricName: string,
  CNName: string,
  ENName: string,
  Value: string,
  Unit: string,
  IsRequired: boolean,
  Type: string,
  Desc: string,
  FieldType: string,
}

// 护航审批信息：售后
export interface AfterSalesStatus {
  Handler?: string,
  Supporter?: string,   // 派遣人
  IsApproved?: boolean, // 售后审批
  IsConfirm?: boolean,  // 护航负责人审批
  State?: number,
}

// 护航审批信息：专项分配
export interface ExpertStatus {
  Handler?: string,
  IsApproved?: boolean,
  Product?: string,
  ProductName?: string,
}


// 护航审批信息：结果审批
export interface ScanResultStatus {
  Handler?: string,
  IsApproved?: boolean,
  Product?: string,
  ProductName?: string,
}

export interface BroadcastContentParams {
  BroadcastId?: number
  AppId?: number
  GuardId?: number

}


// 组合播报内容查询 request
export interface CombinedData {
  AppId?: number,
  BroadcastId?: number
}

// 修改播报订阅-状态内容 request
export interface BroadcastOnlineParams {
  AppId?: number
  BroadcastId?: number
  Online: number
  Updater: string
}

export interface GuardSheetParams {
  Filters: Array<Filter>,
  Offset: number,
  Limit: number,
  AppId?: number,
  StartTime?: string,
  EndTime?: string,
  Type?: string
}

export interface Filter {
  Name: string,
  Values: Array<string>,
}

export interface Error {
  Message: string;
  Code: number;
}

// 获取播报订阅列表 request
export interface BroadcastListParams {
  Filters?: Array<Filter>
  Offset?: number,
  Limit?: number,
  AppId?: number
}
// 获取播报订阅列表 response
export interface BroadcastListRes {
  Error?: Error;
  TotalCount: number
  RequestId: string
  BroadcastLists: Array<BroadcastList>
}
export interface BroadcastList {
  BroadcastId?: number
  BroadcastName: string
  Scene: string
  GuardId: number
  GuardName: string
  CreateTime: string
  UpdateTime: string
  Creater: string
  Updater: string
  CustomerInfo: Array<CustomerInfo>
}
export interface CustomerInfo {
  AppId: number
  Name: string
}


// 获取播报订阅结果列表 request
export interface BroadcastListResultParams {
  Filters?: Array<Filter>
  Offset?: number,
  Limit?: number,
  AppId?: number
}
// 获取播报订阅结果列表 response
export interface BroadcastListResultRes {
  Error?: Error;
  TotalCount: number
  RequestId: string
  BroadcastResults: Array<BroadcastResult>
}
export interface BroadcastResult {
  TaskId: string
  AppId: number
  Product: string
  StrategyId: number
  CNName: string
  Type: string
  SubType: string
  ActionStatus: number
  IsNormal: number
  Result: string
  SendTime: string
  Detail: string
}


// 查询播报订阅内容 request
export interface BroadcastContentParams {
  BroadcastId?: number
  AppId?: number
}
// 查询播报订阅内容 response
export interface BroadcastContentRes {
  Error?: Error;
  RequestId: string
  BroadcastSheet: BroadcastSheet
}
export interface BroadcastSheet {
  BroadcastId?: number
  BroadcastName: string
  Scene: string
  GuardId: number
  Online: number
  GroupIds: Array<string>
  StartTime: string
  EndTime: string
  Period: number
  OnlyWorkdays: boolean
  BroadcastConfig?: Array<BroadcastConfig>
  Pending?: Array<Pending>
}
export interface BroadcastConfig {
  StrategyId: number
  Threshold: Array<Threshold>
}
export interface Pending {
  Product: string
  Name: string
  ResourceIds: Array<string>
}

// 修改播报订阅-规则内容 request
export interface BaseInfoParams {
  AppId?: number
  BroadcastId?: number
  StartTime: string
  EndTime: string
  Period: number
  OnlyWorkdays: boolean
  Updater: string
}
// 修改播报订阅-规则内容 response
export interface BaseInfoRes {
  Error?: Error
  RequestId: string
  Message?: string
}

// 修改播报订阅-策略列表内容 request
export interface BroadcastUserParams {
  AppId?: number
  BroadcastId?: number
  BroadcastConfig: Array<BroadcastPart>
  Updater: string,
  Enable?: boolean,
  CombinedBroadcastConfig?: any
}
export interface BroadcastPart {
  StrategyId: number
  Threshold: Array<Threshold>
}
// 修改播报订阅-策略列表内容 response
export interface BroadcastUserRes {
  Error?: Error
  RequestId: string
  Message?: string
}


// 修改播报订阅-资源补充内容 request
export interface BroadcastResourcesParams {
  AppId?: number
  BroadcastId?: number
  Resources: Array<Resources>
  Updater: string
}
export interface Resources {
  AppId: number
  Product: string
  Region: string
  ResourceId: string
  ResourceName: string
}
// 修改播报订阅-资源补充内容 response
export interface BroadcastResourcesRes {
  Error?: Error
  RequestId: string
  Message?: string
}


// 修改播报订阅-状态内容 request
export interface BroadcastOnlineParams {
  AppId?: number
  BroadcastId?: number
  Online: number
  Updater: string
}
// 修改播报订阅-状态内容 response
export interface BroadcastOnlineRes {
  Error?: Error
  RequestId: string
  Message?: string
}


// 获取护航单下的产品实例 request
export interface GuardInstanceParams {
  Filters?: Array<Filter>
  Offset?: number,
  Limit?: number,
  AppId?: number
}
// 获取护航单下的产品实例 response
export interface GuardInstanceRes {
  Error?: Error;
  TotalCount: number
  RequestId: string
  Instance: Array<Instance>
}
export interface Instance {
  AppId: number
  Product: string
  Region: string
  Zone: string
  InstanceId: string
  InstanceName: string
  InstanceTag: string
}

export interface StrategysListParams {
  Filters: Array<Filter>
  Offset?: number,
  Limit?: number,
  AppId?: number
}

// 组合播报内容修改 request
export interface CombinedDataM {
  AppId?: number,
  BroadcastId?: number,
  Updater?: string,
  CombinedBroadcastConfig?: any
}

// 获取护航单下的产品实例 request
export interface GuardInstanceParams {
  Filters?: Array<Filter>
  Offset?: number,
  Limit?: number,
  AppId?: number
}
