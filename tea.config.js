/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable @typescript-eslint/no-require-imports */
const path = require('path');
const ESLintPlugin = require('eslint-webpack-plugin');
/**
 * Tea 项目配置
 * @type {import("@tencent/tea-types/config").Configuration}
 */
module.exports = {
  command: {
    dev: {
      port: 8322,
      https: true
    }
  },
  buffet: {
    productId: 1336, // 云顾问产品ID
    zh: [
      {
        site: 1,
        route: 'cloud-escort-sdk',
      },
    ],
  },
  classPrefix: 'tea',
  webpack: (config) => {
    // 修改原规则，对 /svg-component 目录下的svg文件不转换为base64
    const svgRule = config.module.rules.find((rule) => rule.test.toString() === /\.svg$/.toString());
    svgRule.exclude = /svg-component\/.*\.svg$/;

    config.module.rules.push({
      test: /svg-component\/.*\.svg$/,
      use: ['@svgr/webpack'],
    });

    // @ts-ignore
    config.plugins.push(new ESLintPlugin({
      extensions: ['ts', 'tsx', 'js', 'jsx'],
      exclude: ['node_modules', 'i18n'],
    }));
    return config;
  },
};
