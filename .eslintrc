{
  "env": {
    "browser": true,
    "es6": true
  },
  "settings": {
    "react": {
      "version": "detect"
    }
  },
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 6,
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true
    }
  },
  "extends": [
    "@tencent/eslint-config-tencent",
    "@tencent/eslint-config-tencent/ts",
    "plugin:react/recommended",
    "plugin:@tencent/tea-i18n/recommended"
    // "plugin:@typescript-eslint/recommended"
  ],
  "plugins": [
    "@tencent/tea-i18n",
    "import",
    "@typescript-eslint"
  ]
}

